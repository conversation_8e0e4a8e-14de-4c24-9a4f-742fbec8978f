# SuperClaude User Guide 📚

## Quick Start

SuperClaude enhances <PERSON> with **22 specialized commands**, **7 behavioral modes**, and **11 expert personas** to help you work more effectively. Here's how to use them properly.

## 🎯 Understanding When to Use Each Command

### Analysis & Understanding Commands

#### `/sc:analyze` - Deep System Analysis
**When to use:**
- Starting work on an unfamiliar codebase
- Need to understand system architecture
- Looking for patterns and anti-patterns
- Security vulnerability assessment
- Performance bottleneck identification

**NOT for:** Simple single-file questions or when you already know the problem

**Example scenarios:**
```bash
# Understand a new project structure
/sc:analyze @src/ --focus architecture

# Security audit before deployment
/sc:analyze @api/ --focus security

# Find performance issues
/sc:analyze @backend/ --focus performance
```

#### `/sc:troubleshoot` - Problem Investigation
**When to use:**
- You have symptoms but don't know the cause
- Bug investigation
- Test failures
- Integration problems

**NOT for:** General improvements or new features

**Example scenarios:**
```bash
# Debug a failing test
/sc:troubleshoot "tests failing with timeout error"

# Find why API is slow
/sc:troubleshoot "API response time > 2 seconds"
```

#### `/sc:explain` - Get Explanations
**When to use:**
- Learning new concepts
- Understanding error messages
- Need clarification on code

**NOT for:** Fixing problems (use troubleshoot) or documentation (use document)

### Building & Implementation Commands

#### `/sc:build` - Create New Features
**When to use:**
- Creating new components from scratch
- Building new API endpoints
- Prototyping
- Database schema creation

**NOT for:** Fixing existing code (use improve) or understanding code (use analyze)

**Example scenarios:**
```bash
# Build a new React component
/sc:build user-profile --type component --framework react

# Create an API endpoint
/sc:build authentication --type api --framework express
```

#### `/sc:implement` - Turn Requirements into Code
**When to use:**
- You have clear specifications
- Need to implement business logic
- Creating integrations
- Building authentication systems

**NOT for:** Quick fixes or when requirements are unclear

**Example scenarios:**
```bash
# Implement a feature from specs
/sc:implement "user registration with email verification" --type feature

# Build authentication
/sc:implement jwt-authentication --type api
```

### Quality & Improvement Commands

#### `/sc:improve` - Enhance Existing Code
**When to use:**
- Code refactoring needed
- Performance optimization
- Security hardening
- Modernizing legacy code

**NOT for:** Creating new features (use build) or fixing bugs (use troubleshoot)

**Example scenarios:**
```bash
# Improve performance
/sc:improve @api/ --focus performance

# Iterative quality improvements
/sc:improve @src/ --quality --loop --iterations 3
```

#### `/sc:refactor` - Restructure Code
**When to use:**
- Code needs reorganization
- Applying design patterns
- Reducing technical debt
- Improving testability

**NOT for:** Adding features or changing functionality

#### `/sc:optimize` - Performance Tuning
**When to use:**
- Code is too slow
- High memory usage
- Database queries need optimization

**NOT for:** General improvements (use improve) or refactoring

### Testing & Quality Assurance

#### `/sc:test` - Create Tests
**When to use:**
- Need unit tests
- Integration testing
- E2E test scenarios
- Improving test coverage

**NOT for:** Fixing test failures (use troubleshoot)

#### `/sc:review` - Code Review
**When to use:**
- Before merging PRs
- Security review needed
- Best practices check
- Architecture review

**NOT for:** Making changes (use improve)

### Documentation Commands

#### `/sc:document` - Create Documentation
**When to use:**
- Writing README files
- API documentation
- Code comments
- User guides

**NOT for:** Quick explanations (use explain)

## 🎨 Understanding Behavioral Modes

### When Each Mode Activates Automatically

#### Normal Mode (Default)
- Simple tasks
- Direct questions
- Quick edits

#### Brainstorming Mode 🤔
**Auto-triggers on:**
- Vague requests: "I want to build something..."
- Keywords: maybe, explore, ideas, thinking about
- Planning discussions

**Benefits:**
- Asks clarifying questions
- Explores multiple options
- Helps refine requirements

#### Introspection Mode 🧠
**Auto-triggers on:**
- Complex problem solving
- When you ask about Claude's reasoning
- Meta-analysis needs

**Benefits:**
- Shows thinking process
- Transparent reasoning
- Educational insights

#### Task Management Mode 📋
**Auto-triggers on:**
- Tasks with 3+ steps
- Multiple file operations
- Complex projects

**Benefits:**
- Organized execution
- Progress tracking
- Systematic approach

#### Orchestration Mode ⚡
**Auto-triggers on:**
- High resource usage (>75%)
- Multiple tool needs
- Performance constraints

**Benefits:**
- Smart tool selection
- Resource optimization
- Parallel execution

#### Token Efficiency Mode 💾
**Auto-triggers on:**
- Context usage >75%
- Large operations
- When you use --uc flag

**Benefits:**
- 30-50% token reduction
- Symbol-based communication
- Compressed output

#### Business Panel Mode 💼
**Auto-triggers on:**
- Strategic questions
- Business context provided
- ROI analysis needed

**Benefits:**
- Multiple expert perspectives
- Business-technical alignment
- Strategic insights

## 👥 Understanding Personas

Personas activate automatically based on your task:

### Technical Personas
- **architect** 🏗️: System design, architecture decisions
- **frontend** 🎨: UI/UX, components, accessibility
- **backend** ⚙️: APIs, databases, server logic
- **security** 🛡️: Vulnerabilities, authentication, encryption
- **performance** ⚡: Optimization, bottlenecks, speed
- **qa** ✅: Testing, quality, edge cases

### Process Personas
- **analyzer** 🔍: Root cause analysis, investigation
- **refactorer** 🔧: Code quality, technical debt
- **devops** 🚀: Infrastructure, deployment, CI/CD
- **mentor** 📚: Teaching, explanations, learning
- **scribe** ✍️: Documentation, writing

## 🔑 Keyboard Shortcuts

- **Cmd+Shift+S**: Open SuperClaude commands
- **Cmd+Shift+H**: Open help panel
- **Cmd+?**: Toggle quick reference
- **Esc**: Close all dialogs

## 📊 Common Workflows

### 1. Understanding a New Codebase
```bash
1. /sc:analyze @src/ --focus architecture
2. /sc:explain [main-feature]
3. /sc:analyze @src/ --focus dependencies
4. /sc:document architecture
```

### 2. Implementing a New Feature
```bash
1. /sc:design [feature-name]
2. /sc:implement [feature] --type feature
3. /sc:test [feature]
4. /sc:document [feature]
```

### 3. Performance Optimization
```bash
1. /sc:analyze @src/ --focus performance
2. /sc:optimize [bottleneck] --measure
3. /sc:test performance
4. /sc:document optimizations
```

### 4. Bug Investigation
```bash
1. /sc:troubleshoot [symptoms]
2. /sc:debug [specific-area]
3. /sc:improve [buggy-code] --fix
4. /sc:test [fix]
```

### 5. Code Quality Improvement
```bash
1. /sc:review @src/
2. /sc:refactor [module] --pattern
3. /sc:improve @src/ --quality --loop
4. /sc:test
```

## 💡 Pro Tips

### Command Usage
1. **Be specific with paths**: Use `@src/` to target directories
2. **Use focus flags**: `--focus security`, `--focus performance`
3. **Enable iterations**: `--loop --iterations 3` for improvements
4. **Specify frameworks**: `--framework react` for better results

### Mode Selection
1. Modes activate automatically - no need to manually select
2. Override with flags if needed: `--brainstorm`, `--introspect`
3. Token efficiency activates at 75% context usage

### Persona Activation
1. Personas activate based on task context
2. Multiple personas can work together
3. Force activation with `--persona-[name]`

### Wave Orchestration
1. Waves activate automatically for complex tasks
2. Use `--wave` to force wave mode
3. Great for multi-step operations

## ⚠️ Common Mistakes to Avoid

### Using Wrong Commands
❌ Using `/sc:analyze` when you already know the problem
✅ Use `/sc:troubleshoot` for specific issues

❌ Using `/sc:build` to fix existing code
✅ Use `/sc:improve` for enhancements

❌ Using `/sc:explain` for documentation
✅ Use `/sc:document` for creating docs

### Command Specificity
❌ Vague commands: `/sc:improve`
✅ Specific commands: `/sc:improve @api/ --focus performance`

❌ No framework specified: `/sc:build component`
✅ Framework specified: `/sc:build component --framework react`

### Scope Issues
❌ Using on entire codebase when not needed
✅ Target specific directories or files

❌ Not providing enough context
✅ Include error messages, symptoms, requirements

## 🎯 Quick Decision Guide

**"I don't understand this code"** → `/sc:explain` or `/sc:analyze`

**"I need to create something new"** → `/sc:build` or `/sc:implement`

**"This code needs improvement"** → `/sc:improve` or `/sc:refactor`

**"Something is broken"** → `/sc:troubleshoot` or `/sc:debug`

**"I need tests"** → `/sc:test`

**"I need documentation"** → `/sc:document`

**"Is this code good?"** → `/sc:review`

**"This is slow"** → `/sc:optimize`

## 🚀 Getting Started

1. **Open SuperClaude**: Click the sparkles icon or press `Cmd+Shift+S`
2. **Browse commands**: See all 22 commands with descriptions
3. **Check help**: Click help button or press `Cmd+Shift+H`
4. **Quick reference**: Press `Cmd+?` for quick tips
5. **Start simple**: Try `/sc:analyze @src/` to understand your project

## 📚 Additional Resources

- **Help Panel**: Comprehensive guide with examples (Cmd+Shift+H)
- **Quick Reference**: Always-visible tips (Cmd+?)
- **Command Picker**: Browse and search commands (Cmd+Shift+S)
- **Mode Selector**: Manually change modes if needed

Remember: SuperClaude is designed to help you work smarter, not harder. The commands, modes, and personas work together to provide exactly the help you need, when you need it. Start with simple commands and gradually explore more advanced features as you become comfortable.

Happy coding with SuperClaude! 🎉
/**
 * Detailed use cases and help data for SuperClaude commands
 */

import type { EnhancedSuperClaudeCommand, ModeGuide, PersonaGuide, WorkflowGuide } from '@/types/superClaudeHelp';

export const enhancedCommands: Record<string, Partial<EnhancedSuperClaudeCommand>> = {
  analyze: {
    useCases: [
      { title: 'New Codebase Understanding', description: 'When you inherit or join a project and need to understand its structure' },
      { title: 'Architecture Review', description: 'Evaluating the overall system design and patterns' },
      { title: 'Security Audit', description: 'Finding vulnerabilities and security issues' },
      { title: 'Performance Analysis', description: 'Identifying bottlenecks and optimization opportunities' },
      { title: 'Dependency Mapping', description: 'Understanding how modules and components interact' }
    ],
    bestFor: 'Complex codebases that need deep understanding, architectural reviews, or comprehensive audits',
    notFor: 'Simple single-file scripts, quick syntax questions, or when you already know what needs to be done',
    relatedCommands: ['explain', 'troubleshoot', 'improve', 'review'],
    tips: [
      'Use --ultrathink for deep architectural analysis',
      'Add --focus security for security-focused analysis',
      'Wave mode automatically activates for large codebases',
      'Combine with business-panel for strategic insights',
      'Use @specific-directory to focus analysis scope'
    ],
    complexity: 'intermediate',
    commonMistakes: [
      'Using analyze when you already know the problem (use troubleshoot instead)',
      'Not specifying a focus area for large codebases',
      'Using on single files (use explain instead)'
    ],
    successIndicators: [
      'Clear understanding of code structure',
      'Identified patterns and anti-patterns',
      'Actionable improvement recommendations',
      'Comprehensive dependency map'
    ]
  },

  build: {
    useCases: [
      { title: 'New Feature Development', description: 'Creating new components, APIs, or services from scratch' },
      { title: 'Prototype Creation', description: 'Quickly building proof-of-concepts' },
      { title: 'Component Generation', description: 'Creating reusable UI components' },
      { title: 'API Endpoint Creation', description: 'Building RESTful or GraphQL endpoints' },
      { title: 'Database Schema Design', description: 'Creating and implementing database structures' }
    ],
    bestFor: 'Creating new features or components with proper architecture and best practices',
    notFor: 'Fixing existing code (use improve), understanding code (use analyze), or simple edits',
    relatedCommands: ['implement', 'design', 'test'],
    tips: [
      'Specify --framework to use framework-specific patterns',
      'Use --type component for UI components',
      'Wave mode helps with complex multi-file builds',
      'Auto-activates relevant personas (frontend/backend)',
      'Consider using design first for complex features'
    ],
    complexity: 'beginner',
    commonMistakes: [
      'Not specifying the target framework',
      'Using build for modifications (use improve)',
      'Building without design for complex features'
    ],
    successIndicators: [
      'Working code that follows best practices',
      'Proper error handling and validation',
      'Tests included or test-ready',
      'Documentation comments added'
    ]
  },

  implement: {
    useCases: [
      { title: 'Feature Implementation', description: 'Taking requirements and turning them into working code' },
      { title: 'Integration Development', description: 'Connecting systems or services together' },
      { title: 'Business Logic', description: 'Implementing complex business rules and workflows' },
      { title: 'Authentication Systems', description: 'Building secure auth flows' },
      { title: 'Data Processing Pipelines', description: 'Creating data transformation and processing systems' }
    ],
    bestFor: 'Taking specifications or requirements and creating complete, working implementations',
    notFor: 'Quick fixes, understanding existing code, or when requirements are unclear',
    relatedCommands: ['build', 'design', 'test', 'document'],
    tips: [
      'Provide clear requirements for best results',
      'Use --type to specify implementation type',
      'Security persona auto-activates for auth work',
      'Consider design command first for complex features',
      'Wave mode helps manage multi-step implementations'
    ],
    complexity: 'intermediate',
    commonMistakes: [
      'Implementing without clear requirements',
      'Not specifying the implementation type',
      'Skipping design phase for complex features'
    ],
    successIndicators: [
      'Feature works as specified',
      'Handles edge cases properly',
      'Integrates well with existing code',
      'Includes necessary tests'
    ]
  },

  improve: {
    useCases: [
      { title: 'Code Refactoring', description: 'Cleaning up and restructuring existing code' },
      { title: 'Performance Optimization', description: 'Making code run faster or use less resources' },
      { title: 'Security Hardening', description: 'Fixing vulnerabilities and improving security' },
      { title: 'Code Modernization', description: 'Updating legacy code to modern standards' },
      { title: 'Quality Enhancement', description: 'Improving readability, maintainability, and testability' }
    ],
    bestFor: 'Enhancing existing code quality, performance, security, or maintainability',
    notFor: 'Creating new features (use build), fixing specific bugs (use troubleshoot)',
    relatedCommands: ['analyze', 'refactor', 'optimize', 'cleanup'],
    tips: [
      'Use --focus to target specific improvements',
      'Add --loop for iterative improvements',
      'Wave mode great for large-scale improvements',
      'Combine with analyze first for better results',
      'Use --iterations to control improvement cycles'
    ],
    complexity: 'intermediate',
    commonMistakes: [
      'Not specifying improvement focus',
      'Using on code that needs complete rewrite',
      'Improving without understanding first'
    ],
    successIndicators: [
      'Measurable improvement in target area',
      'Code passes all existing tests',
      'Improved metrics (performance, quality)',
      'Better code structure and readability'
    ]
  },

  troubleshoot: {
    useCases: [
      { title: 'Bug Investigation', description: 'Finding the root cause of errors or unexpected behavior' },
      { title: 'Performance Issues', description: 'Diagnosing why code is running slowly' },
      { title: 'Integration Problems', description: 'Fixing issues between different systems' },
      { title: 'Test Failures', description: 'Understanding why tests are failing' },
      { title: 'Production Issues', description: 'Investigating problems in live systems' }
    ],
    bestFor: 'Investigating and fixing specific problems when you have symptoms but not the cause',
    notFor: 'General code review (use analyze), improvements (use improve), or new features',
    relatedCommands: ['analyze', 'debug', 'test', 'explain'],
    tips: [
      'Provide detailed symptoms and error messages',
      'Include steps to reproduce if possible',
      'Analyzer persona auto-activates',
      'Use --think for complex debugging',
      'Consider logs and stack traces'
    ],
    complexity: 'intermediate',
    commonMistakes: [
      'Not providing enough context about the problem',
      'Using for general improvements',
      'Skipping systematic investigation'
    ],
    successIndicators: [
      'Root cause identified',
      'Problem fixed or workaround provided',
      'Prevention strategies suggested',
      'Clear explanation of the issue'
    ]
  },

  document: {
    useCases: [
      { title: 'API Documentation', description: 'Creating comprehensive API docs with examples' },
      { title: 'README Files', description: 'Writing project documentation for users and developers' },
      { title: 'Code Comments', description: 'Adding inline documentation to complex code' },
      { title: 'Architecture Docs', description: 'Documenting system design and decisions' },
      { title: 'User Guides', description: 'Creating end-user documentation and tutorials' }
    ],
    bestFor: 'Creating clear, comprehensive documentation for code, APIs, or systems',
    notFor: 'Code analysis (use analyze), quick explanations (use explain)',
    relatedCommands: ['explain', 'analyze', 'git'],
    tips: [
      'Specify documentation type for best results',
      'Scribe persona auto-activates',
      'Use --lang for multilingual docs',
      'Include examples in your request',
      'Consider audience (developers vs users)'
    ],
    complexity: 'beginner',
    commonMistakes: [
      'Not specifying the target audience',
      'Creating docs without understanding code first',
      'Over-documenting simple code'
    ],
    successIndicators: [
      'Clear and comprehensive documentation',
      'Good examples included',
      'Appropriate for target audience',
      'Well-structured and organized'
    ]
  },

  test: {
    useCases: [
      { title: 'Unit Test Creation', description: 'Writing tests for individual functions or components' },
      { title: 'Integration Testing', description: 'Testing how different parts work together' },
      { title: 'E2E Test Scenarios', description: 'Creating end-to-end user journey tests' },
      { title: 'Test Coverage Improvement', description: 'Adding tests to increase coverage' },
      { title: 'Performance Testing', description: 'Creating benchmarks and load tests' }
    ],
    bestFor: 'Creating comprehensive test suites and improving test coverage',
    notFor: 'Fixing test failures (use troubleshoot), understanding test results (use explain)',
    relatedCommands: ['build', 'implement', 'troubleshoot'],
    tips: [
      'Specify test type (unit, integration, e2e)',
      'QA persona auto-activates',
      'Include edge cases in requirements',
      'Use Playwright MCP for browser tests',
      'Consider test-driven development approach'
    ],
    complexity: 'intermediate',
    commonMistakes: [
      'Not specifying test framework',
      'Writing tests without understanding code',
      'Ignoring edge cases'
    ],
    successIndicators: [
      'High test coverage achieved',
      'Edge cases covered',
      'Tests are maintainable',
      'Clear test descriptions'
    ]
  },

  explain: {
    useCases: [
      { title: 'Code Understanding', description: 'Getting explanations of how code works' },
      { title: 'Concept Learning', description: 'Understanding programming concepts and patterns' },
      { title: 'Error Explanations', description: 'Understanding what error messages mean' },
      { title: 'Algorithm Clarification', description: 'Understanding complex algorithms' },
      { title: 'Framework Features', description: 'Learning about framework capabilities' }
    ],
    bestFor: 'Learning and understanding code, concepts, or error messages',
    notFor: 'Fixing problems (use troubleshoot), creating documentation (use document)',
    relatedCommands: ['analyze', 'document', 'troubleshoot'],
    tips: [
      'Be specific about what needs explaining',
      'Mentor persona auto-activates',
      'Ask for examples if needed',
      'Use --detailed for in-depth explanations',
      'Great for learning new concepts'
    ],
    complexity: 'beginner',
    commonMistakes: [
      'Using for full codebase analysis',
      'Not being specific about what to explain',
      'Using when you need fixes, not explanations'
    ],
    successIndicators: [
      'Clear understanding achieved',
      'Can apply knowledge elsewhere',
      'Questions answered thoroughly',
      'Examples help clarify concepts'
    ]
  },

  refactor: {
    useCases: [
      { title: 'Code Restructuring', description: 'Reorganizing code for better structure' },
      { title: 'Pattern Application', description: 'Applying design patterns to existing code' },
      { title: 'Debt Reduction', description: 'Systematically reducing technical debt' },
      { title: 'Modularity Improvement', description: 'Breaking monolithic code into modules' },
      { title: 'Testability Enhancement', description: 'Making code easier to test' }
    ],
    bestFor: 'Systematic code restructuring without changing functionality',
    notFor: 'Adding features (use build), fixing bugs (use troubleshoot), performance (use optimize)',
    relatedCommands: ['improve', 'cleanup', 'analyze'],
    tips: [
      'Ensure tests exist before refactoring',
      'Refactorer persona auto-activates',
      'Use --pattern to apply specific patterns',
      'Consider impact on other code',
      'Make incremental changes'
    ],
    complexity: 'advanced',
    commonMistakes: [
      'Refactoring without tests',
      'Changing functionality during refactor',
      'Making too many changes at once'
    ],
    successIndicators: [
      'Code structure improved',
      'All tests still pass',
      'Easier to understand and maintain',
      'Technical debt reduced'
    ]
  },

  optimize: {
    useCases: [
      { title: 'Performance Tuning', description: 'Making code run faster' },
      { title: 'Memory Optimization', description: 'Reducing memory usage' },
      { title: 'Database Query Optimization', description: 'Improving database performance' },
      { title: 'Algorithm Optimization', description: 'Using more efficient algorithms' },
      { title: 'Resource Usage', description: 'Reducing CPU, network, or disk usage' }
    ],
    bestFor: 'Improving performance metrics like speed, memory usage, or resource consumption',
    notFor: 'General improvements (use improve), bug fixes (use troubleshoot), refactoring',
    relatedCommands: ['analyze', 'improve', 'profile'],
    tips: [
      'Measure performance before and after',
      'Performance persona auto-activates',
      'Focus on bottlenecks first',
      'Consider trade-offs (speed vs memory)',
      'Use profiling data if available'
    ],
    complexity: 'advanced',
    commonMistakes: [
      'Optimizing without measuring first',
      'Premature optimization',
      'Ignoring readability for minor gains'
    ],
    successIndicators: [
      'Measurable performance improvement',
      'Metrics show optimization success',
      'No functionality broken',
      'Trade-offs documented'
    ]
  },

  debug: {
    useCases: [
      { title: 'Step-by-Step Debugging', description: 'Walking through code execution' },
      { title: 'Variable Inspection', description: 'Checking variable values during execution' },
      { title: 'Breakpoint Analysis', description: 'Understanding code flow at specific points' },
      { title: 'State Debugging', description: 'Tracking state changes over time' },
      { title: 'Async Debugging', description: 'Debugging asynchronous code and promises' }
    ],
    bestFor: 'Detailed step-by-step investigation of code execution and state',
    notFor: 'General troubleshooting (use troubleshoot), code understanding (use explain)',
    relatedCommands: ['troubleshoot', 'analyze', 'explain'],
    tips: [
      'Provide specific areas to debug',
      'Include current behavior vs expected',
      'Analyzer persona helps with investigation',
      'Use with logs and stack traces',
      'Consider async/timing issues'
    ],
    complexity: 'intermediate',
    commonMistakes: [
      'Not providing enough context',
      'Debugging without clear hypothesis',
      'Ignoring async complexities'
    ],
    successIndicators: [
      'Issue pinpointed precisely',
      'Execution flow understood',
      'Root cause identified',
      'Fix strategy clear'
    ]
  },

  review: {
    useCases: [
      { title: 'Code Review', description: 'Comprehensive review of code quality and issues' },
      { title: 'Security Review', description: 'Checking for security vulnerabilities' },
      { title: 'Architecture Review', description: 'Evaluating system design decisions' },
      { title: 'PR Review', description: 'Reviewing pull requests before merge' },
      { title: 'Best Practices Check', description: 'Ensuring code follows standards' }
    ],
    bestFor: 'Getting comprehensive feedback on code quality, security, and best practices',
    notFor: 'Making changes (use improve), understanding code (use explain)',
    relatedCommands: ['analyze', 'improve', 'refactor'],
    tips: [
      'Specify review focus if needed',
      'Multiple personas contribute insights',
      'Great before merging PRs',
      'Use --focus for specific areas',
      'Consider automated review tools too'
    ],
    complexity: 'intermediate',
    commonMistakes: [
      'Reviewing without context',
      'Not specifying review criteria',
      'Ignoring review feedback'
    ],
    successIndicators: [
      'Issues identified and prioritized',
      'Actionable feedback provided',
      'Best practices validated',
      'Improvement suggestions clear'
    ]
  },

  'business-panel': {
    useCases: [
      { title: 'Strategic Analysis', description: 'Getting business perspective on technical decisions' },
      { title: 'ROI Evaluation', description: 'Understanding business value of technical work' },
      { title: 'Stakeholder Communication', description: 'Translating technical concepts for business' },
      { title: 'Risk Assessment', description: 'Business risk analysis of technical choices' },
      { title: 'Innovation Planning', description: 'Aligning technical innovation with business goals' }
    ],
    bestFor: 'Getting strategic business insights on technical decisions and plans',
    notFor: 'Pure technical implementation, code-level details without business impact',
    relatedCommands: ['analyze', 'estimate', 'document'],
    tips: [
      'Provide business context in request',
      'Multiple expert perspectives provided',
      'Great for technical leadership',
      'Combines technical and business views',
      'Use for important decisions'
    ],
    complexity: 'advanced',
    commonMistakes: [
      'Using for pure technical problems',
      'Not providing business context',
      'Ignoring strategic recommendations'
    ],
    successIndicators: [
      'Clear business perspective gained',
      'Strategic alignment achieved',
      'Risks and opportunities identified',
      'Actionable business insights'
    ]
  },

  spawn: {
    useCases: [
      { title: 'Complex Multi-Agent Operations', description: 'When you need parallel processing across multiple domains' },
      { title: 'Task Orchestration', description: 'Coordinating multiple sub-agents for large-scale operations' },
      { title: 'Resource Optimization', description: 'Distributing work efficiently across available resources' },
      { title: 'Parallel Analysis', description: 'Analyzing multiple aspects of a system simultaneously' },
      { title: 'Domain-Specific Processing', description: 'Using specialized agents for different parts of a task' }
    ],
    bestFor: 'Complex operations requiring parallel processing, multi-domain expertise, or resource optimization',
    notFor: 'Simple single-task operations, sequential processing tasks, when overhead exceeds benefits',
    relatedCommands: ['task', 'workflow', 'load', 'analyze'],
    tips: [
      'Use --delegate for automatic sub-agent allocation',
      'Specify --parallel-dirs for directory-based parallelization',
      'Monitor resource usage to prevent overload',
      'Great for large codebases with multiple components',
      'Combine with --focus for targeted multi-agent analysis'
    ],
    complexity: 'advanced',
    commonMistakes: [
      'Using for simple tasks that don\'t need parallelization',
      'Not specifying delegation strategy',
      'Overloading system with too many parallel operations'
    ],
    successIndicators: [
      'Faster completion through parallelization',
      'Comprehensive multi-domain analysis',
      'Efficient resource utilization',
      'Coordinated results from multiple agents'
    ]
  },

  git: {
    useCases: [
      { title: 'Commit Message Generation', description: 'Creating meaningful commit messages from changes' },
      { title: 'Branch Management', description: 'Creating and managing feature branches' },
      { title: 'PR Documentation', description: 'Writing pull request descriptions and documentation' },
      { title: 'Git Workflow Automation', description: 'Automating common git operations' },
      { title: 'Merge Conflict Resolution', description: 'Helping resolve merge conflicts intelligently' }
    ],
    bestFor: 'Version control operations, commit management, PR creation, and git workflow automation',
    notFor: 'Non-git projects, when you need manual control over commits, sensitive operations',
    relatedCommands: ['document', 'review', 'test'],
    tips: [
      'Review changes before committing',
      'Use descriptive branch names',
      'Auto-generates commit messages from changes',
      'Follows conventional commit standards',
      'Great for maintaining clean git history'
    ],
    complexity: 'beginner',
    commonMistakes: [
      'Committing without reviewing changes',
      'Not checking branch before operations',
      'Ignoring git status warnings'
    ],
    successIndicators: [
      'Clean commit history',
      'Meaningful commit messages',
      'Proper branch management',
      'Successful PR creation'
    ]
  },

  cleanup: {
    useCases: [
      { title: 'Technical Debt Reduction', description: 'Systematically reducing accumulated technical debt' },
      { title: 'Code Cleanup', description: 'Removing dead code, unused imports, and redundancies' },
      { title: 'Dependency Updates', description: 'Updating and cleaning up project dependencies' },
      { title: 'File Organization', description: 'Reorganizing project structure for better maintainability' },
      { title: 'Performance Cleanup', description: 'Removing performance bottlenecks and inefficiencies' }
    ],
    bestFor: 'Improving code quality, reducing technical debt, and maintaining clean codebases',
    notFor: 'Adding new features, major refactoring, when stability is critical',
    relatedCommands: ['refactor', 'improve', 'optimize', 'analyze'],
    tips: [
      'Run tests before and after cleanup',
      'Use version control to track changes',
      'Focus on one type of cleanup at a time',
      'Document what was cleaned up',
      'Consider impact on other developers'
    ],
    complexity: 'intermediate',
    commonMistakes: [
      'Cleaning up too much at once',
      'Not testing after cleanup',
      'Removing seemingly unused code that\'s actually needed'
    ],
    successIndicators: [
      'Reduced code complexity',
      'Improved maintainability scores',
      'Fewer linting warnings',
      'Cleaner project structure'
    ]
  },

  estimate: {
    useCases: [
      { title: 'Project Timeline Estimation', description: 'Estimating time required for project completion' },
      { title: 'Resource Planning', description: 'Determining resources needed for implementation' },
      { title: 'Complexity Assessment', description: 'Evaluating technical complexity of proposed features' },
      { title: 'Risk Evaluation', description: 'Identifying and estimating project risks' },
      { title: 'Cost-Benefit Analysis', description: 'Evaluating ROI of technical initiatives' }
    ],
    bestFor: 'Project planning, resource allocation, timeline estimation, and risk assessment',
    notFor: 'Actual implementation, when requirements are unclear, for trivial tasks',
    relatedCommands: ['analyze', 'task', 'workflow', 'business-panel'],
    tips: [
      'Provide detailed requirements for accurate estimates',
      'Consider dependencies and risks',
      'Include buffer time for unknowns',
      'Break down into smaller estimatable tasks',
      'Review historical data for similar projects'
    ],
    complexity: 'intermediate',
    commonMistakes: [
      'Estimating without clear requirements',
      'Ignoring dependencies and risks',
      'Over-optimistic estimates'
    ],
    successIndicators: [
      'Accurate time predictions',
      'Identified risks and mitigation',
      'Clear resource requirements',
      'Realistic project timeline'
    ]
  },

  task: {
    useCases: [
      { title: 'Long-Term Project Management', description: 'Managing multi-session projects over days or weeks' },
      { title: 'Complex Feature Implementation', description: 'Breaking down and tracking large features' },
      { title: 'Sprint Planning', description: 'Organizing work into manageable sprints' },
      { title: 'Progress Tracking', description: 'Monitoring progress across multiple work sessions' },
      { title: 'Team Coordination', description: 'Coordinating tasks across team members' }
    ],
    bestFor: 'Long-running projects, complex features requiring multiple sessions, team coordination',
    notFor: 'Quick fixes, single-session tasks, when you need immediate results',
    relatedCommands: ['spawn', 'workflow', 'estimate', 'load'],
    tips: [
      'Break down into Epic → Story → Task hierarchy',
      'Use persistent memory for cross-session state',
      'Regular checkpoints to save progress',
      'Clear task dependencies and priorities',
      'Wave mode helps with complex task orchestration'
    ],
    complexity: 'intermediate',
    commonMistakes: [
      'Not breaking down tasks small enough',
      'Forgetting to save session state',
      'Poor task prioritization'
    ],
    successIndicators: [
      'Clear project roadmap',
      'Consistent progress tracking',
      'Successful milestone completion',
      'Effective cross-session continuity'
    ]
  },

  workflow: {
    useCases: [
      { title: 'CI/CD Pipeline Design', description: 'Designing continuous integration and deployment workflows' },
      { title: 'Development Process Optimization', description: 'Improving team development workflows' },
      { title: 'Automation Planning', description: 'Planning automation for repetitive tasks' },
      { title: 'Integration Workflows', description: 'Designing system integration workflows' },
      { title: 'Testing Workflows', description: 'Creating comprehensive testing strategies' }
    ],
    bestFor: 'Designing and optimizing development processes, automation, and team workflows',
    notFor: 'Single-task operations, ad-hoc work, when process is already well-defined',
    relatedCommands: ['task', 'spawn', 'design', 'implement'],
    tips: [
      'Map out current workflow first',
      'Identify bottlenecks and inefficiencies',
      'Consider team dynamics and skills',
      'Wave mode great for complex workflows',
      'Include monitoring and feedback loops'
    ],
    complexity: 'advanced',
    commonMistakes: [
      'Over-engineering simple processes',
      'Ignoring team feedback',
      'Not considering edge cases'
    ],
    successIndicators: [
      'Improved team efficiency',
      'Reduced manual work',
      'Clear process documentation',
      'Successful automation implementation'
    ]
  },

  index: {
    useCases: [
      { title: 'Command Discovery', description: 'Finding the right SuperClaude command for your needs' },
      { title: 'Feature Exploration', description: 'Discovering available features and capabilities' },
      { title: 'Learning Commands', description: 'Understanding what each command does' },
      { title: 'Command Comparison', description: 'Comparing similar commands to choose the right one' },
      { title: 'Quick Reference', description: 'Quick lookup of command syntax and options' }
    ],
    bestFor: 'Learning about SuperClaude commands, discovering features, command reference',
    notFor: 'Actual implementation, when you already know what command to use',
    relatedCommands: ['explain', 'load'],
    tips: [
      'Use when unsure which command to use',
      'Great for learning SuperClaude',
      'Provides command examples and use cases',
      'Shows related commands',
      'Interactive command exploration'
    ],
    complexity: 'beginner',
    commonMistakes: [
      'Not exploring available commands before starting',
      'Using wrong command for the task',
      'Missing powerful features'
    ],
    successIndicators: [
      'Found appropriate command',
      'Understood command capabilities',
      'Learned new features',
      'Improved command usage'
    ]
  },

  load: {
    useCases: [
      { title: 'Project Initialization', description: 'Loading project context at session start' },
      { title: 'Context Recovery', description: 'Recovering context after session interruption' },
      { title: 'Codebase Understanding', description: 'Loading and analyzing entire project structure' },
      { title: 'Session Preparation', description: 'Preparing Claude with project-specific knowledge' },
      { title: 'Configuration Loading', description: 'Loading project settings and configurations' }
    ],
    bestFor: 'Starting new sessions, loading project context, preparing for work on specific codebases',
    notFor: 'Mid-session operations, when context is already loaded, for single-file operations',
    relatedCommands: ['analyze', 'task', 'spawn'],
    tips: [
      'Always use at session start for complex projects',
      'Loads project structure and dependencies',
      'Identifies key files and patterns',
      'Sets up appropriate personas',
      'Creates mental model of codebase'
    ],
    complexity: 'beginner',
    commonMistakes: [
      'Starting work without loading context',
      'Not reloading after major changes',
      'Loading unnecessary large contexts'
    ],
    successIndicators: [
      'Complete project understanding',
      'Identified key components',
      'Ready for productive work',
      'Context properly initialized'
    ]
  },

  design: {
    useCases: [
      { title: 'System Architecture Design', description: 'Designing overall system architecture and structure' },
      { title: 'UI/UX Design Planning', description: 'Planning user interfaces and experiences' },
      { title: 'API Design', description: 'Designing RESTful or GraphQL API structures' },
      { title: 'Database Schema Design', description: 'Designing efficient database structures' },
      { title: 'Component Architecture', description: 'Designing modular component systems' }
    ],
    bestFor: 'Planning and designing systems before implementation, architecture decisions',
    notFor: 'Quick implementations, when design is already decided, for trivial features',
    relatedCommands: ['build', 'implement', 'analyze', 'workflow'],
    tips: [
      'Use before implementing complex features',
      'Consider scalability and maintainability',
      'Wave mode great for comprehensive designs',
      'Include error handling in design',
      'Document design decisions and rationale'
    ],
    complexity: 'intermediate',
    commonMistakes: [
      'Implementing without proper design',
      'Over-engineering simple features',
      'Ignoring existing patterns'
    ],
    successIndicators: [
      'Clear architecture documentation',
      'Scalable design patterns',
      'Considered edge cases',
      'Team alignment on approach'
    ]
  }
};

export const modeGuides: ModeGuide[] = [
  {
    mode: 'normal',
    label: 'Normal',
    description: 'Standard Claude behavior without special enhancements',
    whenToUse: [
      'Simple questions and tasks',
      'Quick code edits',
      'When you know exactly what you need',
      'Straightforward implementations'
    ],
    benefits: [
      'Fast responses',
      'No additional complexity',
      'Direct and to the point',
      'Minimal token usage'
    ],
    examples: [
      'Fix a syntax error',
      'Add a simple function',
      'Answer a quick question'
    ],
    autoTriggers: [
      'Default mode',
      'Simple requests'
    ],
    icon: 'Sparkles',
    color: 'gray'
  },
  {
    mode: 'brainstorming',
    label: 'Brainstorming',
    description: 'Collaborative discovery and ideation mode',
    whenToUse: [
      'Planning new features',
      'Exploring solution options',
      'When requirements are unclear',
      'Creative problem solving',
      'Architecture planning'
    ],
    benefits: [
      'Explores multiple approaches',
      'Asks clarifying questions',
      'Helps refine requirements',
      'Generates creative solutions'
    ],
    examples: [
      'Design a new feature',
      'Explore architecture options',
      'Plan a refactoring strategy'
    ],
    autoTriggers: [
      'Vague requests',
      'Keywords: maybe, explore, ideas',
      'Planning discussions'
    ],
    icon: 'Lightbulb',
    color: 'yellow'
  },
  {
    mode: 'introspection',
    label: 'Introspection',
    description: 'Meta-cognitive analysis and reasoning transparency',
    whenToUse: [
      'Complex problem solving',
      'When you want to understand Claude\'s reasoning',
      'Debugging Claude\'s responses',
      'Learning from the process'
    ],
    benefits: [
      'Transparent reasoning process',
      'Shows thinking steps',
      'Identifies assumptions',
      'Educational insights'
    ],
    examples: [
      'Complex algorithm design',
      'Architectural decisions',
      'Trade-off analysis'
    ],
    autoTriggers: [
      'Complex problems',
      'Request for reasoning',
      'Meta-analysis needs'
    ],
    icon: 'Brain',
    color: 'purple'
  },
  {
    mode: 'task-management',
    label: 'Task Management',
    description: 'Structured workflow execution with progress tracking',
    whenToUse: [
      'Multi-step projects',
      'Complex implementations',
      'When organization is crucial',
      'Long-running tasks'
    ],
    benefits: [
      'Organized task execution',
      'Progress tracking',
      'Clear milestones',
      'Systematic approach'
    ],
    examples: [
      'Full feature implementation',
      'Large refactoring project',
      'System migration'
    ],
    autoTriggers: [
      'Tasks with 3+ steps',
      'Complex projects',
      'Multiple file operations'
    ],
    icon: 'Target',
    color: 'blue'
  },
  {
    mode: 'orchestration',
    label: 'Orchestration',
    description: 'Intelligent tool selection and resource optimization',
    whenToUse: [
      'Performance-critical tasks',
      'Resource-constrained environments',
      'Complex tool coordination',
      'Parallel operations needed'
    ],
    benefits: [
      'Optimal tool selection',
      'Resource efficiency',
      'Parallel execution',
      'Smart routing'
    ],
    examples: [
      'Large-scale analysis',
      'Multi-tool operations',
      'Performance optimization'
    ],
    autoTriggers: [
      'High resource usage',
      'Multiple tool needs',
      'Performance constraints'
    ],
    icon: 'Cpu',
    color: 'green'
  },
  {
    mode: 'token-efficiency',
    label: 'Token Efficiency',
    description: 'Compressed communication for maximum efficiency',
    whenToUse: [
      'Long conversations',
      'Token limit approaching',
      'Need concise responses',
      'Batch operations'
    ],
    benefits: [
      '30-50% token reduction',
      'More content per response',
      'Symbol-based communication',
      'Efficient summaries'
    ],
    examples: [
      'Large codebase analysis',
      'Extensive documentation',
      'Multi-file operations'
    ],
    autoTriggers: [
      'Context >75% used',
      '--uc flag',
      'Large operations'
    ],
    icon: 'Zap',
    color: 'orange'
  },
  {
    mode: 'business-panel',
    label: 'Business Panel',
    description: 'Multi-expert strategic business analysis',
    whenToUse: [
      'Strategic decisions',
      'Business alignment needed',
      'ROI analysis',
      'Stakeholder communication'
    ],
    benefits: [
      'Multiple expert perspectives',
      'Business-technical alignment',
      'Strategic insights',
      'Risk analysis'
    ],
    examples: [
      'Technology selection',
      'Architecture decisions',
      'Feature prioritization'
    ],
    autoTriggers: [
      'Business context provided',
      'Strategic questions',
      'Leadership decisions'
    ],
    icon: 'Briefcase',
    color: 'indigo'
  }
];

export const commonWorkflows: WorkflowGuide[] = [
  {
    name: 'Understanding a New Codebase',
    description: 'Systematic approach to learning an unfamiliar project',
    steps: [
      {
        step: 1,
        command: '/sc:analyze @src/ --focus architecture',
        description: 'Get high-level architecture overview',
        expectedOutcome: 'Understanding of project structure and main components'
      },
      {
        step: 2,
        command: '/sc:explain [main-feature]',
        description: 'Deep dive into core features',
        expectedOutcome: 'Detailed understanding of key functionality'
      },
      {
        step: 3,
        command: '/sc:analyze @src/ --focus dependencies',
        description: 'Map out dependencies and relationships',
        expectedOutcome: 'Clear picture of how components interact'
      },
      {
        step: 4,
        command: '/sc:document architecture',
        description: 'Create architecture documentation',
        expectedOutcome: 'Documentation for future reference'
      }
    ],
    category: 'analysis',
    difficulty: 'beginner'
  },
  {
    name: 'Implementing a New Feature',
    description: 'From requirements to working code',
    steps: [
      {
        step: 1,
        command: '/sc:design [feature-name]',
        description: 'Design the feature architecture',
        expectedOutcome: 'Clear design and approach'
      },
      {
        step: 2,
        command: '/sc:implement [feature] --type feature',
        description: 'Implement the feature',
        expectedOutcome: 'Working implementation'
      },
      {
        step: 3,
        command: '/sc:test [feature]',
        description: 'Create tests for the feature',
        expectedOutcome: 'Comprehensive test coverage'
      },
      {
        step: 4,
        command: '/sc:document [feature]',
        description: 'Document the feature',
        expectedOutcome: 'User and developer documentation'
      }
    ],
    category: 'development',
    difficulty: 'intermediate'
  },
  {
    name: 'Performance Optimization',
    description: 'Systematic performance improvement process',
    steps: [
      {
        step: 1,
        command: '/sc:analyze @src/ --focus performance',
        description: 'Identify performance bottlenecks',
        expectedOutcome: 'List of performance issues'
      },
      {
        step: 2,
        command: '/sc:optimize [bottleneck] --measure',
        description: 'Optimize identified bottlenecks',
        expectedOutcome: 'Improved performance metrics'
      },
      {
        step: 3,
        command: '/sc:test performance',
        description: 'Create performance tests',
        expectedOutcome: 'Performance benchmarks'
      },
      {
        step: 4,
        command: '/sc:document optimizations',
        description: 'Document changes and results',
        expectedOutcome: 'Performance improvement report'
      }
    ],
    category: 'quality',
    difficulty: 'advanced'
  },
  {
    name: 'Bug Investigation and Fix',
    description: 'Systematic debugging process',
    steps: [
      {
        step: 1,
        command: '/sc:troubleshoot [symptoms]',
        description: 'Investigate the bug',
        expectedOutcome: 'Root cause identified'
      },
      {
        step: 2,
        command: '/sc:debug [specific-area]',
        description: 'Deep dive into problematic code',
        expectedOutcome: 'Detailed understanding of issue'
      },
      {
        step: 3,
        command: '/sc:improve [buggy-code] --fix',
        description: 'Fix the identified issue',
        expectedOutcome: 'Bug resolved'
      },
      {
        step: 4,
        command: '/sc:test [fix]',
        description: 'Test the fix thoroughly',
        expectedOutcome: 'Verification that bug is fixed'
      }
    ],
    category: 'troubleshooting',
    difficulty: 'intermediate'
  },
  {
    name: 'Code Quality Improvement',
    description: 'Systematic code quality enhancement',
    steps: [
      {
        step: 1,
        command: '/sc:review @src/',
        description: 'Review code quality',
        expectedOutcome: 'Quality issues identified'
      },
      {
        step: 2,
        command: '/sc:refactor [module] --pattern',
        description: 'Apply design patterns',
        expectedOutcome: 'Better code structure'
      },
      {
        step: 3,
        command: '/sc:improve @src/ --quality --loop',
        description: 'Iterative quality improvements',
        expectedOutcome: 'Enhanced code quality'
      },
      {
        step: 4,
        command: '/sc:test',
        description: 'Ensure nothing broke',
        expectedOutcome: 'All tests pass'
      }
    ],
    category: 'quality',
    difficulty: 'intermediate'
  }
];

export const personaGuides: PersonaGuide[] = [
  {
    name: 'architect',
    role: 'Systems Design Specialist',
    expertise: ['System architecture', 'Design patterns', 'Scalability', 'Long-term planning'],
    activatesOn: ['Design tasks', 'Architecture reviews', 'System planning'],
    bestFor: ['Large system design', 'Technology selection', 'Architectural decisions'],
    worksWith: ['frontend', 'backend', 'devops'],
    icon: '🏗️',
    color: 'blue'
  },
  {
    name: 'frontend',
    role: 'UI/UX Developer',
    expertise: ['React/Vue/Angular', 'CSS/Styling', 'Accessibility', 'User experience'],
    activatesOn: ['UI components', 'Frontend tasks', 'UX improvements'],
    bestFor: ['Component creation', 'Responsive design', 'User interactions'],
    worksWith: ['architect', 'qa', 'backend'],
    icon: '🎨',
    color: 'green'
  },
  {
    name: 'backend',
    role: 'Server-Side Developer',
    expertise: ['APIs', 'Databases', 'Security', 'Performance'],
    activatesOn: ['API development', 'Database work', 'Server logic'],
    bestFor: ['API design', 'Data processing', 'System integration'],
    worksWith: ['architect', 'security', 'devops'],
    icon: '⚙️',
    color: 'orange'
  },
  {
    name: 'security',
    role: 'Security Specialist',
    expertise: ['Vulnerability assessment', 'Authentication', 'Encryption', 'Compliance'],
    activatesOn: ['Security reviews', 'Auth implementation', 'Vulnerability fixes'],
    bestFor: ['Security audits', 'Authentication systems', 'Data protection'],
    worksWith: ['backend', 'devops', 'architect'],
    icon: '🛡️',
    color: 'red'
  },
  {
    name: 'performance',
    role: 'Optimization Expert',
    expertise: ['Performance tuning', 'Profiling', 'Caching', 'Resource optimization'],
    activatesOn: ['Performance issues', 'Optimization tasks', 'Bottleneck analysis'],
    bestFor: ['Speed improvements', 'Memory optimization', 'Load optimization'],
    worksWith: ['backend', 'frontend', 'devops'],
    icon: '⚡',
    color: 'yellow'
  },
  {
    name: 'qa',
    role: 'Quality Assurance',
    expertise: ['Testing strategies', 'Test automation', 'Quality metrics', 'Bug prevention'],
    activatesOn: ['Test creation', 'Quality reviews', 'Bug investigation'],
    bestFor: ['Test suite creation', 'Quality validation', 'Coverage improvement'],
    worksWith: ['frontend', 'backend', 'devops'],
    icon: '✅',
    color: 'purple'
  }
];

export const quickTips = [
  'Use /sc:analyze before making large changes to understand the codebase',
  'Wave mode automatically activates for complex tasks - let it work its magic',
  'Combine personas for better results: security + backend for auth systems',
  'Use --focus flag to target specific areas: performance, security, quality',
  'The --loop flag enables iterative improvements until quality targets are met',
  'Business panel mode great for explaining technical decisions to stakeholders',
  'Token efficiency mode saves 30-50% tokens - auto-activates when needed',
  'Use /sc:explain for learning, /sc:analyze for investigation',
  'Always specify framework with --framework flag for better results',
  'Chain commands for workflows: analyze → design → implement → test'
];
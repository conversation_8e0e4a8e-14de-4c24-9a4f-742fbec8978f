/**
 * SuperClaude State Management Store
 * Centralized state management for SuperClaude Framework features
 */

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import type {
  SuperClaudeMode,
  SuperClaudePersona,
  SuperClaudeCommand,
  SuperClaudeWaveStrategy,
  BusinessPanelExpert,
  SuperClaudeSessionState,
  SuperClaudeCommandContext,
  SuperClaudeCommandResult,
} from '@/types/superClaude';
import { superClaudeService } from '@/services/superClaude';

/**
 * Wave execution state
 */
interface WaveExecutionState {
  isExecuting: boolean;
  currentWave: number;
  totalWaves: number;
  currentPhase: 'review' | 'planning' | 'implementation' | 'validation' | 'optimization';
  progress: number; // 0-100
  results: Record<number, any>;
  errors: Record<number, string>;
}

/**
 * Command history entry
 */
interface CommandHistoryEntry {
  timestamp: number;
  command: string;
  context: SuperClaudeCommandContext;
  result?: SuperClaudeCommandResult;
  duration?: number;
  tokensUsed?: number;
}

/**
 * Persona performance metrics
 */
interface PersonaMetrics {
  activationCount: number;
  successRate: number;
  averageTokensUsed: number;
  lastActivated?: number;
  preferredDomains: string[];
}

/**
 * SuperClaude store state
 */
interface SuperClaudeState {
  // Core state
  activeMode: SuperClaudeMode;
  activePersonas: SuperClaudePersona[];
  activeWaveStrategy?: SuperClaudeWaveStrategy;
  currentWave?: number;
  tokenEfficiencyEnabled: boolean;
  businessPanelActive: boolean;
  activeExperts?: BusinessPanelExpert[];
  mcpServersActive: string[];
  
  // Wave execution
  waveExecution?: WaveExecutionState;
  
  // Command history
  commandHistory: CommandHistoryEntry[];
  recentCommands: SuperClaudeCommand[];
  
  // Persona metrics
  personaMetrics: Record<SuperClaudePersona, PersonaMetrics>;
  
  // Mode usage statistics
  modeUsageStats: Record<SuperClaudeMode, number>;
  
  // Token usage tracking
  totalTokensUsed: number;
  tokensSaved: number; // From compression
  
  // Settings
  autoModeDetection: boolean;
  autoPersonaActivation: boolean;
  waveValidationRequired: boolean;
  compressionThreshold: number; // Resource usage percentage to trigger compression
  
  // MCP State
  mcpServersAvailable: string[];
  mcpServerHealth: Record<string, any>;
  
  // Actions
  setMode: (mode: SuperClaudeMode) => void;
  setPersonas: (personas: SuperClaudePersona[]) => void;
  addPersona: (persona: SuperClaudePersona) => void;
  removePersona: (persona: SuperClaudePersona) => void;
  setWaveStrategy: (strategy?: SuperClaudeWaveStrategy) => void;
  setTokenEfficiency: (enabled: boolean) => void;
  setBusinessPanel: (active: boolean, experts?: BusinessPanelExpert[]) => void;
  setMCPServers: (servers: string[]) => void;
  
  // Wave actions
  startWaveExecution: (strategy: SuperClaudeWaveStrategy) => void;
  updateWaveProgress: (wave: number, phase: string, progress: number) => void;
  completeWave: (wave: number, result: any) => void;
  failWave: (wave: number, error: string) => void;
  stopWaveExecution: () => void;
  
  // Command actions
  addCommandToHistory: (entry: CommandHistoryEntry) => void;
  clearCommandHistory: () => void;
  addRecentCommand: (command: SuperClaudeCommand) => void;
  
  // Metrics actions
  updatePersonaMetrics: (persona: SuperClaudePersona, metrics: Partial<PersonaMetrics>) => void;
  incrementModeUsage: (mode: SuperClaudeMode) => void;
  updateTokenUsage: (used: number, saved?: number) => void;
  
  // MCP actions
  activateMCPServer: (serverId: string) => void;
  deactivateMCPServer: (serverId: string) => void;
  updateMCPServerHealth: (serverId: string, health: any) => void;
  
  // Settings actions
  updateSettings: (settings: Partial<{
    autoModeDetection: boolean;
    autoPersonaActivation: boolean;
    waveValidationRequired: boolean;
    compressionThreshold: number;
  }>) => void;
  
  // Utility actions
  resetState: () => void;
  exportState: () => SuperClaudeSessionState;
  importState: (state: SuperClaudeSessionState) => void;
  
  // Intelligence actions
  suggestMode: (prompt: string) => SuperClaudeMode;
  suggestPersonas: (prompt: string) => SuperClaudePersona[];
  optimizeConfiguration: () => void;
}

/**
 * Initial state
 */
const initialState = {
  activeMode: 'normal' as SuperClaudeMode,
  activePersonas: [] as SuperClaudePersona[],
  activeWaveStrategy: undefined,
  currentWave: undefined,
  tokenEfficiencyEnabled: false,
  businessPanelActive: false,
  activeExperts: undefined,
  mcpServersActive: [] as string[],
  mcpServersAvailable: ['context7', 'sequential', 'magic', 'playwright', 'serena', 'morphllm'] as string[],
  mcpServerHealth: {} as Record<string, any>,
  mcpCacheSize: 0,
  waveExecution: undefined,
  commandHistory: [] as CommandHistoryEntry[],
  recentCommands: [] as SuperClaudeCommand[],
  personaMetrics: {} as Record<SuperClaudePersona, PersonaMetrics>,
  modeUsageStats: {
    'normal': 0,
    'brainstorming': 0,
    'introspection': 0,
    'task-management': 0,
    'orchestration': 0,
    'token-efficiency': 0,
    'business-panel': 0,
  } as Record<SuperClaudeMode, number>,
  totalTokensUsed: 0,
  tokensSaved: 0,
  autoModeDetection: true,
  autoPersonaActivation: true,
  waveValidationRequired: true,
  mcpAutoSelection: true,
  mcpCachingEnabled: true,
  compressionThreshold: 75,
};

/**
 * SuperClaude store
 */
export const useSuperClaudeStore = create<SuperClaudeState>()(
  devtools(
    persist(
      immer((set, get) => ({
        ...initialState,
        
        // Mode actions
        setMode: (mode) => set((state) => {
          state.activeMode = mode;
          state.modeUsageStats[mode] = (state.modeUsageStats[mode] || 0) + 1;
          
          // Apply mode-specific settings
          const behavior = superClaudeService.getModesBehavior(mode);
          if (behavior.compressionLevel > 2) {
            state.tokenEfficiencyEnabled = true;
          }
          if (mode === 'business-panel') {
            state.businessPanelActive = true;
          }
        }),
        
        // Persona actions
        setPersonas: (personas) => set((state) => {
          state.activePersonas = personas;
          personas.forEach(persona => {
            if (!state.personaMetrics[persona]) {
              state.personaMetrics[persona] = {
                activationCount: 0,
                successRate: 0,
                averageTokensUsed: 0,
                preferredDomains: [],
              };
            }
            state.personaMetrics[persona].activationCount++;
            state.personaMetrics[persona].lastActivated = Date.now();
          });
        }),
        
        addPersona: (persona) => set((state) => {
          if (!state.activePersonas.includes(persona)) {
            state.activePersonas.push(persona);
            if (!state.personaMetrics[persona]) {
              state.personaMetrics[persona] = {
                activationCount: 0,
                successRate: 0,
                averageTokensUsed: 0,
                preferredDomains: [],
              };
            }
            state.personaMetrics[persona].activationCount++;
            state.personaMetrics[persona].lastActivated = Date.now();
          }
        }),
        
        removePersona: (persona) => set((state) => {
          state.activePersonas = state.activePersonas.filter((p: SuperClaudePersona) => p !== persona);
        }),
        
        // Wave actions
        setWaveStrategy: (strategy) => set((state) => {
          state.activeWaveStrategy = strategy;
          state.currentWave = strategy ? 1 : undefined;
        }),
        
        startWaveExecution: (strategy) => set((state) => {
          state.activeWaveStrategy = strategy;
          state.currentWave = 1;
          state.waveExecution = {
            isExecuting: true,
            currentWave: 1,
            totalWaves: strategy.waves.length,
            currentPhase: strategy.waves[0].phase,
            progress: 0,
            results: {},
            errors: {},
          };
        }),
        
        updateWaveProgress: (wave, phase, progress) => set((state) => {
          if (state.waveExecution) {
            state.waveExecution.currentWave = wave;
            state.waveExecution.currentPhase = phase as any;
            state.waveExecution.progress = progress;
          }
        }),
        
        completeWave: (wave, result) => set((state) => {
          if (state.waveExecution) {
            state.waveExecution.results[wave] = result;
            if (wave < state.waveExecution.totalWaves) {
              state.waveExecution.currentWave = wave + 1;
              state.waveExecution.progress = 0;
              state.currentWave = wave + 1;
            } else {
              state.waveExecution.isExecuting = false;
              state.waveExecution.progress = 100;
            }
          }
        }),
        
        failWave: (wave, error) => set((state) => {
          if (state.waveExecution) {
            state.waveExecution.errors[wave] = error;
            state.waveExecution.isExecuting = false;
          }
        }),
        
        stopWaveExecution: () => set((state) => {
          if (state.waveExecution) {
            state.waveExecution.isExecuting = false;
          }
        }),
        
        // Other actions
        setTokenEfficiency: (enabled) => set((state) => {
          state.tokenEfficiencyEnabled = enabled;
          if (enabled) {
            superClaudeService.enableTokenEfficiency();
          } else {
            superClaudeService.disableTokenEfficiency();
          }
        }),
        
        setBusinessPanel: (active, experts) => set((state) => {
          state.businessPanelActive = active;
          state.activeExperts = experts;
          if (active) {
            superClaudeService.activateBusinessPanel(experts);
          } else {
            superClaudeService.deactivateBusinessPanel();
          }
        }),
        
        setMCPServers: (servers) => set((state) => {
          state.mcpServersActive = servers;
        }),
        
        // Command history
        addCommandToHistory: (entry) => set((state) => {
          state.commandHistory.unshift(entry);
          // Keep only last 100 entries
          if (state.commandHistory.length > 100) {
            state.commandHistory = state.commandHistory.slice(0, 100);
          }
        }),
        
        clearCommandHistory: () => set((state) => {
          state.commandHistory = [];
        }),
        
        addRecentCommand: (command) => set((state) => {
          // Remove if already exists
          state.recentCommands = state.recentCommands.filter((c: SuperClaudeCommand) => c.name !== command.name);
          // Add to front
          state.recentCommands.unshift(command);
          // Keep only last 10
          if (state.recentCommands.length > 10) {
            state.recentCommands = state.recentCommands.slice(0, 10);
          }
        }),
        
        // Metrics
        updatePersonaMetrics: (persona, metrics) => set((state) => {
          if (!state.personaMetrics[persona]) {
            state.personaMetrics[persona] = {
              activationCount: 0,
              successRate: 0,
              averageTokensUsed: 0,
              preferredDomains: [],
            };
          }
          Object.assign(state.personaMetrics[persona], metrics);
        }),
        
        incrementModeUsage: (mode) => set((state) => {
          state.modeUsageStats[mode] = (state.modeUsageStats[mode] || 0) + 1;
        }),
        
        updateTokenUsage: (used, saved = 0) => set((state) => {
          state.totalTokensUsed += used;
          state.tokensSaved += saved;
        }),
        
        // Settings
        updateSettings: (settings) => set((state) => {
          Object.assign(state, settings);
        }),
        
        // Utility
        resetState: () => set(() => initialState),
        
        exportState: () => {
          const state = get();
          return {
            activeMode: state.activeMode,
            activePersonas: state.activePersonas,
            activeWaveStrategy: state.activeWaveStrategy,
            currentWave: state.currentWave,
            tokenEfficiencyEnabled: state.tokenEfficiencyEnabled,
            businessPanelActive: state.businessPanelActive,
            activeExperts: state.activeExperts,
            mcpServersActive: state.mcpServersActive,
            mcpServersAvailable: state.mcpServersAvailable,
            mcpServerHealth: state.mcpServerHealth,
          };
        },
        
        // MCP Server actions
        activateMCPServer: (serverId: string) => set((state) => {
          if (!state.mcpServersActive.includes(serverId)) {
            state.mcpServersActive.push(serverId);
          }
        }),
        
        deactivateMCPServer: (serverId: string) => set((state) => {
          state.mcpServersActive = state.mcpServersActive.filter((id: string) => id !== serverId);
        }),
        
        updateMCPServerHealth: (serverId: string, health: any) => set((state) => {
          state.mcpServerHealth = {
            ...state.mcpServerHealth,
            [serverId]: health
          };
        }),
        
        clearMCPCache: () => set((state) => {
          state.mcpCacheSize = 0;
          // In real implementation, would also clear mcpOrchestrator cache
        }),
        
        toggleMCPAutoSelection: () => set((state) => {
          state.mcpAutoSelection = !state.mcpAutoSelection;
        }),
        
        toggleMCPCaching: () => set((state) => {
          state.mcpCachingEnabled = !state.mcpCachingEnabled;
        }),
        
        importState: (sessionState) => set((state) => {
          Object.assign(state, sessionState);
        }),
        
        // Intelligence
        suggestMode: (prompt) => {
          const context = {
            resourceUsage: get().totalTokensUsed > 50000 ? 80 : 30,
            complexity: prompt.split(' ').length > 50 ? 0.8 : 0.3,
          };
          return superClaudeService.detectModeWithContext(prompt, context);
        },
        
        suggestPersonas: (prompt) => {
          const context = {
            domain: prompt.includes('ui') ? 'frontend' as const : 
                   prompt.includes('api') ? 'backend' as const : undefined,
            operationType: prompt.includes('analyze') ? 'analysis' as const :
                          prompt.includes('create') ? 'creation' as const : undefined,
          };
          return superClaudeService.autoActivatePersonas(prompt, context);
        },
        
        optimizeConfiguration: () => set((state) => {
          // Auto-optimize based on metrics
          const avgTokens = state.totalTokensUsed / Math.max(state.commandHistory.length, 1);
          
          // Enable compression if high token usage
          if (avgTokens > 5000 && !state.tokenEfficiencyEnabled) {
            state.tokenEfficiencyEnabled = true;
          }
          
          // Adjust compression threshold based on usage patterns
          if (state.tokensSaved > state.totalTokensUsed * 0.3) {
            state.compressionThreshold = 65; // Lower threshold since compression is effective
          }
        }),
      })),
      {
        name: 'superclaude-store',
        partialize: (state) => ({
          // Persist only essential state
          activeMode: state.activeMode,
          activePersonas: state.activePersonas,
          tokenEfficiencyEnabled: state.tokenEfficiencyEnabled,
          personaMetrics: state.personaMetrics,
          modeUsageStats: state.modeUsageStats,
          totalTokensUsed: state.totalTokensUsed,
          tokensSaved: state.tokensSaved,
          autoModeDetection: state.autoModeDetection,
          autoPersonaActivation: state.autoPersonaActivation,
          waveValidationRequired: state.waveValidationRequired,
          compressionThreshold: state.compressionThreshold,
          recentCommands: state.recentCommands,
        }),
      }
    ),
    {
      name: 'SuperClaude Store',
    }
  )
);

// Export hooks for specific state slices
export const useSuperClaudeMode = () => useSuperClaudeStore((state) => state.activeMode);
export const useSuperClaudePersonas = () => useSuperClaudeStore((state) => state.activePersonas);
export const useSuperClaudeWave = () => useSuperClaudeStore((state) => state.waveExecution);
export const useSuperClaudeMetrics = () => useSuperClaudeStore((state) => ({
  totalTokensUsed: state.totalTokensUsed,
  tokensSaved: state.tokensSaved,
  personaMetrics: state.personaMetrics,
  modeUsageStats: state.modeUsageStats,
}));
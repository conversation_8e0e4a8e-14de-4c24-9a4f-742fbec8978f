import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { RotateCcw, Play, Users } from "lucide-react";
import { BusinessPanel } from "@/components/BusinessPanel";
import { useBusinessPanel } from "@/hooks/useBusinessPanel";
import { cn } from "@/lib/utils";

interface BusinessPanelViewProps {
  initialContent?: string;
  className?: string;
  onAnalysisComplete?: (analyses: any[]) => void;
}

export const BusinessPanelView: React.FC<BusinessPanelViewProps> = ({
  initialContent = "",
  className,
  onAnalysisComplete,
}) => {
  const [content, setContent] = useState(initialContent);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  
  const {
    experts,
    session,
    analyses,
    loading,
    error,
    selectedExperts,
    mode,
    createSession,
    executeAnalysis,
    setSelectedExperts,
    setMode,
  } = useBusinessPanel({
    initialContent,
  });

  const handleExpertToggle = (expertId: string) => {
    if (selectedExperts.includes(expertId)) {
      setSelectedExperts(selectedExperts.filter(id => id !== expertId));
    } else {
      setSelectedExperts([...selectedExperts, expertId]);
    }
  };

  const handleAnalyze = async () => {
    if (!content.trim()) return;
    
    try {
      setIsAnalyzing(true);
      
      // Create session
      await createSession(content, mode, selectedExperts);
      
      // Execute analysis
      await executeAnalysis();
      
      // Notify parent of completion
      if (onAnalysisComplete) {
        onAnalysisComplete(analyses);
      }
    } catch (err) {
      console.error("Analysis failed:", err);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleReset = () => {
    setContent("");
    setSelectedExperts(experts.slice(0, 3).map(e => e.id));
    setMode("discussion");
  };

  // Set default experts on load
  useEffect(() => {
    if (experts.length > 0 && selectedExperts.length === 0) {
      setSelectedExperts(experts.slice(0, 3).map(e => e.id));
    }
  }, [experts, selectedExperts.length]);

  if (isAnalyzing || (session && analyses.length > 0)) {
    return (
      <div className={cn("h-full flex flex-col", className)}>
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-xl font-semibold flex items-center gap-2">
            <Users className="h-5 w-5" />
            Business Panel Analysis
          </h2>
          <Button
            variant="outline"
            size="sm"
            onClick={handleReset}
            disabled={loading}
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            New Analysis
          </Button>
        </div>
        
        <div className="flex-1 overflow-y-auto p-4">
          <BusinessPanel
            analyses={analyses}
            mode={mode}
            content={content}
          />
        </div>
      </div>
    );
  }

  return (
    <div
      className={cn("h-full flex flex-col", className)}
    >
      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {/* Header */}
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Users className="h-6 w-6" />
            Business Panel
          </h2>
          <p className="text-muted-foreground mt-1">
            Multi-expert business analysis with strategic frameworks
          </p>
        </div>

        {/* Content Input */}
        <Card>
          <CardHeader>
            <CardTitle>Analysis Content</CardTitle>
          </CardHeader>
          <CardContent>
            <Textarea
              placeholder="Enter your business challenge, strategy, or topic for analysis..."
              value={content}
              onChange={(e) => setContent(e.target.value)}
              className="min-h-[120px]"
            />
          </CardContent>
        </Card>

        {/* Mode Selection */}
        <Card>
          <CardHeader>
            <CardTitle>Analysis Mode</CardTitle>
          </CardHeader>
          <CardContent>
            <Select value={mode} onValueChange={setMode}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select analysis mode" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="discussion">
                  <div className="flex items-center gap-2">
                    <span>🗨️</span>
                    <div>
                      <div className="font-medium">Discussion</div>
                      <div className="text-xs text-muted-foreground">Collaborative expert analysis</div>
                    </div>
                  </div>
                </SelectItem>
                <SelectItem value="debate">
                  <div className="flex items-center gap-2">
                    <span>⚔️</span>
                    <div>
                      <div className="font-medium">Debate</div>
                      <div className="text-xs text-muted-foreground">Adversarial expert perspectives</div>
                    </div>
                  </div>
                </SelectItem>
                <SelectItem value="socratic">
                  <div className="flex items-center gap-2">
                    <span>❓</span>
                    <div>
                      <div className="font-medium">Socratic</div>
                      <div className="text-xs text-muted-foreground">Question-driven exploration</div>
                    </div>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </CardContent>
        </Card>

        {/* Expert Selection */}
        <Card>
          <CardHeader>
            <CardTitle>Expert Panel</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {experts.length > 0 ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  {experts.map((expert) => (
                    <div
                      key={expert.id}
                      className={cn(
                        "flex items-start gap-3 p-3 rounded-lg border cursor-pointer transition-colors",
                        selectedExperts.includes(expert.id)
                          ? "border-primary bg-primary/5"
                          : "border-border hover:bg-muted/50"
                      )}
                      onClick={() => handleExpertToggle(expert.id)}
                    >
                      <Switch
                        checked={selectedExperts.includes(expert.id)}
                        onCheckedChange={() => handleExpertToggle(expert.id)}
                        className="mt-0.5"
                      />
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <span className="text-lg">{expert.symbol}</span>
                          <div className="font-medium">{expert.name}</div>
                        </div>
                        <div className="text-sm text-muted-foreground mt-1">
                          {expert.framework}
                        </div>
                        <div className="flex flex-wrap gap-1 mt-2">
                          {expert.focusAreas.map((area: string, i: number) => (
                            <Badge key={i} variant="secondary" className="text-xs">
                              {area}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  {loading ? "Loading experts..." : "No experts available"}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Selected Experts Summary */}
        {selectedExperts.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Selected Experts ({selectedExperts.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {selectedExperts.map((expertId) => {
                  const expert = experts.find(e => e.id === expertId);
                  return expert ? (
                    <Badge
                      key={expert.id}
                      variant="outline"
                      className="flex items-center gap-1"
                    >
                      <span>{expert.symbol}</span>
                      {expert.name}
                    </Badge>
                  ) : null;
                })}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Action Bar */}
      <div className="border-t p-4 bg-background/95 backdrop-blur-md">
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            {selectedExperts.length} experts selected in {mode} mode
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={handleReset}
              disabled={loading}
            >
              Reset
            </Button>
            <Button
              onClick={handleAnalyze}
              disabled={!content.trim() || selectedExperts.length === 0 || loading}
            >
              {loading && <RotateCcw className="h-4 w-4 mr-2 animate-spin" />}
              <Play className="h-4 w-4 mr-2" />
              Analyze
            </Button>
          </div>
        </div>
        
        {error && (
          <div className="mt-3 text-sm text-destructive">
            {error}
          </div>
        )}
      </div>
    </div>
  );
};
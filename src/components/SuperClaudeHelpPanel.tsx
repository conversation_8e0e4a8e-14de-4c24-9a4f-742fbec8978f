import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  HelpCircle,
  X,
  ChevronRight,
  Sparkles,
  Zap,
  Lightbulb,
  Brain,
  Target,
  Cpu,
  Briefcase,
  Shield,
  Code,
  FileSearch,
  TestTube,
  FileText,
  Settings,
} from "lucide-react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { enhancedCommands, modeGuides, commonWorkflows, personaGuides, quickTips } from "@/data/superClaudeUseCases";
import { superClaudeService } from "@/services/superClaude";
import type { SuperClaudeCommand } from "@/types/superClaude";
import { cn } from "@/lib/utils";

interface SuperClaudeHelpPanelProps {
  isOpen: boolean;
  onClose: () => void;
  onCommandSelect?: (command: SuperClaudeCommand) => void;
  className?: string;
}

const categoryIcons: Record<string, React.ReactNode> = {
  analysis: <FileSearch className="h-4 w-4" />,
  development: <Code className="h-4 w-4" />,
  quality: <Shield className="h-4 w-4" />,
  documentation: <FileText className="h-4 w-4" />,
  testing: <TestTube className="h-4 w-4" />,
  meta: <Settings className="h-4 w-4" />,
};

const modeIcons: Record<string, React.ReactNode> = {
  normal: <Sparkles className="h-4 w-4" />,
  brainstorming: <Lightbulb className="h-4 w-4" />,
  introspection: <Brain className="h-4 w-4" />,
  "task-management": <Target className="h-4 w-4" />,
  orchestration: <Cpu className="h-4 w-4" />,
  "token-efficiency": <Zap className="h-4 w-4" />,
  "business-panel": <Briefcase className="h-4 w-4" />,
};

export const SuperClaudeHelpPanel: React.FC<SuperClaudeHelpPanelProps> = ({
  isOpen,
  onClose,
  onCommandSelect,
  className,
}) => {
  const [selectedTab, setSelectedTab] = useState("quickstart");
  const [expandedCommand, setExpandedCommand] = useState<string | null>(null);

  const commands = superClaudeService.getAllCommands();

  const handleCommandClick = (command: SuperClaudeCommand) => {
    if (onCommandSelect) {
      onCommandSelect(command);
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm" onClick={onClose}>
        <motion.div
          initial={{ opacity: 0, x: 300 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: 300 }}
          transition={{ duration: 0.3 }}
          className={cn(
            "fixed right-0 top-0 h-full w-full max-w-3xl bg-background border-l shadow-lg",
            className
          )}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b">
            <div className="flex items-center gap-3">
              <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-primary/10">
                <HelpCircle className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h2 className="text-lg font-semibold">SuperClaude Help</h2>
                <p className="text-sm text-muted-foreground">
                  Learn how to use SuperClaude commands effectively
                </p>
              </div>
            </div>
            <Button variant="ghost" size="icon" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Content */}
          <ScrollArea className="h-[calc(100vh-88px)]">
            <Tabs value={selectedTab} onValueChange={setSelectedTab} className="p-6">
              <TabsList className="grid w-full grid-cols-5">
                <TabsTrigger value="quickstart">Quick Start</TabsTrigger>
                <TabsTrigger value="commands">Commands</TabsTrigger>
                <TabsTrigger value="modes">Modes</TabsTrigger>
                <TabsTrigger value="workflows">Workflows</TabsTrigger>
                <TabsTrigger value="tips">Tips</TabsTrigger>
              </TabsList>

              {/* Quick Start Tab */}
              <TabsContent value="quickstart" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Getting Started with SuperClaude</CardTitle>
                    <CardDescription>
                      SuperClaude enhances Claude with 22 specialized commands, 7 behavioral modes, and 11 expert personas.
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2">Essential Commands</h4>
                      <div className="grid grid-cols-2 gap-2">
                        {['analyze', 'build', 'implement', 'improve', 'test', 'document'].map((cmd) => {
                          const command = commands.find(c => c.name === cmd);
                          if (!command) return null;
                          return (
                            <Button
                              key={cmd}
                              variant="outline"
                              size="sm"
                              className="justify-start"
                              onClick={() => handleCommandClick(command)}
                            >
                              <span className="font-mono text-xs">/sc:{cmd}</span>
                            </Button>
                          );
                        })}
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2">Command Structure</h4>
                      <div className="bg-muted p-3 rounded-lg font-mono text-sm">
                        /sc:[command] [arguments] @[path] --[flags]
                      </div>
                      <p className="text-xs text-muted-foreground mt-2">
                        Example: <code>/sc:analyze @src/ --focus security</code>
                      </p>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2">When to Use SuperClaude</h4>
                      <ul className="space-y-1 text-sm">
                        <li className="flex items-start gap-2">
                          <ChevronRight className="h-4 w-4 mt-0.5 text-primary" />
                          <span>Complex tasks requiring multiple steps</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <ChevronRight className="h-4 w-4 mt-0.5 text-primary" />
                          <span>Need expert guidance (personas auto-activate)</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <ChevronRight className="h-4 w-4 mt-0.5 text-primary" />
                          <span>Want comprehensive analysis or implementation</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <ChevronRight className="h-4 w-4 mt-0.5 text-primary" />
                          <span>Require specific expertise (security, performance, etc.)</span>
                        </li>
                      </ul>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Quick Command Reference</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Accordion type="single" collapsible>
                      <AccordionItem value="analysis">
                        <AccordionTrigger>
                          <div className="flex items-center gap-2">
                            <FileSearch className="h-4 w-4" />
                            Analysis & Understanding
                          </div>
                        </AccordionTrigger>
                        <AccordionContent className="space-y-2">
                          <div className="text-sm">
                            <p className="font-medium">When you need to understand code:</p>
                            <ul className="mt-2 space-y-1">
                              <li><code>/sc:analyze</code> - Deep system analysis</li>
                              <li><code>/sc:explain</code> - Get explanations</li>
                              <li><code>/sc:troubleshoot</code> - Find root causes</li>
                            </ul>
                          </div>
                        </AccordionContent>
                      </AccordionItem>

                      <AccordionItem value="development">
                        <AccordionTrigger>
                          <div className="flex items-center gap-2">
                            <Code className="h-4 w-4" />
                            Building & Implementation
                          </div>
                        </AccordionTrigger>
                        <AccordionContent className="space-y-2">
                          <div className="text-sm">
                            <p className="font-medium">When you need to create:</p>
                            <ul className="mt-2 space-y-1">
                              <li><code>/sc:build</code> - Build new features</li>
                              <li><code>/sc:implement</code> - Implement specifications</li>
                              <li><code>/sc:design</code> - Design architecture</li>
                            </ul>
                          </div>
                        </AccordionContent>
                      </AccordionItem>

                      <AccordionItem value="quality">
                        <AccordionTrigger>
                          <div className="flex items-center gap-2">
                            <Shield className="h-4 w-4" />
                            Quality & Improvement
                          </div>
                        </AccordionTrigger>
                        <AccordionContent className="space-y-2">
                          <div className="text-sm">
                            <p className="font-medium">When you need to improve:</p>
                            <ul className="mt-2 space-y-1">
                              <li><code>/sc:improve</code> - Enhance existing code</li>
                              <li><code>/sc:refactor</code> - Restructure code</li>
                              <li><code>/sc:optimize</code> - Improve performance</li>
                              <li><code>/sc:review</code> - Get code review</li>
                            </ul>
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    </Accordion>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Commands Tab */}
              <TabsContent value="commands" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>All Commands Guide</CardTitle>
                    <CardDescription>
                      Detailed information about each SuperClaude command
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Accordion type="single" collapsible value={expandedCommand || undefined}>
                      {commands.map((command) => {
                        const enhanced = enhancedCommands[command.name];
                        return (
                          <AccordionItem key={command.name} value={command.name}>
                            <AccordionTrigger
                              onClick={() => setExpandedCommand(
                                expandedCommand === command.name ? null : command.name
                              )}
                            >
                              <div className="flex items-center justify-between w-full pr-4">
                                <div className="flex items-center gap-2">
                                  {categoryIcons[command.category]}
                                  <span className="font-mono text-sm">/sc:{command.name}</span>
                                  {command.waveEnabled && (
                                    <Badge variant="secondary" className="text-xs">
                                      Wave
                                    </Badge>
                                  )}
                                </div>
                                <span className="text-xs text-muted-foreground">
                                  {command.description}
                                </span>
                              </div>
                            </AccordionTrigger>
                            <AccordionContent className="space-y-4">
                              {enhanced && (
                                <>
                                  <div>
                                    <h5 className="font-medium text-sm mb-2">Best For:</h5>
                                    <p className="text-sm text-muted-foreground">{enhanced.bestFor}</p>
                                  </div>

                                  <div>
                                    <h5 className="font-medium text-sm mb-2">Not For:</h5>
                                    <p className="text-sm text-muted-foreground">{enhanced.notFor}</p>
                                  </div>

                                  <div>
                                    <h5 className="font-medium text-sm mb-2">Use Cases:</h5>
                                    <ul className="space-y-1">
                                      {enhanced.useCases?.map((useCase, idx) => (
                                        <li key={idx} className="flex items-start gap-2 text-sm">
                                          <ChevronRight className="h-3 w-3 mt-0.5 text-primary" />
                                          <div>
                                            <span className="font-medium">{useCase.title}:</span>{" "}
                                            <span className="text-muted-foreground">{useCase.description}</span>
                                          </div>
                                        </li>
                                      ))}
                                    </ul>
                                  </div>

                                  {enhanced.tips && enhanced.tips.length > 0 && (
                                    <div>
                                      <h5 className="font-medium text-sm mb-2">Pro Tips:</h5>
                                      <ul className="space-y-1">
                                        {enhanced.tips.map((tip, idx) => (
                                          <li key={idx} className="flex items-start gap-2 text-sm text-muted-foreground">
                                            <Lightbulb className="h-3 w-3 mt-0.5 text-yellow-500" />
                                            {tip}
                                          </li>
                                        ))}
                                      </ul>
                                    </div>
                                  )}

                                  {enhanced.relatedCommands && enhanced.relatedCommands.length > 0 && (
                                    <div>
                                      <h5 className="font-medium text-sm mb-2">Related Commands:</h5>
                                      <div className="flex gap-2 flex-wrap">
                                        {enhanced.relatedCommands.map((related) => (
                                          <Badge key={related} variant="outline" className="text-xs">
                                            /sc:{related}
                                          </Badge>
                                        ))}
                                      </div>
                                    </div>
                                  )}

                                  <Button
                                    size="sm"
                                    onClick={() => handleCommandClick(command)}
                                  >
                                    Use This Command
                                  </Button>
                                </>
                              )}
                            </AccordionContent>
                          </AccordionItem>
                        );
                      })}
                    </Accordion>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Modes Tab */}
              <TabsContent value="modes" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Behavioral Modes</CardTitle>
                    <CardDescription>
                      Understanding when and how to use different modes
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {modeGuides.map((mode) => (
                        <Card key={mode.mode} className="border-l-4" style={{ borderLeftColor: `var(--${mode.color}-500)` }}>
                          <CardHeader className="pb-3">
                            <div className="flex items-center gap-2">
                              {modeIcons[mode.mode]}
                              <CardTitle className="text-base">{mode.label}</CardTitle>
                            </div>
                            <CardDescription className="text-sm">
                              {mode.description}
                            </CardDescription>
                          </CardHeader>
                          <CardContent className="space-y-3">
                            <div>
                              <p className="text-sm font-medium mb-1">When to Use:</p>
                              <ul className="text-sm text-muted-foreground space-y-0.5">
                                {mode.whenToUse.slice(0, 3).map((when, idx) => (
                                  <li key={idx}>• {when}</li>
                                ))}
                              </ul>
                            </div>
                            <div>
                              <p className="text-sm font-medium mb-1">Benefits:</p>
                              <ul className="text-sm text-muted-foreground space-y-0.5">
                                {mode.benefits.slice(0, 3).map((benefit, idx) => (
                                  <li key={idx}>• {benefit}</li>
                                ))}
                              </ul>
                            </div>
                            <div>
                              <p className="text-sm font-medium mb-1">Auto-Triggers:</p>
                              <div className="flex gap-1 flex-wrap">
                                {mode.autoTriggers.map((trigger, idx) => (
                                  <Badge key={idx} variant="secondary" className="text-xs">
                                    {trigger}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Workflows Tab */}
              <TabsContent value="workflows" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Common Workflows</CardTitle>
                    <CardDescription>
                      Step-by-step guides for common tasks
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Accordion type="single" collapsible>
                      {commonWorkflows.map((workflow) => (
                        <AccordionItem key={workflow.name} value={workflow.name}>
                          <AccordionTrigger>
                            <div className="text-left">
                              <p className="font-medium">{workflow.name}</p>
                              <p className="text-xs text-muted-foreground">{workflow.description}</p>
                            </div>
                          </AccordionTrigger>
                          <AccordionContent>
                            <div className="space-y-3">
                              {workflow.steps.map((step) => (
                                <div key={step.step} className="flex gap-3">
                                  <div className="flex items-center justify-center w-6 h-6 rounded-full bg-primary/10 text-xs font-medium">
                                    {step.step}
                                  </div>
                                  <div className="flex-1 space-y-1">
                                    <code className="text-sm bg-muted px-2 py-1 rounded">
                                      {step.command}
                                    </code>
                                    <p className="text-sm text-muted-foreground">
                                      {step.description}
                                    </p>
                                    <p className="text-xs text-muted-foreground">
                                      <span className="font-medium">Expected:</span> {step.expectedOutcome}
                                    </p>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </AccordionContent>
                        </AccordionItem>
                      ))}
                    </Accordion>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Tips Tab */}
              <TabsContent value="tips" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Tips & Tricks</CardTitle>
                    <CardDescription>
                      Pro tips for getting the most out of SuperClaude
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {quickTips.map((tip, idx) => (
                        <div key={idx} className="flex items-start gap-2">
                          <Lightbulb className="h-4 w-4 text-yellow-500 mt-0.5" />
                          <p className="text-sm">{tip}</p>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Personas Guide</CardTitle>
                    <CardDescription>
                      Understanding automatic persona activation
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-3">
                      {personaGuides.slice(0, 6).map((persona) => (
                        <div key={persona.name} className="p-3 border rounded-lg">
                          <div className="flex items-center gap-2 mb-2">
                            <span className="text-lg">{persona.icon}</span>
                            <p className="font-medium text-sm">{persona.name}</p>
                          </div>
                          <p className="text-xs text-muted-foreground mb-2">{persona.role}</p>
                          <div className="space-y-1">
                            <p className="text-xs font-medium">Best for:</p>
                            <ul className="text-xs text-muted-foreground">
                              {persona.bestFor.slice(0, 2).map((item, idx) => (
                                <li key={idx}>• {item}</li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </ScrollArea>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};
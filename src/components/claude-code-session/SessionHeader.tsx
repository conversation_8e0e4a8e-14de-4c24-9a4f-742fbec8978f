import React from 'react';
import { motion } from 'framer-motion';
import { 
  ArrowLeft, 
  Terminal, 
  FolderOpen, 
  Copy, 
  GitBranch,
  Settings,
  Hash,
  Command,
  Brain,
  Zap,
  Eye,
  Users,
  Workflow,
  Server,
  Cpu
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Popover } from '@/components/ui/popover';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import type { SuperClaudeMode } from '@/types/superClaude';

interface SessionHeaderProps {
  projectPath: string;
  claudeSessionId: string | null;
  totalTokens: number;
  isStreaming: boolean;
  hasMessages: boolean;
  showTimeline: boolean;
  activeMode?: SuperClaudeMode;
  orchestrationState?: {
    toolChains?: number;
    parallelGroups?: number;
    mcpServers?: string[];
    resourceUsage?: number;
  };
  copyPopoverOpen: boolean;
  onBack: () => void;
  onSelectPath: () => void;
  onCopyAsJsonl: () => void;
  onCopyAsMarkdown: () => void;
  onToggleTimeline: () => void;
  onProjectSettings?: () => void;
  onSlashCommandsSettings?: () => void;
  onModeSettings?: () => void;
  setCopyPopoverOpen: (open: boolean) => void;
}

export const SessionHeader: React.FC<SessionHeaderProps> = React.memo(({
  projectPath,
  claudeSessionId,
  totalTokens,
  isStreaming,
  hasMessages,
  showTimeline,
  activeMode,
  orchestrationState,
  copyPopoverOpen,
  onBack,
  onSelectPath,
  onCopyAsJsonl,
  onCopyAsMarkdown,
  onToggleTimeline,
  onProjectSettings,
  onSlashCommandsSettings,
  onModeSettings,
  setCopyPopoverOpen
}) => {
  const getModeIcon = (mode: SuperClaudeMode) => {
    switch (mode) {
      case 'brainstorming': return <Brain className="h-3 w-3" />;
      case 'introspection': return <Eye className="h-3 w-3" />;
      case 'orchestration': return <Zap className="h-3 w-3" />;
      case 'business-panel': return <Users className="h-3 w-3" />;
      case 'task-management': return <Workflow className="h-3 w-3" />;
      case 'token-efficiency': return <Cpu className="h-3 w-3" />;
      default: return null;
    }
  };

  const getModeLabel = (mode: SuperClaudeMode) => {
    switch (mode) {
      case 'brainstorming': return 'Brainstorming';
      case 'introspection': return 'Introspection';
      case 'orchestration': return 'Orchestration';
      case 'business-panel': return 'Business Panel';
      case 'task-management': return 'Task Management';
      case 'token-efficiency': return 'Token Efficiency';
      default: return mode;
    }
  };

  return (
    <motion.div 
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-background/95 backdrop-blur-sm border-b px-4 py-3 sticky top-0 z-40"
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Button
            variant="ghost"
            size="icon"
            onClick={onBack}
            className="h-8 w-8"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          
          <div className="flex items-center gap-2">
            <Terminal className="h-5 w-5 text-primary" />
            <span className="font-semibold">Claude Code Session</span>
          </div>

          {activeMode && activeMode !== 'normal' && (
            <Badge variant="secondary" className="flex items-center gap-1 text-xs">
              {getModeIcon(activeMode)}
              {getModeLabel(activeMode)}
            </Badge>
          )}
          
          {!projectPath && (
            <Button
              variant="outline"
              size="sm"
              onClick={onSelectPath}
              className="flex items-center gap-2"
            >
              <FolderOpen className="h-4 w-4" />
              Select Project
            </Button>
          )}
        </div>

        <div className="flex items-center gap-2">
          {claudeSessionId && (
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-xs">
                <Hash className="h-3 w-3 mr-1" />
                {claudeSessionId.slice(0, 8)}
              </Badge>
              {totalTokens > 0 && (
                <Badge variant="secondary" className="text-xs">
                  {totalTokens.toLocaleString()} tokens
                </Badge>
              )}
            </div>
          )}

          {/* Orchestration-specific badges */}
          {activeMode === 'orchestration' && orchestrationState && (
            <div className="flex items-center gap-1">
              {orchestrationState.toolChains !== undefined && (
                <Badge variant="outline" className="text-xs">
                  <Workflow className="h-3 w-3 mr-1" />
                  {orchestrationState.toolChains} chains
                </Badge>
              )}
              {orchestrationState.parallelGroups !== undefined && (
                <Badge variant="outline" className="text-xs">
                  <Cpu className="h-3 w-3 mr-1" />
                  {orchestrationState.parallelGroups} groups
                </Badge>
              )}
              {orchestrationState.mcpServers && orchestrationState.mcpServers.length > 0 && (
                <Badge variant="outline" className="text-xs">
                  <Server className="h-3 w-3 mr-1" />
                  {orchestrationState.mcpServers.length} servers
                </Badge>
              )}
              {orchestrationState.resourceUsage !== undefined && (
                <Badge 
                  variant={orchestrationState.resourceUsage > 80 ? "destructive" : "secondary"} 
                  className="text-xs"
                >
                  {orchestrationState.resourceUsage}% usage
                </Badge>
              )}
            </div>
          )}

          {hasMessages && !isStreaming && (
            <Popover
              open={copyPopoverOpen}
              onOpenChange={setCopyPopoverOpen}
              trigger={
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <Copy className="h-4 w-4" />
                </Button>
              }
              content={
                <div className="space-y-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start"
                    onClick={onCopyAsJsonl}
                  >
                    Copy as JSONL
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start"
                    onClick={onCopyAsMarkdown}
                  >
                    Copy as Markdown
                  </Button>
                </div>
              }
              className="w-48 p-2"
            />
          )}

          <Button
            variant="ghost"
            size="icon"
            onClick={onToggleTimeline}
            className={cn(
              "h-8 w-8 transition-colors",
              showTimeline && "bg-accent text-accent-foreground"
            )}
          >
            <GitBranch className="h-4 w-4" />
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <Settings className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              {onProjectSettings && projectPath && (
                <DropdownMenuItem onClick={onProjectSettings}>
                  <Settings className="h-4 w-4 mr-2" />
                  Project Settings
                </DropdownMenuItem>
              )}
              {onSlashCommandsSettings && projectPath && (
                <DropdownMenuItem onClick={onSlashCommandsSettings}>
                  <Command className="h-4 w-4 mr-2" />
                  Slash Commands
                </DropdownMenuItem>
              )}
              {onModeSettings && (
                <DropdownMenuItem onClick={onModeSettings}>
                  <Brain className="h-4 w-4 mr-2" />
                  Mode Settings
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </motion.div>
  );
});
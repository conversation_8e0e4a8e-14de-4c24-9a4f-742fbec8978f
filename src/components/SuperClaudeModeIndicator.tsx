import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Brain,
  Lightbulb,
  Target,
  Cpu,
  Zap,
  Briefcase,
  Sparkles,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { TooltipProvider, TooltipSimple } from "@/components/ui/tooltip-modern";
import type { SuperClaudeMode, SuperClaudePersona } from "@/types/superClaude";
import { cn } from "@/lib/utils";

interface SuperClaudeModeIndicatorProps {
  mode: SuperClaudeMode;
  personas: SuperClaudePersona[];
  tokenEfficiency: boolean;
  businessPanel: boolean;
  className?: string;
}

const modeIcons: Record<SuperClaudeMode, React.ReactNode> = {
  normal: <Sparkles className="h-3 w-3" />,
  brainstorming: <Lightbulb className="h-3 w-3" />,
  introspection: <Brain className="h-3 w-3" />,
  "task-management": <Target className="h-3 w-3" />,
  orchestration: <Cpu className="h-3 w-3" />,
  "token-efficiency": <Zap className="h-3 w-3" />,
  "business-panel": <Briefcase className="h-3 w-3" />,
};

const modeColors: Record<SuperClaudeMode, string> = {
  normal: "bg-gray-500/10 text-gray-500 border-gray-500/20",
  brainstorming: "bg-yellow-500/10 text-yellow-500 border-yellow-500/20",
  introspection: "bg-purple-500/10 text-purple-500 border-purple-500/20",
  "task-management": "bg-blue-500/10 text-blue-500 border-blue-500/20",
  orchestration: "bg-green-500/10 text-green-500 border-green-500/20",
  "token-efficiency": "bg-orange-500/10 text-orange-500 border-orange-500/20",
  "business-panel": "bg-indigo-500/10 text-indigo-500 border-indigo-500/20",
};

const personaColors: Record<SuperClaudePersona, string> = {
  architect: "bg-blue-500/10 text-blue-500",
  frontend: "bg-green-500/10 text-green-500",
  backend: "bg-purple-500/10 text-purple-500",
  security: "bg-red-500/10 text-red-500",
  performance: "bg-orange-500/10 text-orange-500",
  analyzer: "bg-cyan-500/10 text-cyan-500",
  qa: "bg-pink-500/10 text-pink-500",
  refactorer: "bg-yellow-500/10 text-yellow-500",
  devops: "bg-indigo-500/10 text-indigo-500",
  mentor: "bg-teal-500/10 text-teal-500",
  scribe: "bg-gray-500/10 text-gray-500",
  requirements: "bg-blue-400/10 text-blue-400",
};

export const SuperClaudeModeIndicator: React.FC<SuperClaudeModeIndicatorProps> = ({
  mode,
  personas,
  tokenEfficiency,
  businessPanel,
  className,
}) => {
  if (mode === "normal" && personas.length === 0 && !tokenEfficiency && !businessPanel) {
    return null;
  }

  return (
    <TooltipProvider>
      <div className={cn("flex items-center gap-2", className)}>
        <AnimatePresence>
          {/* Mode Indicator */}
          {mode !== "normal" && (
            <motion.div
              key="mode-indicator"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.2 }}
            >
              <TooltipSimple content={`Mode: ${mode.replace("-", " ")}`}>
                <Badge
                  variant="outline"
                  className={cn(
                    "flex items-center gap-1 px-2 py-0.5 text-xs capitalize",
                    modeColors[mode]
                  )}
                >
                  {modeIcons[mode]}
                  <span>{mode.replace("-", " ")}</span>
                </Badge>
              </TooltipSimple>
            </motion.div>
          )}

          {/* Token Efficiency Indicator */}
          {tokenEfficiency && (
            <motion.div
              key="token-efficiency"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.2 }}
            >
              <TooltipSimple content="Token Efficiency Mode: 30-50% reduction">
                <Badge
                  variant="outline"
                  className="flex items-center gap-1 px-2 py-0.5 text-xs bg-orange-500/10 text-orange-500 border-orange-500/20"
                >
                  <Zap className="h-3 w-3" />
                  <span>Compressed</span>
                </Badge>
              </TooltipSimple>
            </motion.div>
          )}

          {/* Business Panel Indicator */}
          {businessPanel && (
            <motion.div
              key="business-panel"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.2 }}
            >
              <TooltipSimple content="Business Panel Active">
                <Badge
                  variant="outline"
                  className="flex items-center gap-1 px-2 py-0.5 text-xs bg-indigo-500/10 text-indigo-500 border-indigo-500/20"
                >
                  <Briefcase className="h-3 w-3" />
                  <span>Business Panel</span>
                </Badge>
              </TooltipSimple>
            </motion.div>
          )}

          {/* Active Personas */}
          {personas.length > 0 && (
            <motion.div
              key="personas-container"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.2 }}
              className="flex items-center gap-1"
            >
              <span className="text-xs text-muted-foreground">Personas:</span>
              {personas.slice(0, 3).map((persona, index) => (
                <motion.div
                  key={persona}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.05 }}
                >
                  <TooltipSimple content={`Active: ${persona}`}>
                    <Badge
                      variant="outline"
                      className={cn(
                        "px-2 py-0.5 text-xs capitalize",
                        personaColors[persona]
                      )}
                    >
                      {persona}
                    </Badge>
                  </TooltipSimple>
                </motion.div>
              ))}
              {personas.length > 3 && (
                <TooltipSimple content={`+${personas.length - 3} more personas`}>
                  <Badge
                    variant="outline"
                    className="px-2 py-0.5 text-xs bg-gray-500/10 text-gray-500"
                  >
                    +{personas.length - 3}
                  </Badge>
                </TooltipSimple>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </TooltipProvider>
  );
};
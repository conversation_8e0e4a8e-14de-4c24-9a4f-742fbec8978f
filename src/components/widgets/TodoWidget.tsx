import React from "react";
import { CheckCircle2, Circle, Clock, FileEdit, Workflow, Package, Target } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import type { TaskItem } from '@/services/modes/taskManagement';

interface TodoWidgetProps {
  todos: any[];
  tasks?: TaskItem[];
  result?: any;
}

export const TodoWidget: React.FC<TodoWidgetProps> = ({ todos, tasks, result: _result }) => {
  const statusIcons = {
    completed: <CheckCircle2 className="h-4 w-4 text-green-500" />,
    in_progress: <Clock className="h-4 w-4 text-blue-500 animate-pulse" />,
    pending: <Circle className="h-4 w-4 text-muted-foreground" />,
    blocked: <Circle className="h-4 w-4 text-red-500" />
  };

  const priorityColors = {
    high: "bg-red-500/10 text-red-500 border-red-500/20",
    medium: "bg-yellow-500/10 text-yellow-500 border-yellow-500/20",
    low: "bg-green-500/10 text-green-500 border-green-500/20"
  };

  // Ensure todos is always an array
  const safeTodos = Array.isArray(todos) ? todos : [];
  const safeTasks = Array.isArray(tasks) ? tasks : [];

  // Combine todos and tasks for display
  const allItems = [
    ...safeTodos.map(todo => ({
      ...todo,
      type: 'todo'
    })),
    ...safeTasks.map(task => ({
      ...task,
      type: 'task',
      content: task.name,
      priority: task.priority || 'medium'
    }))
  ];

  const renderTaskIcon = (item: any) => {
    if (item.type === 'task') {
      if (item.subtasks && item.subtasks.length > 0) {
        return <Workflow className="h-4 w-4 text-blue-500" />;
      }
      return <Package className="h-4 w-4 text-purple-500" />;
    }
    return <FileEdit className="h-4 w-4 text-primary" />;
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center gap-2 mb-3">
        {safeTasks.length > 0 ? (
          <Workflow className="h-4 w-4 text-blue-500" />
        ) : (
          <FileEdit className="h-4 w-4 text-primary" />
        )}
        <span className="text-sm font-medium">
          {safeTasks.length > 0 ? 'Task List' : 'Todo List'}
        </span>
        {(safeTodos.length > 0 || safeTasks.length > 0) && (
          <Badge variant="secondary" className="text-xs">
            {safeTodos.length + safeTasks.length} items
          </Badge>
        )}
      </div>
      <div className="space-y-2">
        {allItems.map((item, idx) => (
          <div
            key={item.id || idx}
            className={cn(
              "flex items-start gap-3 p-3 rounded-lg border bg-card/50",
              item.status === "completed" && "opacity-60"
            )}
          >
            <div className="mt-0.5">
              {statusIcons[item.status as keyof typeof statusIcons] || statusIcons.pending}
            </div>
            <div className="flex-1 space-y-1">
              <div className="flex items-start justify-between">
                <p className={cn(
                  "text-sm",
                  item.status === "completed" && "line-through"
                )}>
                  {item.content}
                </p>
                {item.type === 'task' && item.estimatedTime && (
                  <Badge variant="outline" className="text-xs whitespace-nowrap">
                    {item.estimatedTime}m
                  </Badge>
                )}
              </div>
              <div className="flex flex-wrap gap-1">
                {item.priority && (
                  <Badge 
                    variant="outline" 
                    className={cn("text-xs", priorityColors[item.priority as keyof typeof priorityColors])}
                  >
                    {item.priority}
                  </Badge>
                )}
                {item.type === 'task' && item.dependencies && item.dependencies.length > 0 && (
                  <Badge variant="outline" className="text-xs">
                    <Target className="h-3 w-3 mr-1" />
                    {item.dependencies.length} deps
                  </Badge>
                )}
                {item.type === 'task' && item.subtasks && item.subtasks.length > 0 && (
                  <Badge variant="outline" className="text-xs">
                    <Workflow className="h-3 w-3 mr-1" />
                    {item.subtasks.length} sub
                  </Badge>
                )}
              </div>
            </div>
          </div>
        ))}
        {allItems.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <FileEdit className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No tasks or todos available</p>
          </div>
        )}
      </div>
    </div>
  );
};
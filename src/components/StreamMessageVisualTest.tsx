import React, { useState } from "react";
import { StreamMessage } from "./StreamMessage";
import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import type { ClaudeStreamMessage } from "./AgentExecution";

// Mock data for different message types
const mockMessages = {
  assistant: {
    type: "assistant" as const,
    message: {
      content: [
        {
          type: "text",
          text: "Hello! I'm <PERSON>, an AI assistant. Here's some example content with **bold text** and *italic text*. \n\nI can also create lists:\n\n1. First item\n2. Second item\n3. Third item\n\nAnd code blocks:\n\n```javascript\nconst greeting = 'Hello, world!';\nconsole.log(greeting);\n```\n\n> This is a blockquote example\n\n## Heading 2\n\nSome more text content to demonstrate the styling."
        }
      ]
    }
  },
  user: {
    type: "user" as const,
    message: {
      content: [
        {
          type: "text",
          text: "This is a user message with some content. It can contain plain text or commands."
        }
      ]
    },
    superClaudeContext: {
      command: "test",
      mode: "brainstorming",
      personas: ["architect", "frontend"],
      waveStrategy: "aggressive",
      mcpServers: ["magic", "sequential"]
    }
  },
  resultSuccess: {
    type: "result" as const,
    result: "The operation completed successfully. Here's a summary of what was accomplished:\n\n- Task 1 was completed\n- Task 2 was completed\n- All tests passed\n\n```python\ndef example_function():\n    return \"Success\"\n```",
    usage: {
      input_tokens: 1200,
      output_tokens: 800
    },
    cost_usd: 0.0125,
    duration_ms: 2450
  },
  resultError: {
    type: "result" as const,
    is_error: true,
    error: "An error occurred while processing the request. Please check your input and try again.",
    result: "Error details:\n\n- Invalid parameter provided\n- Connection timeout\n\n```bash\nError: Connection failed\n    at line 42\n```"
  }
};

const themes = [
  { name: "Dark", className: "theme-dark" },
  { name: "Gray", className: "theme-gray" },
  { name: "Light", className: "theme-light" }
];

export const StreamMessageVisualTest: React.FC = () => {
  const [currentTheme, setCurrentTheme] = useState(0);
  const [messageType, setMessageType] = useState("assistant");

  const theme = themes[currentTheme];
  const message = mockMessages[messageType as keyof typeof mockMessages];

  return (
    <div className={cn("min-h-screen p-8", theme.className)}>
      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>StreamMessage Visual Test</CardTitle>
          <p className="text-sm text-muted-foreground">
            Testing visual improvements across different themes and message types
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Theme Selector */}
          <div className="flex flex-wrap gap-2">
            <span className="text-sm font-medium self-center">Theme:</span>
            {themes.map((t, idx) => (
              <Button
                key={t.name}
                variant={currentTheme === idx ? "default" : "outline"}
                size="sm"
                onClick={() => setCurrentTheme(idx)}
              >
                {t.name}
              </Button>
            ))}
          </div>

          {/* Message Type Selector */}
          <div className="flex flex-wrap gap-2">
            <span className="text-sm font-medium self-center">Message Type:</span>
            {Object.keys(mockMessages).map((type) => (
              <Button
                key={type}
                variant={messageType === type ? "default" : "outline"}
                size="sm"
                onClick={() => setMessageType(type)}
              >
                {type}
              </Button>
            ))}
          </div>

          {/* Test Message */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Test Message:</h3>
            <div className="border rounded-lg p-4 bg-background">
              <StreamMessage 
                message={message} 
                streamMessages={[]} 
              />
            </div>
          </div>

          {/* Multiple Messages Test */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Multiple Messages:</h3>
            <div className="border rounded-lg p-4 bg-background space-y-4">
              <StreamMessage 
                message={mockMessages.assistant} 
                streamMessages={[]} 
              />
              <StreamMessage 
                message={mockMessages.user} 
                streamMessages={[]} 
              />
              <StreamMessage 
                message={mockMessages.resultSuccess} 
                streamMessages={[]} 
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default StreamMessageVisualTest;
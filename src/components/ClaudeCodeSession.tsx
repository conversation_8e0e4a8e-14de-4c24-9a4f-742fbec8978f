import React, { useState, useEffect, useRef, useMemo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Copy,
  ChevronDown,
  GitBranch,
  ChevronUp,
  X,
  Has<PERSON>,
  <PERSON><PERSON>,
  <PERSON>rkles,
  HelpCircle,
  Keyboard,
  Brain
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Popover } from "@/components/ui/popover";
import { api, type Session } from "@/lib/api";
import { cn } from "@/lib/utils";
import { listen, type UnlistenFn } from "@tauri-apps/api/event";
import { StreamMessage } from "./StreamMessage";
import { FloatingPromptInput, type FloatingPromptInputRef } from "./FloatingPromptInput";
import { ErrorBoundary } from "./ErrorBoundary";
import { TimelineNavigator } from "./TimelineNavigator";
import { CheckpointSettings } from "./CheckpointSettings";
import { SlashCommandsManager } from "./SlashCommandsManager";
import { MCPServerSelector } from "./MCPServerSelector";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { TooltipProvider, TooltipSimple } from "@/components/ui/tooltip-modern";
import { SplitPane } from "@/components/ui/split-pane";
import { WebviewPreview } from "./WebviewPreview";
import { SessionHeader } from "./claude-code-session/SessionHeader";
import type { ClaudeStreamMessage } from "./AgentExecution";
import { useVirtualizer } from "@tanstack/react-virtual";
import { useTrackEvent, useComponentMetrics, useWorkflowTracking } from "@/hooks";
import { SessionPersistenceService } from "@/services/sessionPersistence";
import { superClaudeService } from "@/services/superClaude";
import { SuperClaudeCommandPicker } from "./SuperClaudeCommandPicker";
import { SuperClaudeModeIndicator } from "./SuperClaudeModeIndicator";
import { SuperClaudeModeSelector } from "./SuperClaudeModeSelector";
import { SuperClaudeHelpPanel } from "./SuperClaudeHelpPanel";
import { SuperClaudeQuickReference } from "./SuperClaudeQuickReference";
import { AgentNotification } from "./AgentNotification";
import { BrainstormingView } from "./BrainstormingView";
import { IntrospectionView } from "./IntrospectionView";
import { TokenEfficiencyMonitor } from "./TokenEfficiencyMonitor";
import type { 
  SuperClaudeCommand,
  SuperClaudeCommandContext, 
  SuperClaudeMode, 
  SuperClaudePersona,
  ModeActivationContext,
  PersonaActivationContext 
} from "@/types/superClaude";

interface ClaudeCodeSessionProps {
  /**
   * Optional session to resume (when clicking from SessionList)
   */
  session?: Session;
  /**
   * Initial project path (for new sessions)
   */
  initialProjectPath?: string;
  /**
   * Callback to go back
   */
  onBack: () => void;
  /**
   * Callback to open hooks configuration
   */
  onProjectSettings?: (projectPath: string) => void;
  /**
   * Optional className for styling
   */
  className?: string;
  /**
   * Callback when streaming state changes
   */
  onStreamingChange?: (isStreaming: boolean, sessionId: string | null) => void;
  /**
   * Callback when project path changes
   */
  onProjectPathChange?: (path: string) => void;
}

/**
 * ClaudeCodeSession component for interactive Claude Code sessions
 * 
 * @example
 * <ClaudeCodeSession onBack={() => setView('projects')} />
 */
export const ClaudeCodeSession: React.FC<ClaudeCodeSessionProps> = ({
  session,
  initialProjectPath = "",
  className,
  onStreamingChange,
  onProjectPathChange,
}) => {
  const [projectPath] = useState(initialProjectPath || session?.project_path || "");
  const [messages, setMessages] = useState<ClaudeStreamMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [rawJsonlOutput, setRawJsonlOutput] = useState<string[]>([]);
  const [copyPopoverOpen, setCopyPopoverOpen] = useState(false);
  const [isFirstPrompt, setIsFirstPrompt] = useState(!session);
  const [totalTokens, setTotalTokens] = useState(0);
  const [extractedSessionInfo, setExtractedSessionInfo] = useState<{ sessionId: string; projectId: string } | null>(null);
  const [claudeSessionId, setClaudeSessionId] = useState<string | null>(null);
  const [showTimeline, setShowTimeline] = useState(false);
  const [timelineVersion, setTimelineVersion] = useState(0);
  const [showSettings, setShowSettings] = useState(false);
  const [showForkDialog, setShowForkDialog] = useState(false);
  const [showSlashCommandsSettings, setShowSlashCommandsSettings] = useState(false);
  const [forkCheckpointId, setForkCheckpointId] = useState<string | null>(null);
  const [forkSessionName, setForkSessionName] = useState("");
  
  // SuperClaude state
  const [showSuperClaudeCommands, setShowSuperClaudeCommands] = useState(false);
  const [showSuperClaudeHelp, setShowSuperClaudeHelp] = useState(false);
  const [showQuickReference, setShowQuickReference] = useState(false);
  const [superClaudeMode, setSuperClaudeMode] = useState<SuperClaudeMode>("normal");
  const [superClaudePersonas, setSuperClaudePersonas] = useState<SuperClaudePersona[]>([]);
  const [tokenEfficiencyEnabled, setTokenEfficiencyEnabled] = useState(false);
  const [businessPanelActive, setBusinessPanelActive] = useState(false);
  const [activeMCPServers, setActiveMCPServers] = useState<string[]>([]);
  const [showAgentNotification, setShowAgentNotification] = useState(false);
  const [notificationAgents, setNotificationAgents] = useState<SuperClaudePersona[]>([]);
  const [brainstormingTopic, setBrainstormingTopic] = useState<string | null>(null);
  const [showBrainstormingView, setShowBrainstormingView] = useState(false);
  
  // Business panel progress tracking (for future UI integration)
  const [_businessPanelProgress, setBusinessPanelProgress] = useState<{
    stage: 'idle' | 'creating-session' | 'analyzing' | 'synthesizing' | 'complete' | 'error';
    currentExpert?: string;
    progress: number;
    message: string;
  }>({ stage: 'idle', progress: 0, message: '' });
  
  // Queued prompts state
  const [queuedPrompts, setQueuedPrompts] = useState<Array<{ id: string; prompt: string; model: "sonnet" | "opus" }>>([]);
  
  // New state for preview feature
  const [showPreview, setShowPreview] = useState(false);
  const [previewUrl, setPreviewUrl] = useState("");
  const [showPreviewPrompt, setShowPreviewPrompt] = useState(false);
  const [splitPosition, setSplitPosition] = useState(50);
  const [isPreviewMaximized, setIsPreviewMaximized] = useState(false);
  
  // Add collapsed state for queued prompts
  const [queuedPromptsCollapsed, setQueuedPromptsCollapsed] = useState(false);
  
  const parentRef = useRef<HTMLDivElement>(null);
  const unlistenRefs = useRef<UnlistenFn[]>([]);
  const hasActiveSessionRef = useRef(false);
  const floatingPromptRef = useRef<FloatingPromptInputRef>(null);
  const queuedPromptsRef = useRef<Array<{ id: string; prompt: string; model: "sonnet" | "opus" }>>([]);
  const isMountedRef = useRef(true);
  const isListeningRef = useRef(false);
  const sessionStartTime = useRef<number>(Date.now());
  
  // Session metrics state for enhanced analytics
  const sessionMetrics = useRef({
    firstMessageTime: null as number | null,
    promptsSent: 0,
    toolsExecuted: 0,
    toolsFailed: 0,
    filesCreated: 0,
    filesModified: 0,
    filesDeleted: 0,
    codeBlocksGenerated: 0,
    errorsEncountered: 0,
    lastActivityTime: Date.now(),
    toolExecutionTimes: [] as number[],
    checkpointCount: 0,
    wasResumed: !!session,
    modelChanges: [] as Array<{ from: string; to: string; timestamp: number }>,
  });

  // Analytics tracking
  const trackEvent = useTrackEvent();
  useComponentMetrics('ClaudeCodeSession');
  // const aiTracking = useAIInteractionTracking('sonnet'); // Default model
  const workflowTracking = useWorkflowTracking('claude_session');
  
  // Keyboard shortcuts for SuperClaude help
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Cmd+Shift+S: Open SuperClaude commands
      if (e.metaKey && e.shiftKey && e.key === 's') {
        e.preventDefault();
        setShowSuperClaudeCommands(true);
      }
      // Cmd+Shift+H: Open SuperClaude help
      if (e.metaKey && e.shiftKey && e.key === 'h') {
        e.preventDefault();
        setShowSuperClaudeHelp(true);
      }
      // Cmd+?: Toggle quick reference
      if (e.metaKey && e.key === '?') {
        e.preventDefault();
        setShowQuickReference(prev => !prev);
      }
      // Escape: Close all dialogs
      if (e.key === 'Escape') {
        setShowSuperClaudeCommands(false);
        setShowSuperClaudeHelp(false);
        setShowQuickReference(false);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);
  
  // Call onProjectPathChange when component mounts with initial path
  useEffect(() => {
    if (onProjectPathChange && projectPath) {
      onProjectPathChange(projectPath);
    }
  }, []); // Only run on mount
  
  // Keep ref in sync with state
  useEffect(() => {
    queuedPromptsRef.current = queuedPrompts;
  }, [queuedPrompts]);

  // Get effective session info (from prop or extracted) - use useMemo to ensure it updates
  const effectiveSession = useMemo(() => {
    if (session) return session;
    if (extractedSessionInfo) {
      return {
        id: extractedSessionInfo.sessionId,
        project_id: extractedSessionInfo.projectId,
        project_path: projectPath,
        created_at: Date.now(),
      } as Session;
    }
    return null;
  }, [session, extractedSessionInfo, projectPath]);

  // Filter out messages that shouldn't be displayed
  const displayableMessages = useMemo(() => {
    return messages.filter((message, index) => {
      // Skip meta messages that don't have meaningful content
      if (message.isMeta && !message.leafUuid && !message.summary) {
        return false;
      }

      // Skip user messages that only contain tool results that are already displayed
      if (message.type === "user" && message.message) {
        if (message.isMeta) return false;

        const msg = message.message;
        if (!msg.content || (Array.isArray(msg.content) && msg.content.length === 0)) {
          return false;
        }

        if (Array.isArray(msg.content)) {
          let hasVisibleContent = false;
          for (const content of msg.content) {
            if (content.type === "text") {
              hasVisibleContent = true;
              break;
            }
            if (content.type === "tool_result") {
              let willBeSkipped = false;
              if (content.tool_use_id) {
                // Look for the matching tool_use in previous assistant messages
                for (let i = index - 1; i >= 0; i--) {
                  const prevMsg = messages[i];
                  if (prevMsg.type === 'assistant' && prevMsg.message?.content && Array.isArray(prevMsg.message.content)) {
                    const toolUse = prevMsg.message.content.find((c: any) => 
                      c.type === 'tool_use' && c.id === content.tool_use_id
                    );
                    if (toolUse) {
                      const toolName = toolUse.name?.toLowerCase();
                      const toolsWithWidgets = [
                        'task', 'edit', 'multiedit', 'todowrite', 'ls', 'read', 
                        'glob', 'bash', 'write', 'grep'
                      ];
                      if (toolsWithWidgets.includes(toolName) || toolUse.name?.startsWith('mcp__')) {
                        willBeSkipped = true;
                      }
                      break;
                    }
                  }
                }
              }
              if (!willBeSkipped) {
                hasVisibleContent = true;
                break;
              }
            }
          }
          if (!hasVisibleContent) {
            return false;
          }
        }
      }
      return true;
    });
  }, [messages]);

  const rowVirtualizer = useVirtualizer({
    count: displayableMessages.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 150, // Estimate, will be dynamically measured
    overscan: 5,
  });

  // Debug logging
  useEffect(() => {
    console.log('[ClaudeCodeSession] State update:', {
      projectPath,
      session,
      extractedSessionInfo,
      effectiveSession,
      messagesCount: messages.length,
      isLoading
    });
  }, [projectPath, session, extractedSessionInfo, effectiveSession, messages.length, isLoading]);

  // Load session history if resuming
  useEffect(() => {
    if (session) {
      // Set the claudeSessionId immediately when we have a session
      setClaudeSessionId(session.id);
      
      // Load session history first, then check for active session
      const initializeSession = async () => {
        await loadSessionHistory();
        // After loading history, check if the session is still active
        if (isMountedRef.current) {
          await checkForActiveSession();
        }
      };
      
      initializeSession();
    }
  }, [session]); // Remove hasLoadedSession dependency to ensure it runs on mount

  // Report streaming state changes
  useEffect(() => {
    onStreamingChange?.(isLoading, claudeSessionId);
  }, [isLoading, claudeSessionId, onStreamingChange]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (displayableMessages.length > 0) {
      rowVirtualizer.scrollToIndex(displayableMessages.length - 1, { align: 'end', behavior: 'smooth' });
    }
  }, [displayableMessages.length, rowVirtualizer]);

  // Calculate total tokens from messages
  useEffect(() => {
    const tokens = messages.reduce((total, msg) => {
      if (msg.message?.usage) {
        return total + msg.message.usage.input_tokens + msg.message.usage.output_tokens;
      }
      if (msg.usage) {
        return total + msg.usage.input_tokens + msg.usage.output_tokens;
      }
      return total;
    }, 0);
    setTotalTokens(tokens);
  }, [messages]);

  const loadSessionHistory = async () => {
    if (!session) return;
    
    try {
      setIsLoading(true);
      setError(null);
      
      const history = await api.loadSessionHistory(session.id, session.project_id);
      
      // Save session data for restoration
      if (history && history.length > 0) {
        SessionPersistenceService.saveSession(
          session.id,
          session.project_id,
          session.project_path,
          history.length
        );
      }
      
      // Convert history to messages format
      const loadedMessages: ClaudeStreamMessage[] = history.map(entry => ({
        ...entry,
        type: entry.type || "assistant"
      }));
      
      setMessages(loadedMessages);
      setRawJsonlOutput(history.map(h => JSON.stringify(h)));
      
      // After loading history, we're continuing a conversation
      setIsFirstPrompt(false);
      
      // Scroll to bottom after loading history
      setTimeout(() => {
        if (loadedMessages.length > 0) {
          rowVirtualizer.scrollToIndex(loadedMessages.length - 1, { align: 'end', behavior: 'auto' });
        }
      }, 100);
    } catch (err) {
      console.error("Failed to load session history:", err);
      setError("Failed to load session history");
    } finally {
      setIsLoading(false);
    }
  };

  const checkForActiveSession = async () => {
    // If we have a session prop, check if it's still active
    if (session) {
      try {
        const activeSessions = await api.listRunningClaudeSessions();
        const activeSession = activeSessions.find((s: any) => {
          if ('process_type' in s && s.process_type && 'ClaudeSession' in s.process_type) {
            return (s.process_type as any).ClaudeSession.session_id === session.id;
          }
          return false;
        });
        
        if (activeSession) {
          // Session is still active, reconnect to its stream
          console.log('[ClaudeCodeSession] Found active session, reconnecting:', session.id);
          // IMPORTANT: Set claudeSessionId before reconnecting
          setClaudeSessionId(session.id);
          
          // Don't add buffered messages here - they've already been loaded by loadSessionHistory
          // Just set up listeners for new messages
          
          // Set up listeners for the active session
          reconnectToSession(session.id);
        }
      } catch (err) {
        console.error('Failed to check for active sessions:', err);
      }
    }
  };

  const reconnectToSession = async (sessionId: string) => {
    console.log('[ClaudeCodeSession] Reconnecting to session:', sessionId);
    
    // Prevent duplicate listeners
    if (isListeningRef.current) {
      console.log('[ClaudeCodeSession] Already listening to session, skipping reconnect');
      return;
    }
    
    // Clean up previous listeners
    unlistenRefs.current.forEach(unlisten => unlisten());
    unlistenRefs.current = [];
    
    // IMPORTANT: Set the session ID before setting up listeners
    setClaudeSessionId(sessionId);
    
    // Mark as listening
    isListeningRef.current = true;
    
    // Set up session-specific listeners
    const outputUnlisten = await listen<string>(`claude-output:${sessionId}`, async (event) => {
      try {
        console.log('[ClaudeCodeSession] Received claude-output on reconnect:', event.payload);
        
        if (!isMountedRef.current) return;
        
        // Store raw JSONL
        setRawJsonlOutput(prev => [...prev, event.payload]);
        
        // Parse and display
        const message = JSON.parse(event.payload) as ClaudeStreamMessage;
        setMessages(prev => [...prev, message]);
      } catch (err) {
        console.error("Failed to parse message:", err, event.payload);
      }
    });

    const errorUnlisten = await listen<string>(`claude-error:${sessionId}`, (event) => {
      console.error("Claude error:", event.payload);
      if (isMountedRef.current) {
        setError(event.payload);
      }
    });

    const completeUnlisten = await listen<boolean>(`claude-complete:${sessionId}`, async (event) => {
      console.log('[ClaudeCodeSession] Received claude-complete on reconnect:', event.payload);
      if (isMountedRef.current) {
        setIsLoading(false);
        hasActiveSessionRef.current = false;
      }
    });

    unlistenRefs.current = [outputUnlisten, errorUnlisten, completeUnlisten];
    
    // Mark as loading to show the session is active
    if (isMountedRef.current) {
      setIsLoading(true);
      hasActiveSessionRef.current = true;
    }
  };

  // Project path selection handled by parent tab controls

  const handleSendPrompt = async (prompt: string, model: "sonnet" | "opus") => {
    console.log('[ClaudeCodeSession] handleSendPrompt called with:', { prompt, model, projectPath, claudeSessionId, effectiveSession });
    
    if (!projectPath) {
      setError("Please select a project directory first");
      return;
    }

    // Enhanced SuperClaude command processing with mode and persona detection
    let effectivePrompt = prompt;
    let detectedMode: SuperClaudeMode = 'normal';
    let activatedPersonas: SuperClaudePersona[] = [];
    let requiredMCPServers: string[] = [];
    let superClaudeCommandContext: SuperClaudeCommandContext | null = null;
    
    // Create activation context for intelligent detection
    const activationContext: ModeActivationContext = {
      resourceUsage: messages.length > 50 ? 80 : messages.length * 1.5, // Estimate based on message count
      complexity: prompt.split(' ').length > 50 ? 0.8 : 0.3,
      multiToolOperation: prompt.includes('and') && prompt.includes('then'),
      errorRecovery: error !== null,
      fileCount: 0, // Would need to be calculated from project
      directoryCount: 0, // Would need to be calculated from project
    };
    
    const personaContext: PersonaActivationContext = {
      domain: prompt.includes('ui') || prompt.includes('component') ? 'frontend' :
              prompt.includes('api') || prompt.includes('database') ? 'backend' :
              prompt.includes('deploy') ? 'infrastructure' :
              prompt.includes('security') || prompt.includes('auth') ? 'security' :
              prompt.includes('document') || prompt.includes('readme') ? 'documentation' : undefined,
      operationType: prompt.includes('analyze') ? 'analysis' :
                    prompt.includes('create') || prompt.includes('build') ? 'creation' :
                    prompt.includes('fix') || prompt.includes('debug') ? 'debugging' :
                    prompt.includes('refactor') || prompt.includes('improve') ? 'modification' : undefined,
      complexity: activationContext.complexity,
    };
    
    // Check if this is a SuperClaude command
    if (superClaudeService.isSuperClaudeCommand(prompt)) {
      console.log('[ClaudeCodeSession] Detected SuperClaude command, parsing...');
      const context = superClaudeService.parseCommand(prompt);
      superClaudeCommandContext = context;
      
      console.log('[ClaudeCodeSession] Parse result:', {
        hasContext: context !== null,
        context: context,
        prompt: prompt
      });
      
      if (context) {
        // Enhanced mode detection with context
        detectedMode = superClaudeService.detectModeWithContext(prompt, activationContext);
        
        // Auto-activate personas based on command and context
        activatedPersonas = context.personas || superClaudeService.autoActivatePersonas(prompt, personaContext);
        
        // Update context with detected mode and personas
        context.mode = detectedMode;
        context.personas = activatedPersonas;
        
        // Apply agent-specific behaviors if agents are activated
        if (activatedPersonas.length > 0) {
          console.log('[ClaudeCodeSession] Applying agent behaviors:', activatedPersonas);
          
          // Show notification for activated agents
          setNotificationAgents(activatedPersonas);
          setShowAgentNotification(true);
          
          // Apply agent enhancements through superClaude service
          const agentEnhancements = superClaudeService.applyAgentBehaviors(activatedPersonas, prompt);
          if (agentEnhancements) {
            // Store agent context for later use
            sessionStorage.setItem('activeAgents', JSON.stringify({
              activeAgents: activatedPersonas,
              capabilities: agentEnhancements.capabilities,
              preferredTools: agentEnhancements.tools,
              instructions: agentEnhancements.instructions
            }));
            
            // Log agent activation
            console.log('[ClaudeCodeSession] Agent behaviors applied:', agentEnhancements);
          }
        }
        
        // Apply mode behavior to prompt
        const modeEnhancedPrompt = await superClaudeService.applyModeBehavior(prompt, detectedMode);
        if (modeEnhancedPrompt !== prompt) {
          effectivePrompt = modeEnhancedPrompt;
        }
        
        // Process the command through SuperClaude with enhancements
        const enhancedPrompt = superClaudeService.formatCommandForExecution(context);
        effectivePrompt = enhancedPrompt;
        
        console.log('[ClaudeCodeSession] SuperClaude command processed:', {
          originalPrompt: prompt,
          enhancedPrompt,
          command: context.command,
          mode: detectedMode,
          personas: activatedPersonas,
          waveStrategy: context.waveStrategy,
          flags: context.flags
        });
        
        // Update SuperClaude state
        setSuperClaudeMode(detectedMode);
        setSuperClaudePersonas(activatedPersonas);
        
        // Get mode behavior configuration
        const modeBehavior = superClaudeService.getModesBehavior(detectedMode);
        
        // Enable token efficiency if needed
        if (modeBehavior.compressionLevel > 2 || (activationContext.resourceUsage || 0) > 75) {
          setTokenEfficiencyEnabled(true);
          superClaudeService.enableTokenEfficiency();
        }
        
        // For business panel mode, we'll let Claude handle it as a tool call instead of replacing the view
        if (detectedMode === 'business-panel') {
          // Don't activate the business panel view - let Claude handle it as a tool
          console.log('[ClaudeCodeSession] Business panel mode detected, will handle as tool call');
        }
        
        // Update service state with all enhancements
        // Use the MCP servers from the parsed context directly
        requiredMCPServers = context.mcpServers || [];
        console.log('[ClaudeCodeSession] Required MCP servers from context:', {
          command: context.command,
          mcpServers: requiredMCPServers,
          contextMcpServers: context.mcpServers
        });
        superClaudeService.updateSessionState({
          activeMode: detectedMode,
          activePersonas: activatedPersonas,
          tokenEfficiencyEnabled: modeBehavior.compressionLevel > 2,
          businessPanelActive: false, // Don't activate business panel mode - handle through tool calls
          mcpServersActive: requiredMCPServers,
        });
        
        // Check if required MCP servers are available
        const mcpAvailable = await superClaudeService.checkMCPServersAvailable(requiredMCPServers);
        if (!mcpAvailable) {
          console.warn(`[SuperClaude] Required MCP servers not available for command ${context.command}:`, {
            command: context.command,
            requiredServers: requiredMCPServers,
            available: false
          });
        } else if (requiredMCPServers.length > 0) {
          console.log('[SuperClaude] MCP servers activated:', requiredMCPServers);
          setActiveMCPServers(requiredMCPServers);
        }
        
        // Detect cross-persona collaboration needs
        const collaborations = superClaudeService.detectCrossPersonaCollaboration(activatedPersonas);
        if (Object.keys(collaborations).length > 0) {
          console.log('[SuperClaude] Cross-persona collaborations detected:', collaborations);
        }
        
        // Track SuperClaude command usage with enhanced metrics
        trackEvent.featureUsed('superclaude', context.command, {
          command: context.command,
          mode: detectedMode,
          personas: activatedPersonas.join(','),
          compressionLevel: modeBehavior.compressionLevel,
          collaborations: Object.keys(collaborations).join(','),
        });
      }
    } else {
      // Enhanced mode detection for regular prompts
      detectedMode = superClaudeService.detectModeWithContext(prompt, activationContext);
      
      // Auto-activate personas for regular prompts
      activatedPersonas = superClaudeService.autoActivatePersonas(prompt, personaContext);
      
      // Apply agent-specific behaviors if agents are activated
      if (activatedPersonas.length > 0) {
        console.log('[ClaudeCodeSession] Applying agent behaviors for regular prompt:', activatedPersonas);
        
        // Show notification for activated agents
        setNotificationAgents(activatedPersonas);
        setShowAgentNotification(true);
        
        // Apply agent enhancements through superClaude service
        const agentEnhancements = superClaudeService.applyAgentBehaviors(activatedPersonas, prompt);
        if (agentEnhancements) {
          // Store agent context for later use
          sessionStorage.setItem('activeAgents', JSON.stringify({
            activeAgents: activatedPersonas,
            capabilities: agentEnhancements.capabilities,
            preferredTools: agentEnhancements.tools,
            instructions: agentEnhancements.instructions
          }));
          
          // Enhance the prompt with agent context
          if (agentEnhancements.enhancedPrompt) {
            effectivePrompt = agentEnhancements.enhancedPrompt;
          }
          
          // Log agent activation
          console.log('[ClaudeCodeSession] Agent behaviors applied:', agentEnhancements);
        }
      }
      
      // Check if MCP servers are mentioned in the prompt
      const mentionedServers: string[] = [];
      if (prompt.toLowerCase().includes('magic') || prompt.toLowerCase().includes('ui') || prompt.toLowerCase().includes('component')) {
        mentionedServers.push('magic');
      }
      if (prompt.toLowerCase().includes('sequential') || prompt.toLowerCase().includes('think') || prompt.toLowerCase().includes('analyze')) {
        mentionedServers.push('sequential');
      }
      if (prompt.toLowerCase().includes('context7') || prompt.toLowerCase().includes('documentation') || prompt.toLowerCase().includes('library')) {
        mentionedServers.push('context7');
      }
      if (prompt.toLowerCase().includes('playwright') || prompt.toLowerCase().includes('test') || prompt.toLowerCase().includes('browser')) {
        mentionedServers.push('playwright');
      }
      
      if (mentionedServers.length > 0) {
        requiredMCPServers = [...new Set(mentionedServers)];
        console.log('[ClaudeCodeSession] Auto-detected MCP servers from prompt:', requiredMCPServers);
        // Don't override manually selected servers, just show what was auto-detected
        if (activeMCPServers.length === 0) {
          setActiveMCPServers(requiredMCPServers);
        }
      }
      
      // Merge with manually selected MCP servers
      if (activeMCPServers.length > 0) {
        requiredMCPServers = [...new Set([...requiredMCPServers, ...activeMCPServers])];
        console.log('[ClaudeCodeSession] Combined MCP servers (auto + manual):', requiredMCPServers);
      }
      
      if (detectedMode !== superClaudeMode) {
        setSuperClaudeMode(detectedMode);
        
        // Apply mode behavior
        effectivePrompt = await superClaudeService.applyModeBehavior(prompt, detectedMode);
      }
      
      if (activatedPersonas.length > 0 && activatedPersonas.join(',') !== superClaudePersonas.join(',')) {
        setSuperClaudePersonas(activatedPersonas);
      }
    }
    
    // Use the enhanced prompt for actual execution
    prompt = effectivePrompt;

    // If already loading, queue the prompt
    if (isLoading) {
      const newPrompt = {
        id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        prompt,
        model
      };
      setQueuedPrompts(prev => [...prev, newPrompt]);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      hasActiveSessionRef.current = true;
      
      // For resuming sessions, ensure we have the session ID
      if (effectiveSession && !claudeSessionId) {
        setClaudeSessionId(effectiveSession.id);
      }
      
      // Only clean up and set up new listeners if not already listening
      if (!isListeningRef.current) {
        // Clean up previous listeners
        unlistenRefs.current.forEach(unlisten => unlisten());
        unlistenRefs.current = [];
        
        // Mark as setting up listeners
        isListeningRef.current = true;
        
        // --------------------------------------------------------------------
        // 1️⃣  Event Listener Setup Strategy
        // --------------------------------------------------------------------
        // Claude Code may emit a *new* session_id even when we pass --resume. If
        // we listen only on the old session-scoped channel we will miss the
        // stream until the user navigates away & back. To avoid this we:
        //   • Always start with GENERIC listeners (no suffix) so we catch the
        //     very first "system:init" message regardless of the session id.
        //   • Once that init message provides the *actual* session_id, we
        //     dynamically switch to session-scoped listeners and stop the
        //     generic ones to prevent duplicate handling.
        // --------------------------------------------------------------------

        console.log('[ClaudeCodeSession] Setting up generic event listeners first');

        let currentSessionId: string | null = claudeSessionId || effectiveSession?.id || null;

        // Helper to attach session-specific listeners **once we are sure**
        const attachSessionSpecificListeners = async (sid: string) => {
          console.log('[ClaudeCodeSession] Attaching session-specific listeners for', sid);

          const specificOutputUnlisten = await listen<string>(`claude-output:${sid}`, (evt) => {
            handleStreamMessage(evt.payload);
          });

          const specificErrorUnlisten = await listen<string>(`claude-error:${sid}`, (evt) => {
            console.error('Claude error (scoped):', evt.payload);
            setError(evt.payload);
          });

          const specificCompleteUnlisten = await listen<boolean>(`claude-complete:${sid}`, (evt) => {
            console.log('[ClaudeCodeSession] Received claude-complete (scoped):', evt.payload);
            processComplete(evt.payload);
          });

          // Replace existing unlisten refs with these new ones (after cleaning up)
          unlistenRefs.current.forEach((u) => u());
          unlistenRefs.current = [specificOutputUnlisten, specificErrorUnlisten, specificCompleteUnlisten];
        };

        // Generic listeners (catch-all)
        const genericOutputUnlisten = await listen<string>('claude-output', async (event) => {
          handleStreamMessage(event.payload);

          // Attempt to extract session_id on the fly (for the very first init)
          try {
            const msg = JSON.parse(event.payload) as ClaudeStreamMessage;
            if (msg.type === 'system' && msg.subtype === 'init' && msg.session_id) {
              if (!currentSessionId || currentSessionId !== msg.session_id) {
                console.log('[ClaudeCodeSession] Detected new session_id from generic listener:', msg.session_id);
                currentSessionId = msg.session_id;
                setClaudeSessionId(msg.session_id);

                // If we haven't extracted session info before, do it now
                if (!extractedSessionInfo) {
                  const projectId = projectPath.replace(/[^a-zA-Z0-9]/g, '-');
                  setExtractedSessionInfo({ sessionId: msg.session_id, projectId });
                  
                  // Save session data for restoration
                  SessionPersistenceService.saveSession(
                    msg.session_id,
                    projectId,
                    projectPath,
                    messages.length
                  );
                }

                // Switch to session-specific listeners
                await attachSessionSpecificListeners(msg.session_id);
              }
            }
          } catch {
            /* ignore parse errors */
          }
        });

        // Helper to process any JSONL stream message string
        function handleStreamMessage(payload: string) {
          try {
            // Don't process if component unmounted
            if (!isMountedRef.current) return;
            
            // Store raw JSONL
            setRawJsonlOutput((prev) => [...prev, payload]);

            const message = JSON.parse(payload) as ClaudeStreamMessage;
            
            // Track enhanced tool execution
            if (message.type === 'assistant' && message.message?.content) {
              const toolUses = message.message.content.filter((c: any) => c.type === 'tool_use');
              toolUses.forEach((toolUse: any) => {
                // Increment tools executed counter
                sessionMetrics.current.toolsExecuted += 1;
                sessionMetrics.current.lastActivityTime = Date.now();
                
                // Track file operations
                const toolName = toolUse.name?.toLowerCase() || '';
                if (toolName.includes('create') || toolName.includes('write')) {
                  sessionMetrics.current.filesCreated += 1;
                } else if (toolName.includes('edit') || toolName.includes('multiedit') || toolName.includes('search_replace')) {
                  sessionMetrics.current.filesModified += 1;
                } else if (toolName.includes('delete')) {
                  sessionMetrics.current.filesDeleted += 1;
                }
                
                // Track tool start - we'll track completion when we get the result
                workflowTracking.trackStep(toolUse.name);
              });
            }
            
            // Track tool results
            if (message.type === 'user' && message.message?.content) {
              const toolResults = message.message.content.filter((c: any) => c.type === 'tool_result');
              toolResults.forEach((result: any) => {
                const isError = result.is_error || false;
                // Note: We don't have execution time here, but we can track success/failure
                if (isError) {
                  sessionMetrics.current.toolsFailed += 1;
                  sessionMetrics.current.errorsEncountered += 1;
                  
                  trackEvent.enhancedError({
                    error_type: 'tool_execution',
                    error_code: 'tool_failed',
                    error_message: result.content,
                    context: `Tool execution failed`,
                    user_action_before_error: 'executing_tool',
                    recovery_attempted: false,
                    recovery_successful: false,
                    error_frequency: 1,
                    stack_trace_hash: undefined
                  });
                }
              });
            }
            
            // Track code blocks generated
            if (message.type === 'assistant' && message.message?.content) {
              const codeBlocks = message.message.content.filter((c: any) => 
                c.type === 'text' && c.text?.includes('```')
              );
              if (codeBlocks.length > 0) {
                // Count code blocks in text content
                codeBlocks.forEach((block: any) => {
                  const matches = (block.text.match(/```/g) || []).length;
                  sessionMetrics.current.codeBlocksGenerated += Math.floor(matches / 2);
                });
              }
            }
            
            // Track errors in system messages
            if (message.type === 'system' && (message.subtype === 'error' || message.error)) {
              sessionMetrics.current.errorsEncountered += 1;
            }
            
            setMessages((prev) => [...prev, message]);
          } catch (err) {
            console.error('Failed to parse message:', err, payload);
          }
        }

        // Helper to handle completion events (both generic and scoped)
        const processComplete = async (success: boolean) => {
          setIsLoading(false);
          hasActiveSessionRef.current = false;
          isListeningRef.current = false; // Reset listening state
          
          // Track enhanced session stopped metrics when session completes
          if (effectiveSession && claudeSessionId) {
            const sessionStartTimeValue = messages.length > 0 ? messages[0].timestamp || Date.now() : Date.now();
            const duration = Date.now() - sessionStartTimeValue;
            const metrics = sessionMetrics.current;
            const timeToFirstMessage = metrics.firstMessageTime 
              ? metrics.firstMessageTime - sessionStartTime.current 
              : undefined;
            const idleTime = Date.now() - metrics.lastActivityTime;
            const avgResponseTime = metrics.toolExecutionTimes.length > 0
              ? metrics.toolExecutionTimes.reduce((a, b) => a + b, 0) / metrics.toolExecutionTimes.length
              : undefined;
            
            trackEvent.enhancedSessionStopped({
              // Basic metrics
              duration_ms: duration,
              messages_count: messages.length,
              reason: success ? 'completed' : 'error',
              
              // Timing metrics
              time_to_first_message_ms: timeToFirstMessage,
              average_response_time_ms: avgResponseTime,
              idle_time_ms: idleTime,
              
              // Interaction metrics
              prompts_sent: metrics.promptsSent,
              tools_executed: metrics.toolsExecuted,
              tools_failed: metrics.toolsFailed,
              files_created: metrics.filesCreated,
              files_modified: metrics.filesModified,
              files_deleted: metrics.filesDeleted,
              
              // Content metrics
              total_tokens_used: totalTokens,
              code_blocks_generated: metrics.codeBlocksGenerated,
              errors_encountered: metrics.errorsEncountered,
              
              // Session context
              model: metrics.modelChanges.length > 0 
                ? metrics.modelChanges[metrics.modelChanges.length - 1].to 
                : 'sonnet',
              has_checkpoints: metrics.checkpointCount > 0,
              checkpoint_count: metrics.checkpointCount,
              was_resumed: metrics.wasResumed,
              
              // Agent context (if applicable)
              agent_type: undefined, // TODO: Pass from agent execution
              agent_name: undefined, // TODO: Pass from agent execution
              agent_success: success,
              
              // Stop context
              stop_source: 'completed',
              final_state: success ? 'success' : 'failed',
              has_pending_prompts: queuedPrompts.length > 0,
              pending_prompts_count: queuedPrompts.length,
            });
          }

          if (effectiveSession && success) {
            try {
              const settings = await api.getCheckpointSettings(
                effectiveSession.id,
                effectiveSession.project_id,
                projectPath
              );

              if (settings.auto_checkpoint_enabled) {
                await api.checkAutoCheckpoint(
                  effectiveSession.id,
                  effectiveSession.project_id,
                  projectPath,
                  prompt
                );
                // Reload timeline to show new checkpoint
                setTimelineVersion((v) => v + 1);
              }
            } catch (err) {
              console.error('Failed to check auto checkpoint:', err);
            }
          }

          // Process queued prompts after completion
          if (queuedPromptsRef.current.length > 0) {
            const [nextPrompt, ...remainingPrompts] = queuedPromptsRef.current;
            setQueuedPrompts(remainingPrompts);
            
            // Small delay to ensure UI updates
            setTimeout(() => {
              handleSendPrompt(nextPrompt.prompt, nextPrompt.model);
            }, 100);
          }
        };

        const genericErrorUnlisten = await listen<string>('claude-error', (evt) => {
          console.error('Claude error:', evt.payload);
          setError(evt.payload);
        });

        const genericCompleteUnlisten = await listen<boolean>('claude-complete', (evt) => {
          console.log('[ClaudeCodeSession] Received claude-complete (generic):', evt.payload);
          processComplete(evt.payload);
        });

        // Store the generic unlisteners for now; they may be replaced later.
        unlistenRefs.current = [genericOutputUnlisten, genericErrorUnlisten, genericCompleteUnlisten];

        // --------------------------------------------------------------------
        // 2️⃣  Auto-checkpoint logic moved after listener setup (unchanged)
        // --------------------------------------------------------------------

        // Add the user message immediately to the UI (after setting up listeners)
        const userMessage: ClaudeStreamMessage = {
          type: "user",
          message: {
            content: [
              {
                type: "text",
                text: prompt
              }
            ]
          },
          // Add SuperClaude context if a command was detected
          superClaudeContext: superClaudeCommandContext ? {
            command: superClaudeCommandContext.command,
            mode: detectedMode,
            personas: activatedPersonas,
            mcpServers: requiredMCPServers,
            waveStrategy: superClaudeCommandContext.waveStrategy,
            flags: superClaudeCommandContext.flags,
            arguments: superClaudeCommandContext.arguments
          } : undefined
        } as ClaudeStreamMessage;
        
        // Log SuperClaude context attachment
        if (superClaudeCommandContext) {
          console.log('[ClaudeCodeSession] SuperClaude context attached to user message:', {
            command: superClaudeCommandContext.command,
            mode: detectedMode,
            personas: activatedPersonas,
            mcpServers: requiredMCPServers,
            waveStrategy: superClaudeCommandContext.waveStrategy,
            hasContext: true
          });
        }
        
        setMessages(prev => [...prev, userMessage]);
        
        // Update session metrics
        sessionMetrics.current.promptsSent += 1;
        sessionMetrics.current.lastActivityTime = Date.now();
        if (!sessionMetrics.current.firstMessageTime) {
          sessionMetrics.current.firstMessageTime = Date.now();
        }
        
        // Track model changes
        const lastModel = sessionMetrics.current.modelChanges.length > 0 
          ? sessionMetrics.current.modelChanges[sessionMetrics.current.modelChanges.length - 1].to
          : (sessionMetrics.current.wasResumed ? 'sonnet' : model); // Default to sonnet if resumed
        
        if (lastModel !== model) {
          sessionMetrics.current.modelChanges.push({
            from: lastModel,
            to: model,
            timestamp: Date.now()
          });
        }
        
        // Track enhanced prompt submission
        const codeBlockMatches = prompt.match(/```[\s\S]*?```/g) || [];
        const hasCode = codeBlockMatches.length > 0;
        const conversationDepth = messages.filter(m => m.user_message).length;
        const sessionAge = sessionStartTime.current ? Date.now() - sessionStartTime.current : 0;
        const wordCount = prompt.split(/\s+/).filter(word => word.length > 0).length;
        
        trackEvent.enhancedPromptSubmitted({
          prompt_length: prompt.length,
          model: model,
          has_attachments: false, // TODO: Add attachment support when implemented
          source: 'keyboard', // TODO: Track actual source (keyboard vs button)
          word_count: wordCount,
          conversation_depth: conversationDepth,
          prompt_complexity: wordCount < 20 ? 'simple' : wordCount < 100 ? 'moderate' : 'complex',
          contains_code: hasCode,
          language_detected: hasCode ? codeBlockMatches?.[0]?.match(/```(\w+)/)?.[1] : undefined,
          session_age_ms: sessionAge
        });

        // Execute the appropriate command
        if (effectiveSession && !isFirstPrompt) {
          console.log('[ClaudeCodeSession] Resuming session:', effectiveSession.id);
          console.log('[ClaudeCodeSession] Passing MCP servers to resume:', requiredMCPServers);
          trackEvent.sessionResumed(effectiveSession.id);
          trackEvent.modelSelected(model);
          await api.resumeClaudeCode(projectPath, effectiveSession.id, prompt, model, requiredMCPServers);
        } else {
          // Check if this is a business panel command that should be handled directly
          if (superClaudeCommandContext?.command === 'business-panel') {
            console.log('[ClaudeCodeSession] Handling business panel command directly');
            
            // Extract content from the prompt (everything after the command)
            const content = superClaudeCommandContext.arguments || prompt.replace(/^\/sc:business-panel\s*/, '').trim();
            
            // Extract mode from flags
            let mode = 'discussion';
            if (superClaudeCommandContext.flags?.includes('mode')) {
              const modeIndex = superClaudeCommandContext.flags.indexOf('mode');
              if (modeIndex + 1 < superClaudeCommandContext.flags.length) {
                mode = superClaudeCommandContext.flags[modeIndex + 1];
              }
            } else if (prompt.includes('--mode')) {
              const modeMatch = prompt.match(/--mode\s+(\w+)/);
              if (modeMatch) {
                mode = modeMatch[1];
              }
            }
            
            try {
              // Execute business panel analysis with error handling
              const { invoke } = await import('@tauri-apps/api/core');
              
              // Update progress: Creating session
              setBusinessPanelProgress({
                stage: 'creating-session',
                progress: 10,
                message: 'Creating business panel session...'
              });
              
              let sessionResponse: any;
              try {
                sessionResponse = await invoke('create_business_panel_session', {
                  content,
                  mode: mode || 'discussion',
                  selectedExperts: undefined, // Will use default experts
                });
                
                // Update progress: Session created
                setBusinessPanelProgress({
                  stage: 'analyzing',
                  progress: 30,
                  message: 'Session created, starting analysis...'
                });
              } catch (sessionError) {
                setBusinessPanelProgress({
                  stage: 'error',
                  progress: 0,
                  message: 'Failed to create session'
                });
                console.error("Failed to create business panel session:", sessionError);
                throw new Error(`Failed to create business panel session: ${sessionError instanceof Error ? sessionError.message : 'Unknown error'}`);
              }
              
              // Add user message for the business panel command
              const userMessage: ClaudeStreamMessage = {
                type: "user",
                message: {
                  content: [
                    {
                      type: "text",
                      text: prompt
                    }
                  ]
                },
                superClaudeContext: superClaudeCommandContext ? {
                  command: superClaudeCommandContext.command,
                  mode: detectedMode,
                  personas: activatedPersonas,
                  mcpServers: requiredMCPServers,
                  waveStrategy: superClaudeCommandContext.waveStrategy,
                  flags: superClaudeCommandContext.flags,
                  arguments: superClaudeCommandContext.arguments
                } : undefined
              } as ClaudeStreamMessage;
              
              setMessages(prev => [...prev, userMessage]);
              
              // Add assistant message indicating tool use
              const toolUseMessage: ClaudeStreamMessage = {
                type: "assistant",
                message: {
                  content: [
                    {
                      type: "text",
                      text: "I'll analyze this using the business panel tool."
                    },
                    {
                      type: "tool_use",
                      id: `business_panel_${Date.now()}`,
                      name: "business_panel",
                      input: {
                        content,
                        mode,
                        session_id: sessionResponse.id
                      }
                    }
                  ]
                }
              } as ClaudeStreamMessage;
              
              setMessages(prev => [...prev, toolUseMessage]);
              
              // Execute the business panel analysis with retry logic
              let analyses: any[] = [];
              let retryCount = 0;
              const maxRetries = 2;
              
              // Update progress: Analyzing
              setBusinessPanelProgress({
                stage: 'analyzing',
                progress: 50,
                message: 'Experts analyzing content...'
              });
              
              while (retryCount <= maxRetries) {
                try {
                  analyses = await invoke('execute_business_analysis', {
                    sessionId: sessionResponse.id,
                  });
                  
                  // Update progress: Synthesizing
                  setBusinessPanelProgress({
                    stage: 'synthesizing',
                    progress: 80,
                    message: 'Synthesizing expert insights...'
                  });
                  
                  break; // Success, exit retry loop
                } catch (analysisError) {
                  retryCount++;
                  console.error(`Business panel analysis attempt ${retryCount} failed:`, analysisError);
                  
                  if (retryCount > maxRetries) {
                    // All retries exhausted
                    setBusinessPanelProgress({
                      stage: 'error',
                      progress: 0,
                      message: 'Analysis failed after retries'
                    });
                    const errorMessage = analysisError instanceof Error ? analysisError.message : 'Unknown error';
                    throw new Error(`Failed to execute business panel analysis after ${maxRetries} retries: ${errorMessage}`);
                  }
                  
                  // Update progress: Retrying
                  setBusinessPanelProgress({
                    stage: 'analyzing',
                    progress: 40 + (retryCount * 10),
                    message: `Retrying analysis (attempt ${retryCount + 1})...`
                  });
                  
                  // Wait before retrying (exponential backoff)
                  await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000));
                }
              }
              
              // Add tool result message
              const toolResultMessage: ClaudeStreamMessage = {
                type: "user",
                message: {
                  content: [
                    {
                      type: "tool_result",
                      tool_use_id: `business_panel_${Date.now()}`,
                      content: JSON.stringify({
                        analyses,
                        mode,
                        content
                      }),
                      is_error: false
                    }
                  ]
                }
              } as ClaudeStreamMessage;
              
              setMessages(prev => [...prev, toolResultMessage]);
              
              // Update progress: Complete
              setBusinessPanelProgress({
                stage: 'complete',
                progress: 100,
                message: 'Analysis complete!'
              });
              
              // Reset progress after a delay
              setTimeout(() => {
                setBusinessPanelProgress({
                  stage: 'idle',
                  progress: 0,
                  message: ''
                });
              }, 3000);
              
              // Reset loading state
              setIsLoading(false);
              hasActiveSessionRef.current = false;
              isListeningRef.current = false;
              
              // Process any queued prompts
              if (queuedPromptsRef.current.length > 0) {
                const [nextPrompt, ...remainingPrompts] = queuedPromptsRef.current;
                setQueuedPrompts(remainingPrompts);
                
                // Small delay to ensure UI updates
                setTimeout(() => {
                  handleSendPrompt(nextPrompt.prompt, nextPrompt.model);
                }, 100);
              }
              
            } catch (err) {
              console.error("Failed to execute business panel analysis:", err);
              
              // Provide detailed error message
              const errorMessage = err instanceof Error ? err.message : "Unknown error occurred";
              setError(`Business Panel Error: ${errorMessage}`);
              
              // Add error message to the chat
              const errorToolResult: ClaudeStreamMessage = {
                type: "user",
                message: {
                  content: [
                    {
                      type: "tool_result",
                      tool_use_id: `business_panel_error_${Date.now()}`,
                      content: `Error: ${errorMessage}\n\nThe business panel analysis could not be completed. You can try again or proceed with a different approach.`,
                      is_error: true
                    }
                  ]
                }
              } as ClaudeStreamMessage;
              
              setMessages(prev => [...prev, errorToolResult]);
              
              // Track error in analytics
              trackEvent.featureUsed('business_panel', 'error', {
                error: errorMessage,
                mode,
                content_length: content?.length || 0
              });
              
              setIsLoading(false);
              hasActiveSessionRef.current = false;
              isListeningRef.current = false;
            }
          } else {
            console.log('[ClaudeCodeSession] Starting new session');
            console.log('[ClaudeCodeSession] Passing MCP servers to execute:', requiredMCPServers);
            setIsFirstPrompt(false);
            trackEvent.sessionCreated(model, 'prompt_input');
            trackEvent.modelSelected(model);
            await api.executeClaudeCode(projectPath, prompt, model, requiredMCPServers);
          }
        }
      }
    } catch (err) {
      console.error("Failed to send prompt:", err);
      setError("Failed to send prompt");
      setIsLoading(false);
      hasActiveSessionRef.current = false;
    }
  };

  const handleCopyAsJsonl = async () => {
    const jsonl = rawJsonlOutput.join('\n');
    await navigator.clipboard.writeText(jsonl);
    setCopyPopoverOpen(false);
  };

  const handleCopyAsMarkdown = async () => {
    let markdown = `# Claude Code Session\n\n`;
    markdown += `**Project:** ${projectPath}\n`;
    markdown += `**Date:** ${new Date().toISOString()}\n\n`;
    markdown += `---\n\n`;

    for (const msg of messages) {
      if (msg.type === "system" && msg.subtype === "init") {
        markdown += `## System Initialization\n\n`;
        markdown += `- Session ID: \`${msg.session_id || 'N/A'}\`\n`;
        markdown += `- Model: \`${msg.model || 'default'}\`\n`;
        if (msg.cwd) markdown += `- Working Directory: \`${msg.cwd}\`\n`;
        if (msg.tools?.length) markdown += `- Tools: ${msg.tools.join(', ')}\n`;
        markdown += `\n`;
      } else if (msg.type === "assistant" && msg.message) {
        markdown += `## Assistant\n\n`;
        for (const content of msg.message.content || []) {
          if (content.type === "text") {
            const textContent = typeof content.text === 'string' 
              ? content.text 
              : (content.text?.text || JSON.stringify(content.text || content));
            markdown += `${textContent}\n\n`;
          } else if (content.type === "tool_use") {
            markdown += `### Tool: ${content.name}\n\n`;
            markdown += `\`\`\`json\n${JSON.stringify(content.input, null, 2)}\n\`\`\`\n\n`;
          }
        }
        if (msg.message.usage) {
          markdown += `*Tokens: ${msg.message.usage.input_tokens} in, ${msg.message.usage.output_tokens} out*\n\n`;
        }
      } else if (msg.type === "user" && msg.message) {
        markdown += `## User\n\n`;
        for (const content of msg.message.content || []) {
          if (content.type === "text") {
            const textContent = typeof content.text === 'string' 
              ? content.text 
              : (content.text?.text || JSON.stringify(content.text));
            markdown += `${textContent}\n\n`;
          } else if (content.type === "tool_result") {
            markdown += `### Tool Result\n\n`;
            let contentText = '';
            if (typeof content.content === 'string') {
              contentText = content.content;
            } else if (content.content && typeof content.content === 'object') {
              if (content.content.text) {
                contentText = content.content.text;
              } else if (Array.isArray(content.content)) {
                contentText = content.content
                  .map((c: any) => (typeof c === 'string' ? c : c.text || JSON.stringify(c)))
                  .join('\n');
              } else {
                contentText = JSON.stringify(content.content, null, 2);
              }
            }
            markdown += `\`\`\`\n${contentText}\n\`\`\`\n\n`;
          }
        }
      } else if (msg.type === "result") {
        markdown += `## Execution Result\n\n`;
        if (msg.result) {
          markdown += `${msg.result}\n\n`;
        }
        if (msg.error) {
          markdown += `**Error:** ${msg.error}\n\n`;
        }
      }
    }

    await navigator.clipboard.writeText(markdown);
    setCopyPopoverOpen(false);
  };

  const handleCheckpointSelect = async () => {
    // Reload messages from the checkpoint
    await loadSessionHistory();
    // Ensure timeline reloads to highlight current checkpoint
    setTimelineVersion((v) => v + 1);
  };
  
  const handleCheckpointCreated = () => {
    // Update checkpoint count in session metrics
    sessionMetrics.current.checkpointCount += 1;
  };

  const handleCancelExecution = async () => {
    if (!claudeSessionId || !isLoading) return;
    
    try {
      const sessionStartTime = messages.length > 0 ? messages[0].timestamp || Date.now() : Date.now();
      const duration = Date.now() - sessionStartTime;
      
      await api.cancelClaudeExecution(claudeSessionId);
      
      // Calculate metrics for enhanced analytics
      const metrics = sessionMetrics.current;
      const timeToFirstMessage = metrics.firstMessageTime 
        ? metrics.firstMessageTime - sessionStartTime.current 
        : undefined;
      const idleTime = Date.now() - metrics.lastActivityTime;
      const avgResponseTime = metrics.toolExecutionTimes.length > 0
        ? metrics.toolExecutionTimes.reduce((a, b) => a + b, 0) / metrics.toolExecutionTimes.length
        : undefined;
      
      // Track enhanced session stopped
      trackEvent.enhancedSessionStopped({
        // Basic metrics
        duration_ms: duration,
        messages_count: messages.length,
        reason: 'user_stopped',
        
        // Timing metrics
        time_to_first_message_ms: timeToFirstMessage,
        average_response_time_ms: avgResponseTime,
        idle_time_ms: idleTime,
        
        // Interaction metrics
        prompts_sent: metrics.promptsSent,
        tools_executed: metrics.toolsExecuted,
        tools_failed: metrics.toolsFailed,
        files_created: metrics.filesCreated,
        files_modified: metrics.filesModified,
        files_deleted: metrics.filesDeleted,
        
        // Content metrics
        total_tokens_used: totalTokens,
        code_blocks_generated: metrics.codeBlocksGenerated,
        errors_encountered: metrics.errorsEncountered,
        
        // Session context
        model: metrics.modelChanges.length > 0 
          ? metrics.modelChanges[metrics.modelChanges.length - 1].to 
          : 'sonnet', // Default to sonnet
        has_checkpoints: metrics.checkpointCount > 0,
        checkpoint_count: metrics.checkpointCount,
        was_resumed: metrics.wasResumed,
        
        // Agent context (if applicable)
        agent_type: undefined, // TODO: Pass from agent execution
        agent_name: undefined, // TODO: Pass from agent execution
        agent_success: undefined, // TODO: Pass from agent execution
        
        // Stop context
        stop_source: 'user_button',
        final_state: 'cancelled',
        has_pending_prompts: queuedPrompts.length > 0,
        pending_prompts_count: queuedPrompts.length,
      });
      
      // Clean up listeners
      unlistenRefs.current.forEach(unlisten => unlisten());
      unlistenRefs.current = [];
      
      // Reset states
      setIsLoading(false);
      hasActiveSessionRef.current = false;
      isListeningRef.current = false;
      setError(null);
      
      // Clear queued prompts
      setQueuedPrompts([]);
      
      // Add a message indicating the session was cancelled
      const cancelMessage: ClaudeStreamMessage = {
        type: "system",
        subtype: "info",
        result: "Session cancelled by user",
        timestamp: new Date().toISOString()
      };
      setMessages(prev => [...prev, cancelMessage]);
    } catch (err) {
      console.error("Failed to cancel execution:", err);
      
      // Even if backend fails, we should update UI to reflect stopped state
      // Add error message but still stop the UI loading state
      const errorMessage: ClaudeStreamMessage = {
        type: "system",
        subtype: "error",
        result: `Failed to cancel execution: ${err instanceof Error ? err.message : 'Unknown error'}. The process may still be running in the background.`,
        timestamp: new Date().toISOString()
      };
      setMessages(prev => [...prev, errorMessage]);
      
      // Clean up listeners anyway
      unlistenRefs.current.forEach(unlisten => unlisten());
      unlistenRefs.current = [];
      
      // Reset states to allow user to continue
      setIsLoading(false);
      hasActiveSessionRef.current = false;
      isListeningRef.current = false;
      setError(null);
    }
  };

  const handleFork = (checkpointId: string) => {
    setForkCheckpointId(checkpointId);
    setForkSessionName(`Fork-${new Date().toISOString().slice(0, 10)}`);
    setShowForkDialog(true);
  };

  const handleConfirmFork = async () => {
    if (!forkCheckpointId || !forkSessionName.trim() || !effectiveSession) return;
    
    try {
      setIsLoading(true);
      setError(null);
      
      const newSessionId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      await api.forkFromCheckpoint(
        forkCheckpointId,
        effectiveSession.id,
        effectiveSession.project_id,
        projectPath,
        newSessionId,
        forkSessionName
      );
      
      // Open the new forked session
      // You would need to implement navigation to the new session
      console.log("Forked to new session:", newSessionId);
      
      setShowForkDialog(false);
      setForkCheckpointId(null);
      setForkSessionName("");
    } catch (err) {
      console.error("Failed to fork checkpoint:", err);
      setError("Failed to fork checkpoint");
    } finally {
      setIsLoading(false);
    }
  };

  // Handle SuperClaude command selection
  const handleSuperClaudeCommandSelect = (command: SuperClaudeCommand) => {
    setShowSuperClaudeCommands(false);
    
    // Format the command for the prompt input
    const commandText = `/sc:${command.name} `;
    
    // Add the command to the floating prompt input
    if (floatingPromptRef.current) {
      floatingPromptRef.current.setValue(commandText);
      floatingPromptRef.current.focus();
    } else {
      // Fallback: send directly if no ref available
      handleSendPrompt(commandText, "sonnet");
    }
  };

  // Handle URL detection from terminal output
  const handleLinkDetected = (url: string) => {
    if (!showPreview && !showPreviewPrompt) {
      setPreviewUrl(url);
      setShowPreviewPrompt(true);
    }
  };

  const handleClosePreview = () => {
    setShowPreview(false);
    setIsPreviewMaximized(false);
    // Keep the previewUrl so it can be restored when reopening
  };

  const handlePreviewUrlChange = (url: string) => {
    console.log('[ClaudeCodeSession] Preview URL changed to:', url);
    setPreviewUrl(url);
  };

  const handleTogglePreviewMaximize = () => {
    setIsPreviewMaximized(!isPreviewMaximized);
    // Reset split position when toggling maximize
    if (isPreviewMaximized) {
      setSplitPosition(50);
    }
  };

  // Cleanup event listeners and track mount state
  useEffect(() => {
    isMountedRef.current = true;
    
    return () => {
      console.log('[ClaudeCodeSession] Component unmounting, cleaning up listeners');
      isMountedRef.current = false;
      isListeningRef.current = false;
      
      // Track session completion with engagement metrics
      if (effectiveSession) {
        trackEvent.sessionCompleted();
        
        // Track session engagement
        const sessionDuration = sessionStartTime.current ? Date.now() - sessionStartTime.current : 0;
        const messageCount = messages.filter(m => m.user_message).length;
        const toolsUsed = new Set<string>();
        messages.forEach(msg => {
          if (msg.type === 'assistant' && msg.message?.content) {
            const tools = msg.message.content.filter((c: any) => c.type === 'tool_use');
            tools.forEach((tool: any) => toolsUsed.add(tool.name));
          }
        });
        
        // Calculate engagement score (0-100)
        const engagementScore = Math.min(100, 
          (messageCount * 10) + 
          (toolsUsed.size * 5) + 
          (sessionDuration > 300000 ? 20 : sessionDuration / 15000) // 5+ min session gets 20 points
        );
        
        trackEvent.sessionEngagement({
          session_duration_ms: sessionDuration,
          messages_sent: messageCount,
          tools_used: Array.from(toolsUsed),
          files_modified: 0, // TODO: Track file modifications
          engagement_score: Math.round(engagementScore)
        });
      }
      
      // Clean up listeners
      unlistenRefs.current.forEach(unlisten => unlisten());
      unlistenRefs.current = [];
      
      // Clear checkpoint manager when session ends
      if (effectiveSession) {
        api.clearCheckpointManager(effectiveSession.id).catch(err => {
          console.error("Failed to clear checkpoint manager:", err);
        });
      }
    };
  }, [effectiveSession, projectPath]);

  // Handle brainstorming mode
  const handleStartBrainstorming = (topic: string) => {
    setBrainstormingTopic(topic);
    setShowBrainstormingView(true);
    // Clear messages when starting brainstorming
    setMessages([]);
  };

  const handleEndBrainstorming = () => {
    setShowBrainstormingView(false);
    setBrainstormingTopic(null);
  };

  const handleIdeaSubmit = (idea: string, category: string) => {
    // In a real implementation, this would add the idea to the session
    // For now, we'll just add it to the messages as a user message
    const userMessage: ClaudeStreamMessage = {
      type: "user",
      message: {
        content: [
          {
            type: "text",
            text: `Idea: ${idea} (Category: ${category})`
          }
        ]
      }
    };
    setMessages(prev => [...prev, userMessage]);
  };

  const messagesList = (
    <div
      ref={parentRef}
      className="flex-1 overflow-y-auto relative pb-40"
      style={{
        contain: 'strict',
      }}
    >
      {showBrainstormingView && brainstormingTopic ? (
        <div className="p-4">
          <div className="max-w-6xl mx-auto">
            <Button
              variant="outline"
              onClick={handleEndBrainstorming}
              className="mb-4"
            >
              Back to Normal Mode
            </Button>
            <BrainstormingView
              topic={brainstormingTopic}
              onIdeaSubmit={handleIdeaSubmit}
            />
          </div>
        </div>
      ) : superClaudeMode === 'introspection' && claudeSessionId ? (
        <div className="p-4">
          <div className="max-w-6xl mx-auto">
            <Button
              variant="outline"
              onClick={() => setSuperClaudeMode('normal')}
              className="mb-4"
            >
              <Brain className="h-4 w-4 mr-2" />
              Back to Normal Mode
            </Button>
            <IntrospectionView
              sessionId={claudeSessionId}
              onAnalyzeSession={() => {
                // Trigger introspection analysis
                handleSendPrompt("Analyze my current session and provide insights on my reasoning patterns, decision-making, and areas for improvement.", "sonnet");
              }}
            />
          </div>
        </div>
      ) : (
        <>
          <div
            className="relative w-full max-w-6xl mx-auto px-4 pt-8 pb-4"
            style={{
              height: `${Math.max(rowVirtualizer.getTotalSize(), 100)}px`,
              minHeight: '100px',
            }}
          >
            <AnimatePresence>
              {rowVirtualizer.getVirtualItems().map((virtualItem) => {
                const message = displayableMessages[virtualItem.index];
                return (
                  <motion.div
                    key={virtualItem.key}
                    data-index={virtualItem.index}
                    ref={(el) => el && rowVirtualizer.measureElement(el)}
                    initial={{ opacity: 0, y: 8 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -8 }}
                    transition={{ duration: 0.3 }}
                    className="absolute inset-x-4 pb-4"
                    style={{
                      top: virtualItem.start,
                    }}
                  >
                    <StreamMessage
                      message={message}
                      streamMessages={messages}
                      onLinkDetected={handleLinkDetected}
                    />
                  </motion.div>
                );
              })}
            </AnimatePresence>
          </div>

          {/* Loading indicator under the latest message */}
          {isLoading && (
            <motion.div
              initial={{ opacity: 0, y: 8 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.15 }}
              className="flex items-center justify-center py-4 mb-40"
            >
              <div className="rotating-symbol text-primary" />
            </motion.div>
          )}

          {/* Error indicator */}
          {error && (
            <motion.div
              initial={{ opacity: 0, y: 8 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.15 }}
              className="rounded-lg border border-destructive/50 bg-destructive/10 p-4 text-sm text-destructive mb-40 w-full max-w-6xl mx-auto"
            >
              {error}
            </motion.div>
          )}
        </>
      )}
    </div>
  );

  const projectPathInput = null; // Removed project path display

  // If preview is maximized, render only the WebviewPreview in full screen
  if (showPreview && isPreviewMaximized) {
    return (
      <AnimatePresence>
        <motion.div 
          className="fixed inset-0 z-50 bg-background"
          initial={{ opacity: 0, y: 8 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
        >
          <WebviewPreview
            initialUrl={previewUrl}
            onClose={handleClosePreview}
            isMaximized={isPreviewMaximized}
            onToggleMaximize={handleTogglePreviewMaximize}
            onUrlChange={handlePreviewUrlChange}
            className="h-full"
          />
        </motion.div>
      </AnimatePresence>
    );
  }

  return (
    <TooltipProvider>
      <div className={cn("flex flex-col h-full bg-background", className)}>
        <div className="w-full h-full flex flex-col">

        {/* Agent Activation Notification */}
        {showAgentNotification && (
          <AgentNotification
            agents={notificationAgents}
            onClose={() => setShowAgentNotification(false)}
          />
        )}

        {/* SuperClaude Mode Indicator */}
        {(superClaudeMode !== "normal" || superClaudePersonas.length > 0 || tokenEfficiencyEnabled || businessPanelActive) && (
          <div className="absolute top-4 left-4 z-40">
            <SuperClaudeModeIndicator
              mode={superClaudeMode}
              personas={superClaudePersonas}
              tokenEfficiency={tokenEfficiencyEnabled}
              businessPanel={businessPanelActive}
            />
          </div>
        )}

        {/* Introspection Mode Button */}
        {effectiveSession && superClaudeMode !== "introspection" && (
          <div className="absolute top-4 right-4 z-40">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSuperClaudeMode("introspection")}
              className="flex items-center gap-2"
            >
              <Brain className="h-4 w-4" />
              Introspect
            </Button>
          </div>
        )}

        {/* Main Content Area */}
        <div className={cn(
          "flex-1 overflow-hidden transition-all duration-300",
          showTimeline && "sm:mr-96"
        )}>
          {showPreview ? (
            // Split pane layout when preview is active
            <SplitPane
              left={
                <div className="h-full flex flex-col">
                  {projectPathInput}
                  {messagesList}
                </div>
              }
              right={
                <WebviewPreview
                  initialUrl={previewUrl}
                  onClose={handleClosePreview}
                  isMaximized={isPreviewMaximized}
                  onToggleMaximize={handleTogglePreviewMaximize}
                  onUrlChange={handlePreviewUrlChange}
                />
              }
              initialSplit={splitPosition}
              onSplitChange={setSplitPosition}
              minLeftWidth={400}
              minRightWidth={400}
              className="h-full"
            />
          ) : (
            // Original layout when no preview
            <div className="h-full flex flex-col max-w-6xl mx-auto px-6">
              {projectPathInput}
              {messagesList}
              
              {isLoading && messages.length === 0 && (
                <div className="flex items-center justify-center h-full">
                  <div className="flex items-center gap-3">
                    <div className="rotating-symbol text-primary" />
                    <span className="text-sm text-muted-foreground">
                      {session ? "Loading session history..." : "Initializing Claude Code..."}
                    </span>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Floating Prompt Input - Always visible */}
        <ErrorBoundary>
          {/* Queued Prompts Display */}
          <AnimatePresence>
            {queuedPrompts.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 20 }}
                className="fixed bottom-24 left-1/2 -translate-x-1/2 z-30 w-full max-w-3xl px-4"
              >
                <div className="bg-background/95 backdrop-blur-md border rounded-lg shadow-lg p-3 space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="text-xs font-medium text-muted-foreground mb-1">
                      Queued Prompts ({queuedPrompts.length})
                    </div>
                    <TooltipSimple content={queuedPromptsCollapsed ? "Expand queue" : "Collapse queue"} side="top">
                      <motion.div
                        whileTap={{ scale: 0.97 }}
                        transition={{ duration: 0.15 }}
                      >
                        <Button variant="ghost" size="icon" onClick={() => setQueuedPromptsCollapsed(prev => !prev)}>
                          {queuedPromptsCollapsed ? <ChevronUp className="h-3 w-3" /> : <ChevronDown className="h-3 w-3" />}
                        </Button>
                      </motion.div>
                    </TooltipSimple>
                  </div>
                  {!queuedPromptsCollapsed && queuedPrompts.map((queuedPrompt, index) => (
                    <motion.div
                      key={queuedPrompt.id}
                      initial={{ opacity: 0, y: 4 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -4 }}
                      transition={{ duration: 0.15, delay: index * 0.02 }}
                      className="flex items-start gap-2 bg-muted/50 rounded-md p-2"
                    >
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="text-xs font-medium text-muted-foreground">#{index + 1}</span>
                          <span className="text-xs px-1.5 py-0.5 bg-primary/10 text-primary rounded">
                            {queuedPrompt.model === "opus" ? "Opus" : "Sonnet"}
                          </span>
                        </div>
                        <p className="text-sm line-clamp-2 break-words">{queuedPrompt.prompt}</p>
                      </div>
                      <motion.div
                        whileTap={{ scale: 0.97 }}
                        transition={{ duration: 0.15 }}
                      >
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6 flex-shrink-0"
                          onClick={() => setQueuedPrompts(prev => prev.filter(p => p.id !== queuedPrompt.id))}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </motion.div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Navigation Arrows - positioned above prompt bar with spacing */}
          {displayableMessages.length > 5 && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ delay: 0.5 }}
              className="fixed bottom-32 right-6 z-50"
            >
              <div className="flex items-center bg-background/95 backdrop-blur-md border rounded-full shadow-lg overflow-hidden">
                <TooltipSimple content="Scroll to top" side="top">
                  <motion.div
                    whileTap={{ scale: 0.97 }}
                    transition={{ duration: 0.15 }}
                  >
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                      // Use virtualizer to scroll to the first item
                      if (displayableMessages.length > 0) {
                        // Scroll to top of the container
                        parentRef.current?.scrollTo({
                          top: 0,
                          behavior: 'smooth'
                        });
                        
                        // After smooth scroll completes, trigger a small scroll to ensure rendering
                        setTimeout(() => {
                          if (parentRef.current) {
                            // Scroll down 1px then back to 0 to trigger virtualizer update
                            parentRef.current.scrollTop = 1;
                            requestAnimationFrame(() => {
                              if (parentRef.current) {
                                parentRef.current.scrollTop = 0;
                              }
                            });
                          }
                        }, 500); // Wait for smooth scroll to complete
                      }
                    }}
                      className="px-3 py-2 hover:bg-accent rounded-none"
                    >
                      <ChevronUp className="h-4 w-4" />
                    </Button>
                  </motion.div>
                </TooltipSimple>
                <div className="w-px h-4 bg-border" />
                <TooltipSimple content="Scroll to bottom" side="top">
                  <motion.div
                    whileTap={{ scale: 0.97 }}
                    transition={{ duration: 0.15 }}
                  >
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                      // Use virtualizer to scroll to the last item
                      if (displayableMessages.length > 0) {
                        // Scroll to bottom of the container
                        const scrollElement = parentRef.current;
                        if (scrollElement) {
                          scrollElement.scrollTo({
                            top: scrollElement.scrollHeight,
                            behavior: 'smooth'
                          });
                        }
                      }
                    }}
                      className="px-3 py-2 hover:bg-accent rounded-none"
                    >
                      <ChevronDown className="h-4 w-4" />
                    </Button>
                  </motion.div>
                </TooltipSimple>
              </div>
            </motion.div>
          )}

          <div className={cn(
            "fixed bottom-0 left-0 right-0 transition-all duration-300 z-50",
            showTimeline && "sm:right-96"
          )}>
            <FloatingPromptInput
              ref={floatingPromptRef}
              onSend={handleSendPrompt}
              onCancel={handleCancelExecution}
              isLoading={isLoading}
              disabled={!projectPath}
              projectPath={projectPath}
              extraMenuItems={
                <>
                  {/* MCP Server Selector */}
                  <MCPServerSelector
                    selectedServers={activeMCPServers}
                    onServersChange={setActiveMCPServers}
                    className="mr-2"
                  />
                  
                  {/* SuperClaude Commands Button */}
                  <TooltipSimple content="SuperClaude Commands" side="top">
                    <motion.div
                      whileTap={{ scale: 0.97 }}
                      transition={{ duration: 0.15 }}
                    >
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => setShowSuperClaudeCommands(true)}
                        className="h-9 w-9 text-muted-foreground hover:text-foreground"
                      >
                        <Sparkles className={cn("h-3.5 w-3.5", superClaudeMode !== "normal" && "text-primary")} />
                      </Button>
                    </motion.div>
                  </TooltipSimple>

                  {/* SuperClaude Help Button */}
                  <TooltipSimple content="SuperClaude Help (Cmd+Shift+H)">
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.97 }}
                      transition={{ duration: 0.15 }}
                    >
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => setShowSuperClaudeHelp(true)}
                        className="h-9 w-9 text-muted-foreground hover:text-foreground"
                      >
                        <HelpCircle className="h-3.5 w-3.5" />
                      </Button>
                    </motion.div>
                  </TooltipSimple>

                  {/* Quick Reference Button */}
                  <TooltipSimple content="Quick Reference (Cmd+?)">
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.97 }}
                      transition={{ duration: 0.15 }}
                    >
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => setShowQuickReference(!showQuickReference)}
                        className="h-9 w-9 text-muted-foreground hover:text-foreground"
                      >
                        <Keyboard className="h-3.5 w-3.5" />
                      </Button>
                    </motion.div>
                  </TooltipSimple>

                  {/* SuperClaude Mode Selector */}
                  <SuperClaudeModeSelector
                    currentMode={superClaudeMode}
                    onModeChange={(mode) => {
                      setSuperClaudeMode(mode);
                      superClaudeService.updateSessionState({
                        activeMode: mode,
                        activePersonas: superClaudePersonas,
                        tokenEfficiencyEnabled,
                        businessPanelActive,
                        mcpServersActive: [],
                      });
                    }}
                  />
                  
                  {effectiveSession && (
                    <TooltipSimple content="Session Timeline" side="top">
                      <motion.div
                        whileTap={{ scale: 0.97 }}
                        transition={{ duration: 0.15 }}
                      >
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => setShowTimeline(!showTimeline)}
                          className="h-9 w-9 text-muted-foreground hover:text-foreground"
                        >
                          <GitBranch className={cn("h-3.5 w-3.5", showTimeline && "text-primary")} />
                        </Button>
                      </motion.div>
                    </TooltipSimple>
                  )}
                  {messages.length > 0 && (
                    <Popover
                      trigger={
                        <TooltipSimple content="Copy conversation" side="top">
                          <motion.div
                            whileTap={{ scale: 0.97 }}
                            transition={{ duration: 0.15 }}
                          >
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-9 w-9 text-muted-foreground hover:text-foreground"
                            >
                              <Copy className="h-3.5 w-3.5" />
                            </Button>
                          </motion.div>
                        </TooltipSimple>
                      }
                      content={
                        <div className="w-44 p-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={handleCopyAsMarkdown}
                            className="w-full justify-start text-xs"
                          >
                            Copy as Markdown
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={handleCopyAsJsonl}
                            className="w-full justify-start text-xs"
                          >
                            Copy as JSONL
                          </Button>
                        </div>
                      }
                      open={copyPopoverOpen}
                      onOpenChange={setCopyPopoverOpen}
                      side="top"
                      align="end"
                    />
                  )}
                  <TooltipSimple content="Checkpoint Settings" side="top">
                    <motion.div
                      whileTap={{ scale: 0.97 }}
                      transition={{ duration: 0.15 }}
                    >
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => setShowSettings(!showSettings)}
                        className="h-8 w-8 text-muted-foreground hover:text-foreground"
                      >
                        <Wrench className={cn("h-3.5 w-3.5", showSettings && "text-primary")} />
                      </Button>
                    </motion.div>
                  </TooltipSimple>
                </>
              }
            />
          </div>

          {/* Token Counter - positioned under the Send button */}
          {totalTokens > 0 && (
            <div className="fixed bottom-0 left-0 right-0 z-30 pointer-events-none">
              <div className="max-w-6xl mx-auto">
                <div className="flex justify-end px-4 pb-2">
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                    className="bg-background/95 backdrop-blur-md border rounded-full px-3 py-1 shadow-lg pointer-events-auto"
                  >
                    <div className="flex items-center gap-1.5 text-xs">
                      <Hash className="h-3 w-3 text-muted-foreground" />
                      <span className="font-mono">{totalTokens.toLocaleString()}</span>
                      <span className="text-muted-foreground">tokens</span>
                    </div>
                  </motion.div>
                </div>
              </div>
            </div>
          )}
        </ErrorBoundary>

        {/* Timeline */}
        <AnimatePresence>
          {showTimeline && effectiveSession && (
            <motion.div
              initial={{ x: "100%" }}
              animate={{ x: 0 }}
              exit={{ x: "100%" }}
              transition={{ duration: 0.2, ease: "easeOut" }}
              className="fixed right-0 top-0 h-full w-full sm:w-96 bg-background border-l border-border shadow-xl z-30 overflow-hidden"
            >
              <div className="h-full flex flex-col">
                {/* Timeline Header */}
                <div className="flex items-center justify-between p-4 border-b border-border">
                  <h3 className="text-lg font-semibold">Session Timeline</h3>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setShowTimeline(false)}
                    className="h-8 w-8"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                
                {/* Timeline Content */}
                <div className="flex-1 overflow-y-auto p-4">
                  <TimelineNavigator
                    sessionId={effectiveSession.id}
                    projectId={effectiveSession.project_id}
                    projectPath={projectPath}
                    currentMessageIndex={messages.length - 1}
                    onCheckpointSelect={handleCheckpointSelect}
                    onFork={handleFork}
                    onCheckpointCreated={handleCheckpointCreated}
                    refreshVersion={timelineVersion}
                  />
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Fork Dialog */}
      <Dialog open={showForkDialog} onOpenChange={setShowForkDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Fork Session</DialogTitle>
            <DialogDescription>
              Create a new session branch from the selected checkpoint.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="fork-name">New Session Name</Label>
              <Input
                id="fork-name"
                placeholder="e.g., Alternative approach"
                value={forkSessionName}
                onChange={(e) => setForkSessionName(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === "Enter" && !isLoading) {
                    handleConfirmFork();
                  }
                }}
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowForkDialog(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              onClick={handleConfirmFork}
              disabled={isLoading || !forkSessionName.trim()}
            >
              Create Fork
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Settings Dialog */}
      {showSettings && effectiveSession && (
        <Dialog open={showSettings} onOpenChange={setShowSettings}>
          <DialogContent className="max-w-2xl">
            <CheckpointSettings
              sessionId={effectiveSession.id}
              projectId={effectiveSession.project_id}
              projectPath={projectPath}
              onClose={() => setShowSettings(false)}
            />
          </DialogContent>
        </Dialog>
      )}

      {/* Slash Commands Settings Dialog */}
      {showSlashCommandsSettings && (
        <Dialog open={showSlashCommandsSettings} onOpenChange={setShowSlashCommandsSettings}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
            <DialogHeader>
              <DialogTitle>Slash Commands</DialogTitle>
              <DialogDescription>
                Manage project-specific slash commands for {projectPath}
              </DialogDescription>
            </DialogHeader>
            <div className="flex-1 overflow-y-auto">
              <SlashCommandsManager projectPath={projectPath} />
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* SuperClaude Command Picker */}
      {showSuperClaudeCommands && (
        <SuperClaudeCommandPicker
          onSelect={handleSuperClaudeCommandSelect}
          onClose={() => setShowSuperClaudeCommands(false)}
          onOpenHelp={() => {
            setShowSuperClaudeCommands(false);
            setShowSuperClaudeHelp(true);
          }}
        />
      )}
      
      {/* SuperClaude Help Panel */}
      <SuperClaudeHelpPanel
        isOpen={showSuperClaudeHelp}
        onClose={() => setShowSuperClaudeHelp(false)}
        onCommandSelect={(command) => {
          setShowSuperClaudeHelp(false);
          handleSuperClaudeCommandSelect(command);
        }}
      />
      
      {/* SuperClaude Quick Reference */}
      <SuperClaudeQuickReference
        isOpen={showQuickReference}
        onClose={() => setShowQuickReference(false)}
        currentMode={superClaudeMode}
        onCommandSelect={(command) => {
          setShowQuickReference(false);
          handleSuperClaudeCommandSelect(command);
        }}
      />
      
      {/* Token Efficiency Monitor */}
      <TokenEfficiencyMonitor
        currentTokens={totalTokens}
        maxTokens={4096} // Claude 3.5 Sonnet token limit
        activeMode={superClaudeMode}
        show={true}
        onActivateEfficiencyMode={() => {
          setSuperClaudeMode('token-efficiency');
          setTokenEfficiencyEnabled(true);
          superClaudeService.enableTokenEfficiency();
        }}
        onDeactivateEfficiencyMode={() => {
          setSuperClaudeMode('normal');
          setTokenEfficiencyEnabled(false);
          superClaudeService.disableTokenEfficiency();
        }}
      />
      </div>
    </TooltipProvider>
  );
};

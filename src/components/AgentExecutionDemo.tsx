import React, { useState } from "react";
import { StreamMessage } from "./StreamMessage";
import type { ClaudeStreamMessage } from "./AgentExecution";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

/**
 * Enhanced demo component showing all the different message types and tools with theme testing
 */
export const AgentExecutionDemo: React.FC = () => {
  const [currentTheme, setCurrentTheme] = useState("theme-dark");
  
  // Sample messages covering all message types
  const messages: ClaudeStreamMessage[] = [
    // System initialization message
    {
      type: "system",
      subtype: "init",
      session_id: "test-session-123",
      model: "claude-4-sonnet",
      cwd: "/Users/<USER>/dev/project",
      tools: ["read", "write", "edit", "bash"]
    },
    
    // Summary message
    {
      leafUuid: "3c5ecb4f-c1f0-40c2-a357-ab7642ad28b8",
      summary: "JSONL Viewer Model Configuration and Setup",
      type: "summary" as any
    },
    
    // Assistant message with text content
    {
      type: "assistant",
      message: {
        content: [{
          type: "text",
          text: "Hello! I'm <PERSON>, an AI assistant. Here's some example content with **bold text** and *italic text*. \n\nI can also create lists:\n\n1. First item\n2. Second item\n3. Third item\n\nAnd code blocks:\n\n```javascript\nconst greeting = 'Hello, world!';\nconsole.log(greeting);\n```\n\n> This is a blockquote example\n\n## Heading 2\n\nSome more text content to demonstrate the styling."
        }],
        usage: { input_tokens: 1200, output_tokens: 800 }
      }
    },
    
    // User message with SuperClaude context
    {
      type: "user",
      message: {
        content: [
          {
            type: "text",
            text: "This is a user message with SuperClaude context and badges."
          }
        ]
      },
      superClaudeContext: {
        command: "test",
        mode: "brainstorming",
        personas: ["architect", "frontend"],
        waveStrategy: "aggressive",
        mcpServers: ["magic", "sequential"]
      }
    },
    
    // Assistant with Edit tool
    {
      type: "assistant",
      message: {
        content: [{
          type: "tool_use",
          name: "Edit",
          input: {
            file_path: "/Users/<USER>/dev/jsonl-viewer/script.js",
            new_string: "reader.onerror = () => reject(new Error('Failed to read file'));",
            old_string: "reader.onerror = e => reject(new Error('Failed to read file'));"
          }
        }],
        usage: { input_tokens: 4, output_tokens: 158 }
      }
    },
    
    // User with Edit tool result
    {
      type: "user",
      message: {
        content: [{
          type: "tool_result",
          content: `The file /Users/<USER>/dev/jsonl-viewer/script.js has been updated. Here's the result of running \`cat -n\` on a snippet of the edited file:
   220       readFileAsText(file) {
   221         return new Promise((resolve, reject) => {
   222           const reader = new FileReader();
   223           reader.onload = e => resolve(e.target.result);
   224           reader.onerror = () => reject(new Error('Failed to read file'));
   225           reader.readAsText(file);
   226         });
   227       }
   228`
        }]
      }
    },
    
    // Assistant with MCP tool
    {
      type: "assistant",
      message: {
        content: [{
          type: "tool_use",
          name: "mcp__ide__getDiagnostics",
          input: {}
        }],
        usage: { input_tokens: 4, output_tokens: 37 }
      }
    },
    
    // User with empty tool result
    {
      type: "user",
      message: {
        content: [{
          type: "tool_result",
          content: ""
        }]
      }
    },
    
    // Assistant with Write tool (large content)
    {
      type: "assistant",
      message: {
        content: [{
          type: "tool_use",
          name: "Write",
          input: {
            file_path: "/Users/<USER>/dev/jsonl-viewer/styles.css",
            content: `/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
    background: #f8fafc;
    min-height: 100vh;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 40px;
}

.header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 8px;
}

.header p {
    color: #718096;
    font-size: 1.1rem;
}

/* Input Section */
.input-section {
    display: grid;
    gap: 20px;
    margin-bottom: 30px;
}

/* Drop Zone */
.drop-zone {
    border: 2px dashed #cbd5e0;
    border-radius: 12px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
    position: relative;
}

.drop-zone:hover,
.drop-zone.drag-over {
    border-color: #4299e1;
    background: #ebf8ff;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(66, 153, 225, 0.1);
}

/* ... many more lines of CSS ... */
/* This content is over 1000 characters so it should show the maximize button */
` + '\n'.repeat(100) + '/* End of very long CSS file */'
          }
        }]
      }
    },
    
    // Success result message
    {
      type: "result",
      result: "The operation completed successfully. Here's a summary of what was accomplished:\n\n- Task 1 was completed\n- Task 2 was completed\n- All tests passed\n\n```python\ndef example_function():\n    return \"Success\"\n```",
      usage: {
        input_tokens: 1200,
        output_tokens: 800
      },
      cost_usd: 0.0125,
      duration_ms: 2450
    },
    
    // Error result message
    {
      type: "result",
      is_error: true,
      error: "An error occurred while processing the request. Please check your input and try again.",
      result: "Error details:\n\n- Invalid parameter provided\n- Connection timeout\n\n```bash\nError: Connection failed\n    at line 42\n```"
    }
  ];

  return (
    <div className={currentTheme}>
      <div className="max-w-4xl mx-auto p-6 space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>StreamMessage Visual Test</CardTitle>
            <p className="text-sm text-muted-foreground">
              Testing visual improvements across different themes and message types
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Theme Selector */}
            <div className="flex flex-wrap gap-2">
              <span className="text-sm font-medium self-center">Theme:</span>
              <Button
                variant={currentTheme === "theme-dark" ? "default" : "outline"}
                size="sm"
                onClick={() => setCurrentTheme("theme-dark")}
              >
                Dark
              </Button>
              <Button
                variant={currentTheme === "theme-gray" ? "default" : "outline"}
                size="sm"
                onClick={() => setCurrentTheme("theme-gray")}
              >
                Gray
              </Button>
              <Button
                variant={currentTheme === "theme-light" ? "default" : "outline"}
                size="sm"
                onClick={() => setCurrentTheme("theme-light")}
              >
                Light
              </Button>
            </div>
          </CardContent>
        </Card>
        
        <div className="space-y-4">
          {messages.map((message, idx) => (
            <StreamMessage key={idx} message={message} streamMessages={messages} />
          ))}
        </div>
      </div>
    </div>
  );
};
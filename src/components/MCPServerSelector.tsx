import React, { useState } from 'react';
import { Check, ChevronDown, Server } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface MCPServer {
  name: string;
  displayName: string;
  description?: string;
  available: boolean;
}

interface MCPServerSelectorProps {
  selectedServers: string[];
  onServersChange: (servers: string[]) => void;
  className?: string;
}

// Predefined MCP servers with their display names and descriptions
const PREDEFINED_SERVERS: MCPServer[] = [
  { 
    name: 'sequential-thinking', 
    displayName: 'Sequential',
    description: 'Complex analysis & multi-step reasoning',
    available: true 
  },
  { 
    name: 'context7', 
    displayName: 'Context7',
    description: 'Documentation & library patterns',
    available: true 
  },
  { 
    name: 'playwright', 
    displayName: 'Playwright',
    description: 'Browser automation & E2E testing',
    available: true 
  },
  { 
    name: 'web-browser', 
    displayName: 'Magic/Browser',
    description: 'UI components & web browsing',
    available: true 
  },
  { 
    name: 'filesystem', 
    displayName: 'Filesystem',
    description: 'File system operations',
    available: true 
  },
  { 
    name: 'memory', 
    displayName: 'Memory',
    description: 'Persistent memory & knowledge graph',
    available: true 
  },
  { 
    name: 'morphllm', 
    displayName: 'Morphllm',
    description: 'Pattern-based code editing & bulk transformations',
    available: true 
  },
  { 
    name: 'serena', 
    displayName: 'Serena',
    description: 'Semantic code understanding & project memory',
    available: true 
  },
];

export const MCPServerSelector: React.FC<MCPServerSelectorProps> = ({
  selectedServers,
  onServersChange,
  className,
}) => {
  const [availableServers] = useState<MCPServer[]>(PREDEFINED_SERVERS);
  const [isOpen, setIsOpen] = useState(false);

  // Map SuperClaude shorthand names to actual MCP server names
  const mapServerName = (name: string): string => {
    switch (name) {
      case 'sequential': return 'sequential-thinking';
      case 'magic': return 'web-browser';
      case 'context7': return 'context7';
      case 'playwright': return 'playwright';
      default: return name;
    }
  };

  // Map selected servers to their actual names
  const mappedSelectedServers = selectedServers.map(mapServerName);

  const handleServerToggle = (serverName: string) => {
    const actualName = mapServerName(serverName);
    const newServers = mappedSelectedServers.includes(actualName)
      ? mappedSelectedServers.filter(s => s !== actualName)
      : [...mappedSelectedServers, actualName];
    
    // Map back to shorthand names for consistency with SuperClaude
    const shorthandServers = newServers.map(s => {
      switch (s) {
        case 'sequential-thinking': return 'sequential';
        case 'web-browser': return 'magic';
        default: return s;
      }
    });
    
    onServersChange(shorthandServers);
  };

  const handleSelectAll = () => {
    const allServerNames = availableServers
      .filter(s => s.available)
      .map(s => {
        switch (s.name) {
          case 'sequential-thinking': return 'sequential';
          case 'web-browser': return 'magic';
          default: return s.name;
        }
      });
    onServersChange(allServerNames);
  };

  const handleClearAll = () => {
    onServersChange([]);
  };

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-2 text-xs"
          >
            <Server className="h-3.5 w-3.5" />
            <span>MCP Servers</span>
            {mappedSelectedServers.length > 0 && (
              <Badge variant="secondary" className="ml-1 px-1.5 py-0 text-xs">
                {mappedSelectedServers.length}
              </Badge>
            )}
            <ChevronDown className="h-3 w-3 opacity-50" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent 
          align="start" 
          className="w-72 max-h-96 overflow-y-auto"
        >
          <div className="px-2 py-1.5 text-xs font-semibold text-muted-foreground">
            Select MCP Servers
          </div>
          
          <div className="flex gap-1 px-2 pb-2">
            <Button
              variant="ghost"
              size="sm"
              className="text-xs h-7 px-2"
              onClick={handleSelectAll}
            >
              Select All
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="text-xs h-7 px-2"
              onClick={handleClearAll}
            >
              Clear All
            </Button>
          </div>
          
          <DropdownMenuSeparator />
          
          {availableServers.map((server) => (
            <DropdownMenuCheckboxItem
              key={server.name}
              checked={mappedSelectedServers.includes(server.name)}
              onCheckedChange={() => handleServerToggle(server.name)}
              disabled={!server.available}
              className="px-2 py-2"
            >
              <div className="flex items-start gap-2 flex-1">
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-sm">
                      {server.displayName}
                    </span>
                    {!server.available && (
                      <Badge variant="outline" className="text-xs px-1 py-0">
                        Unavailable
                      </Badge>
                    )}
                  </div>
                  {server.description && (
                    <p className="text-xs text-muted-foreground mt-0.5">
                      {server.description}
                    </p>
                  )}
                </div>
              </div>
            </DropdownMenuCheckboxItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
      
      {mappedSelectedServers.length > 0 && (
        <div className="flex flex-wrap gap-1">
          {mappedSelectedServers.map((server) => {
            const serverInfo = availableServers.find(s => s.name === server);
            return (
              <Badge
                key={server}
                variant="secondary"
                className="text-xs px-2 py-0.5 flex items-center gap-1"
              >
                <Check className="h-3 w-3 text-green-500" />
                {serverInfo?.displayName || server}
              </Badge>
            );
          })}
        </div>
      )}
    </div>
  );
};
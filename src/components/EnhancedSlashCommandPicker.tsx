import React, { useState, useEffect, useRef } from "react";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { api } from "@/lib/api";
import { 
  X, 
  Command,
  Search,
  Globe,
  FolderOpen,
  Zap,
  FileCode,
  Terminal,
  AlertCircle,
  User,
  Building2,
  Code,
  Wrench,
  FileText,
  Target,
  Lightbulb,
  Shield,
  ChevronRight,
  Star
} from "lucide-react";
import type { SlashCommand } from "@/lib/api";
import { cn } from "@/lib/utils";
import { useTrackEvent, useFeatureAdoptionTracking } from "@/hooks";
import { commandExecutor } from '@/services/commandExecutor';
import type { SuperClaudeCommand } from '@/types/superClaude';
import { agentRegistry } from '@/services/agents';

interface EnhancedSlashCommandPickerProps {
  /**
   * The project path for loading project-specific commands
   */
  projectPath?: string;
  /**
   * Callback when a command is selected
   */
  onSelect: (command: SlashCommand) => void;
  /**
   * Callback to close the picker
   */
  onClose: () => void;
  /**
   * Initial search query (text after /)
   */
  initialQuery?: string;
  /**
   * Optional className for styling
   */
  className?: string;
}

// Get icon for command based on its properties
const getCommandIcon = (command: SlashCommand | SuperClaudeCommand) => {
  // If it has bash commands, show terminal icon
  if ('has_bash_commands' in command && command.has_bash_commands) return Terminal;
  
  // For SuperClaude commands, use category-based icons
  if ('category' in command) {
    switch (command.category) {
      case 'analysis': return Search;
      case 'development': return Code;
      case 'quality': return Wrench;
      case 'documentation': return FileText;
      case 'planning': return Target;
      case 'testing': return AlertCircle;
      case 'meta': return Lightbulb;
      default: return Command;
    }
  }
  
  // Default icons based on command content
  if ('full_command' in command) {
    if (command.full_command.includes('git')) return Globe;
    if (command.full_command.includes('npm') || command.full_command.includes('yarn')) return FileCode;
    if (command.full_command.includes('test')) return AlertCircle;
  }
  
  return Command;
};

// Get category icon
const getCategoryIcon = (category: string) => {
  switch (category) {
    case 'analysis': return Search;
    case 'development': return Code;
    case 'quality': return Wrench;
    case 'documentation': return FileText;
    case 'planning': return Target;
    case 'testing': return AlertCircle;
    case 'security': return Shield;
    case 'meta': return Lightbulb;
    default: return Command;
  }
};

// SuperClaude command categories
const SUPERCLAUDE_CATEGORIES = [
  { id: 'all', name: 'All Commands', icon: Command },
  { id: 'analysis', name: 'Analysis', icon: Search },
  { id: 'development', name: 'Development', icon: Code },
  { id: 'quality', name: 'Quality', icon: Wrench },
  { id: 'documentation', name: 'Documentation', icon: FileText },
  { id: 'planning', name: 'Planning', icon: Target },
  { id: 'testing', name: 'Testing', icon: AlertCircle },
  { id: 'meta', name: 'Meta', icon: Lightbulb }
];

// Mode categories
const MODE_CATEGORIES = [
  { id: 'all', name: 'All Modes', icon: Command },
  { id: 'normal', name: 'Normal', icon: Zap },
  { id: 'brainstorming', name: 'Brainstorming', icon: Lightbulb },
  { id: 'introspection', name: 'Introspection', icon: Search },
  { id: 'task-management', name: 'Task Management', icon: Target },
  { id: 'orchestration', name: 'Orchestration', icon: Code },
  { id: 'token-efficiency', name: 'Token Efficiency', icon: Zap },
  { id: 'business-panel', name: 'Business Panel', icon: Building2 }
];

export const EnhancedSlashCommandPicker: React.FC<EnhancedSlashCommandPickerProps> = ({
  projectPath,
  onSelect,
  onClose,
  initialQuery = "",
  className,
}) => {
  const [commands, setCommands] = useState<SlashCommand[]>([]);
  const [superClaudeCommands, setSuperClaudeCommands] = useState<SuperClaudeCommand[]>([]);
  const [modes, setModes] = useState<any[]>([]);
  const [agents, setAgents] = useState<any[]>([]);
  const [filteredCommands, setFilteredCommands] = useState<SlashCommand[]>([]);
  const [filteredSuperClaudeCommands, setFilteredSuperClaudeCommands] = useState<SuperClaudeCommand[]>([]);
  const [filteredModes, setFilteredModes] = useState<any[]>([]);
  const [filteredAgents, setFilteredAgents] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [searchQuery, setSearchQuery] = useState(initialQuery);
  const [activeTab, setActiveTab] = useState<string>("superclaude");
  const [activeCategory, setActiveCategory] = useState<string>("all");
  const [activeModeCategory, setActiveModeCategory] = useState<string>("all");
  const [showDocumentation, setShowDocumentation] = useState<string | null>(null);
  
  const commandListRef = useRef<HTMLDivElement>(null);
  
  // Analytics tracking
  const trackEvent = useTrackEvent();
  const slashCommandFeatureTracking = useFeatureAdoptionTracking('slash_commands');
  
  useEffect(() => {
    loadCommands();
    loadSuperClaudeCommands();
    loadModes();
    loadAgents();
  }, [projectPath]);
  
  // Load regular commands
  const loadCommands = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Always load fresh commands from filesystem
      const loadedCommands = await api.slashCommandsList(projectPath);
      setCommands(loadedCommands);
    } catch (err) {
      console.error("Failed to load slash commands:", err);
      setError(err instanceof Error ? err.message : 'Failed to load commands');
      setCommands([]);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Load SuperClaude commands
  const loadSuperClaudeCommands = () => {
    try {
      const availableCommands = commandExecutor.getAvailableCommands();
      setSuperClaudeCommands(availableCommands);
    } catch (err) {
      console.error("Failed to load SuperClaude commands:", err);
      setSuperClaudeCommands([]);
    }
  };
  
  // Load modes
  const loadModes = () => {
    try {
      // Get all available modes from the activation engine
      // This is a simplified approach - in reality, you'd get this from the mode system
      const availableModes = [
        { id: 'normal', name: 'Normal Mode', description: 'Standard Claude behavior' },
        { id: 'brainstorming', name: 'Brainstorming Mode', description: 'Collaborative discovery and ideation' },
        { id: 'introspection', name: 'Introspection Mode', description: 'Meta-cognitive analysis and reasoning' },
        { id: 'task-management', name: 'Task Management Mode', description: 'Structured workflow execution' },
        { id: 'orchestration', name: 'Orchestration Mode', description: 'Intelligent tool selection' },
        { id: 'token-efficiency', name: 'Token Efficiency Mode', description: 'Compressed communication' },
        { id: 'business-panel', name: 'Business Panel Mode', description: 'Multi-expert strategic analysis' }
      ];
      setModes(availableModes as any);
    } catch (err) {
      console.error("Failed to load modes:", err);
      setModes([]);
    }
  };
  
  // Load agents
  const loadAgents = () => {
    try {
      const availableAgents = agentRegistry.getAll();
      setAgents(availableAgents);
    } catch (err) {
      console.error("Failed to load agents:", err);
      setAgents([]);
    }
  };
  
  // Simple fuzzy search function
  const fuzzySearch = (text: string, query: string): boolean => {
    if (!query) return true;
    const textLower = text.toLowerCase();
    const queryLower = query.toLowerCase();
    return textLower.includes(queryLower);
  };
  
  // Filter items based on search query and active filters
  useEffect(() => {
    // Filter regular commands
    if (commands.length > 0) {
      const query = searchQuery.toLowerCase();
      let filteredByTab: SlashCommand[];
      
      // Filter by active tab
      if (activeTab === "default") {
        // Show default/built-in commands
        filteredByTab = commands.filter(cmd => cmd.scope === "default");
      } else {
        // Show all custom commands (both user and project)
        filteredByTab = commands.filter(cmd => cmd.scope !== "default");
      }
      
      // Then filter by search query
      let filtered: SlashCommand[];
      if (!query) {
        filtered = filteredByTab;
      } else {
        filtered = filteredByTab.filter(cmd => {
          // Match against command name
          if (cmd.name.toLowerCase().includes(query)) return true;
          
          // Match against full command
          if (cmd.full_command.toLowerCase().includes(query)) return true;
          
          // Match against namespace
          if (cmd.namespace && cmd.namespace.toLowerCase().includes(query)) return true;
          
          // Match against description
          if (cmd.description && cmd.description.toLowerCase().includes(query)) return true;
          
          return false;
        });
      }
      
      setFilteredCommands(filtered);
    }
    
    // Filter SuperClaude commands
    if (superClaudeCommands.length > 0) {
      const query = searchQuery.toLowerCase();
      let filteredByCategory: SuperClaudeCommand[];
      
      // Filter by active category
      if (activeCategory === "all") {
        filteredByCategory = superClaudeCommands;
      } else {
        filteredByCategory = superClaudeCommands.filter(cmd => cmd.category === activeCategory);
      }
      
      // Then filter by search query using fuzzy search
      let filtered: SuperClaudeCommand[];
      if (!query) {
        filtered = filteredByCategory;
      } else {
        filtered = filteredByCategory.filter(cmd => {
          // Fuzzy match against command name
          if (fuzzySearch(cmd.name, query)) return true;
          
          // Fuzzy match against description
          if (fuzzySearch(cmd.description, query)) return true;
          
          // Fuzzy match against arguments
          if (cmd.arguments && fuzzySearch(cmd.arguments, query)) return true;
          
          return false;
        });
      }
      
      setFilteredSuperClaudeCommands(filtered);
    }
    
    // Filter modes
    if (modes.length > 0) {
      const query = searchQuery.toLowerCase();
      let filteredByCategory: any[];
      
      // Filter by active mode category
      if (activeModeCategory === "all") {
        filteredByCategory = modes;
      } else {
        filteredByCategory = modes.filter(mode => mode.id === activeModeCategory);
      }
      
      // Then filter by search query
      let filtered: any[];
      if (!query) {
        filtered = filteredByCategory;
      } else {
        filtered = filteredByCategory.filter(mode => {
          // Fuzzy match against mode name
          if (fuzzySearch(mode.name, query)) return true;
          
          // Fuzzy match against description
          if (fuzzySearch(mode.description, query)) return true;
          
          return false;
        });
      }
      
      setFilteredModes(filtered);
    }
    
    // Filter agents
    if (agents.length > 0) {
      const query = searchQuery.toLowerCase();
      
      // Filter by search query
      let filtered: any[];
      if (!query) {
        filtered = agents;
      } else {
        filtered = agents.filter(agent => {
          // Fuzzy match against agent name
          if (fuzzySearch(agent.name, query)) return true;
          
          // Fuzzy match against description
          if (fuzzySearch(agent.description, query)) return true;
          
          return false;
        });
      }
      
      setFilteredAgents(filtered);
    }
    
    // Reset selected index when filters change
    setSelectedIndex(0);
  }, [commands, superClaudeCommands, modes, agents, searchQuery, activeTab, activeCategory, activeModeCategory]);
  
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'Escape':
          e.preventDefault();
          if (showDocumentation) {
            setShowDocumentation(null);
          } else {
            onClose();
          }
          break;
          
        case 'Enter':
          e.preventDefault();
          if (showDocumentation) {
            return;
          }
          
          // Handle selection based on active tab
          if (activeTab === "superclaude" && filteredSuperClaudeCommands.length > 0) {
            const command = filteredSuperClaudeCommands[selectedIndex];
            if (command) {
              // Create a mock SlashCommand for SuperClaude commands
              const mockCommand: SlashCommand = {
                id: `sc-${command.name}`,
                name: `sc:${command.name}`,
                full_command: `/sc:${command.name}`,
                description: command.description,
                scope: "default",
                file_path: "",
                content: "",
                allowed_tools: [],
                has_bash_commands: false,
                has_file_references: false,
                accepts_arguments: false
              };
              trackEvent.slashCommandSelected({
                command_name: mockCommand.name,
                selection_method: 'keyboard'
              });
              slashCommandFeatureTracking.trackUsage();
              onSelect(mockCommand);
            }
          } else if (activeTab === "modes" && filteredModes.length > 0) {
            const mode = filteredModes[selectedIndex];
            if (mode) {
              // Create a mock SlashCommand for mode activation
              const mockCommand: SlashCommand = {
                id: `mode-${mode.id}`,
                name: `mode:${mode.id}`,
                full_command: `/mode:${mode.id}`,
                description: mode.description,
                scope: "default",
                file_path: "",
                content: "",
                allowed_tools: [],
                has_bash_commands: false,
                has_file_references: false,
                accepts_arguments: false
              };
              trackEvent.slashCommandSelected({
                command_name: mockCommand.name,
                selection_method: 'keyboard'
              });
              slashCommandFeatureTracking.trackUsage();
              onSelect(mockCommand);
            }
          } else if (activeTab === "agents" && filteredAgents.length > 0) {
            const agent = filteredAgents[selectedIndex];
            if (agent) {
              // Create a mock SlashCommand for agent activation
              const mockCommand: SlashCommand = {
                id: `agent-${agent.id}`,
                name: `agent:${agent.id}`,
                full_command: `/agent:${agent.id}`,
                description: agent.description,
                scope: "default",
                file_path: "",
                content: "",
                allowed_tools: [],
                has_bash_commands: false,
                has_file_references: false,
                accepts_arguments: false
              };
              trackEvent.slashCommandSelected({
                command_name: mockCommand.name,
                selection_method: 'keyboard'
              });
              slashCommandFeatureTracking.trackUsage();
              onSelect(mockCommand);
            }
          } else if (filteredCommands.length > 0) {
            const command = filteredCommands[selectedIndex];
            if (command) {
              trackEvent.slashCommandSelected({
                command_name: command.name,
                selection_method: 'keyboard'
              });
              slashCommandFeatureTracking.trackUsage();
              onSelect(command);
            }
          }
          break;
          
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev => Math.max(0, prev - 1));
          break;
          
        case 'ArrowDown':
          e.preventDefault();
          let maxIndex = 0;
          if (activeTab === "superclaude") {
            maxIndex = filteredSuperClaudeCommands.length - 1;
          } else if (activeTab === "modes") {
            maxIndex = filteredModes.length - 1;
          } else if (activeTab === "agents") {
            maxIndex = filteredAgents.length - 1;
          } else {
            maxIndex = filteredCommands.length - 1;
          }
          setSelectedIndex(prev => Math.min(maxIndex, prev + 1));
          break;
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [filteredCommands, filteredSuperClaudeCommands, filteredModes, filteredAgents, selectedIndex, activeTab, onClose, onSelect, showDocumentation]);
  
  useEffect(() => {
    if (commandListRef.current && !showDocumentation) {
      const selectedElement = commandListRef.current.querySelector(`[data-index="${selectedIndex}"]`);
      if (selectedElement) {
        selectedElement.scrollIntoView({ block: 'nearest', behavior: 'smooth' });
      }
    }
  }, [selectedIndex, showDocumentation]);
  
  const handleCommandClick = (command: SlashCommand) => {
    trackEvent.slashCommandSelected({
      command_name: command.name,
      selection_method: 'click'
    });
    slashCommandFeatureTracking.trackUsage();
    onSelect(command);
  };
  
  // Handle SuperClaude command click
  const handleSuperClaudeCommandClick = (command: SuperClaudeCommand) => {
    // Create a mock SlashCommand for SuperClaude commands
    const mockCommand: SlashCommand = {
      id: `sc-${command.name}`,
      name: `sc:${command.name}`,
      full_command: `/sc:${command.name}`,
      description: command.description,
      scope: "default",
      file_path: "",
      content: "",
      allowed_tools: [],
      has_bash_commands: false,
      has_file_references: false,
      accepts_arguments: false
    };
    trackEvent.slashCommandSelected({
      command_name: mockCommand.name,
      selection_method: 'click'
    });
    slashCommandFeatureTracking.trackUsage();
    onSelect(mockCommand);
  };
  
  // Handle mode click
  const handleModeClick = (mode: any) => {
    // Create a mock SlashCommand for mode activation
    const mockCommand: SlashCommand = {
      id: `mode-${mode.id}`,
      name: `mode:${mode.id}`,
      full_command: `/mode:${mode.id}`,
      description: mode.description,
      scope: "default",
      file_path: "",
      content: "",
      allowed_tools: [],
      has_bash_commands: false,
      has_file_references: false,
      accepts_arguments: false
    };
    trackEvent.slashCommandSelected({
      command_name: mockCommand.name,
      selection_method: 'click'
    });
    slashCommandFeatureTracking.trackUsage();
    onSelect(mockCommand);
  };
  
  // Handle agent click
  const handleAgentClick = (agent: any) => {
    // Create a mock SlashCommand for agent activation
    const mockCommand: SlashCommand = {
      id: `agent-${agent.id}`,
      name: `agent:${agent.id}`,
      full_command: `/agent:${agent.id}`,
      description: agent.description,
      scope: "default",
      file_path: "",
      content: "",
      allowed_tools: [],
      has_bash_commands: false,
      has_file_references: false,
      accepts_arguments: false
    };
    trackEvent.slashCommandSelected({
      command_name: mockCommand.name,
      selection_method: 'click'
    });
    slashCommandFeatureTracking.trackUsage();
    onSelect(mockCommand);
  };
  
  // Show documentation for a command
  const showCommandDocumentation = (command: SuperClaudeCommand) => {
    setShowDocumentation(command.name);
  };
  
  useEffect(() => {
    setSearchQuery(initialQuery);
  }, [initialQuery]);
  
  // Render command documentation
  const renderDocumentation = (commandName: string) => {
    // In a real implementation, this would fetch detailed documentation
    // For now, we'll show basic info
    const command = superClaudeCommands.find(cmd => cmd.name === commandName);
    if (!command) return null;
    
    return (
      <div className="p-4 h-full overflow-y-auto">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Command className="h-5 w-5" />
            {command.name}
          </h3>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowDocumentation(null)}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
        
        <p className="text-muted-foreground mb-4">{command.description}</p>
        
        {command.arguments && (
          <div className="mb-4">
            <h4 className="font-medium mb-2">Arguments</h4>
            <code className="bg-muted px-2 py-1 rounded text-sm">{command.arguments}</code>
          </div>
        )}
        
        {command.flags && command.flags.length > 0 && (
          <div className="mb-4">
            <h4 className="font-medium mb-2">Flags</h4>
            <div className="flex flex-wrap gap-2">
              {command.flags.map(flag => (
                <span key={flag} className="bg-muted px-2 py-1 rounded text-sm">
                  --{flag}
                </span>
              ))}
            </div>
          </div>
        )}
        
        {command.category && (
          <div className="mb-4">
            <h4 className="font-medium mb-2">Category</h4>
            <span className="bg-muted px-2 py-1 rounded text-sm capitalize">
              {command.category}
            </span>
          </div>
        )}
        
        <div className="mt-6 pt-4 border-t">
          <h4 className="font-medium mb-2">Usage Examples</h4>
          <div className="space-y-2">
            <code className="block bg-muted p-2 rounded text-sm">
              /sc:{command.name} [arguments] [--flags]
            </code>
            {command.examples && command.examples.map((example, index) => (
              <code key={index} className="block bg-muted p-2 rounded text-sm">
                {example}
              </code>
            ))}
          </div>
        </div>
      </div>
    );
  };
  
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      className={cn(
        "absolute bottom-full mb-2 left-0 z-50",
        "w-[700px] h-[500px]", // Increased size for better UX
        "bg-background border border-border rounded-lg shadow-lg",
        "flex flex-col overflow-hidden",
        className
      )}
    >
      {showDocumentation ? (
        renderDocumentation(showDocumentation)
      ) : (
        <>
          {/* Header */}
          <div className="border-b border-border p-3">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <Command className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Enhanced Slash Commands</span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search commands, modes, agents..."
                className="w-full pl-8 pr-4 py-2 bg-muted rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-ring"
                autoFocus
              />
              {searchQuery && (
                <span className="absolute right-2 top-1/2 transform -translate-y-1/2 text-xs text-muted-foreground">
                  Searching: "{searchQuery}"
                </span>
              )}
            </div>
          </div>
          
          {/* Tabs */}
          <div className="border-b border-border">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="superclaude" className="text-xs">
                  <Star className="h-3 w-3 mr-1" />
                  SuperClaude
                </TabsTrigger>
                <TabsTrigger value="modes" className="text-xs">
                  <Zap className="h-3 w-3 mr-1" />
                  Modes
                </TabsTrigger>
                <TabsTrigger value="agents" className="text-xs">
                  <User className="h-3 w-3 mr-1" />
                  Agents
                </TabsTrigger>
                <TabsTrigger value="custom" className="text-xs">
                  <FolderOpen className="h-3 w-3 mr-1" />
                  Custom
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
          
          {/* Category Filters */}
          {activeTab === "superclaude" && (
            <div className="border-b border-border px-3 py-2">
              <div className="flex gap-1 overflow-x-auto">
                {SUPERCLAUDE_CATEGORIES.map((category) => {
                  const Icon = getCategoryIcon(category.id);
                  return (
                    <button
                      key={category.id}
                      onClick={() => setActiveCategory(category.id)}
                      className={cn(
                        "flex items-center gap-1 px-2 py-1 rounded text-xs whitespace-nowrap",
                        activeCategory === category.id
                          ? "bg-primary text-primary-foreground"
                          : "bg-muted hover:bg-muted/80"
                      )}
                    >
                      <Icon className="h-3 w-3" />
                      {category.name}
                    </button>
                  );
                })}
              </div>
            </div>
          )}
          
          {activeTab === "modes" && (
            <div className="border-b border-border px-3 py-2">
              <div className="flex gap-1 overflow-x-auto">
                {MODE_CATEGORIES.map((category) => {
                  const Icon = getCategoryIcon(category.id);
                  return (
                    <button
                      key={category.id}
                      onClick={() => setActiveModeCategory(category.id)}
                      className={cn(
                        "flex items-center gap-1 px-2 py-1 rounded text-xs whitespace-nowrap",
                        activeModeCategory === category.id
                          ? "bg-primary text-primary-foreground"
                          : "bg-muted hover:bg-muted/80"
                      )}
                    >
                      <Icon className="h-3 w-3" />
                      {category.name}
                    </button>
                  );
                })}
              </div>
            </div>
          )}
          
          {/* Command List */}
          <div 
            ref={commandListRef}
            className="flex-1 overflow-y-auto p-2"
          >
            {isLoading ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-muted-foreground">Loading commands...</div>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-destructive">Error: {error}</div>
              </div>
            ) : activeTab === "superclaude" ? (
              filteredSuperClaudeCommands.length > 0 ? (
                <div className="space-y-1">
                  {filteredSuperClaudeCommands.map((command, index) => {
                    const Icon = getCommandIcon(command);
                    return (
                      <div
                        key={command.name}
                        data-index={index}
                        onClick={() => handleSuperClaudeCommandClick(command)}
                        className={cn(
                          "flex items-center justify-between p-2 rounded cursor-pointer",
                          selectedIndex === index
                            ? "bg-primary/10 border border-primary/20"
                            : "hover:bg-muted"
                        )}
                      >
                        <div className="flex items-center gap-3 flex-1 min-w-0">
                          <Icon className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <span className="font-medium text-sm truncate">/{command.name}</span>
                              <span className="text-xs bg-muted px-1.5 py-0.5 rounded capitalize">
                                {command.category}
                              </span>
                            </div>
                            <p className="text-xs text-muted-foreground truncate">
                              {command.description}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-1 flex-shrink-0">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              showCommandDocumentation(command);
                            }}
                            className="text-xs text-muted-foreground hover:text-foreground"
                          >
                            Docs
                          </button>
                          <ChevronRight className="h-4 w-4 text-muted-foreground" />
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="flex items-center justify-center h-full">
                  <div className="text-muted-foreground text-center">
                    No SuperClaude commands found
                    {searchQuery && ` matching "${searchQuery}"`}
                  </div>
                </div>
              )
            ) : activeTab === "modes" ? (
              filteredModes.length > 0 ? (
                <div className="space-y-1">
                  {filteredModes.map((mode, index) => {
                    const Icon = getCategoryIcon(mode.id);
                    return (
                      <div
                        key={mode.id}
                        data-index={index}
                        onClick={() => handleModeClick(mode)}
                        className={cn(
                          "flex items-center gap-3 p-2 rounded cursor-pointer",
                          selectedIndex === index
                            ? "bg-primary/10 border border-primary/20"
                            : "hover:bg-muted"
                        )}
                      >
                        <Icon className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                        <div className="flex-1 min-w-0">
                          <div className="font-medium text-sm">{mode.name}</div>
                          <p className="text-xs text-muted-foreground truncate">
                            {mode.description}
                          </p>
                        </div>
                        <ChevronRight className="h-4 w-4 text-muted-foreground" />
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="flex items-center justify-center h-full">
                  <div className="text-muted-foreground text-center">
                    No modes found
                    {searchQuery && ` matching "${searchQuery}"`}
                  </div>
                </div>
              )
            ) : activeTab === "agents" ? (
              filteredAgents.length > 0 ? (
                <div className="space-y-1">
                  {filteredAgents.map((agent, index) => {
                    return (
                      <div
                        key={agent.id}
                        data-index={index}
                        onClick={() => handleAgentClick(agent)}
                        className={cn(
                          "flex items-center gap-3 p-2 rounded cursor-pointer",
                          selectedIndex === index
                            ? "bg-primary/10 border border-primary/20"
                            : "hover:bg-muted"
                        )}
                      >
                        <User className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                        <div className="flex-1 min-w-0">
                          <div className="font-medium text-sm capitalize">{agent.name}</div>
                          <p className="text-xs text-muted-foreground truncate">
                            {agent.description}
                          </p>
                        </div>
                        <ChevronRight className="h-4 w-4 text-muted-foreground" />
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="flex items-center justify-center h-full">
                  <div className="text-muted-foreground text-center">
                    No agents found
                    {searchQuery && ` matching "${searchQuery}"`}
                  </div>
                </div>
              )
            ) : (
              filteredCommands.length > 0 ? (
                <div className="space-y-1">
                  {filteredCommands.map((command, index) => {
                    const Icon = getCommandIcon(command);
                    return (
                      <div
                        key={command.id}
                        data-index={index}
                        onClick={() => handleCommandClick(command)}
                        className={cn(
                          "flex items-center gap-3 p-2 rounded cursor-pointer",
                          selectedIndex === index
                            ? "bg-primary/10 border border-primary/20"
                            : "hover:bg-muted"
                        )}
                      >
                        <Icon className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                        <div className="flex-1 min-w-0">
                          <div className="font-medium text-sm">{command.name}</div>
                          <p className="text-xs text-muted-foreground truncate">
                            {command.description}
                          </p>
                        </div>
                        <ChevronRight className="h-4 w-4 text-muted-foreground" />
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="flex items-center justify-center h-full">
                  <div className="text-muted-foreground text-center">
                    No custom commands found
                    {searchQuery && ` matching "${searchQuery}"`}
                  </div>
                </div>
              )
            )}
          </div>
        </>
      )}
    </motion.div>
  );
};
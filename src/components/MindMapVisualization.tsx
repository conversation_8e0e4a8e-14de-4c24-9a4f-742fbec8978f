import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import type { MindMapNode } from '@/services/modes/brainstorming';
import { Button } from '@/components/ui/button';
import { 
  ChevronDown, 
  ChevronRight, 
  Edit3, 
  Plus, 
  Trash2, 
  ZoomIn, 
  ZoomOut,
  Move,
  Info
} from 'lucide-react';

interface MindMapVisualizationProps {
  data: MindMapNode;
  className?: string;
  onNodeClick?: (node: MindMapNode) => void;
  onNodeEdit?: (node: MindMapNode, content: string) => void;
  onNodeDelete?: (node: MindMapNode) => void;
  onNodeAdd?: (parent: MindMapNode, content: string) => void;
}

export const MindMapVisualization: React.FC<MindMapVisualizationProps> = ({
  data,
  className,
  onNodeClick,
  onNodeEdit,
  onNodeDelete,
  onNodeAdd
}) => {
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set(['root']));
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [editingNode, setEditingNode] = useState<string | null>(null);
  const [editContent, setEditContent] = useState('');
  const [zoomLevel, setZoomLevel] = useState(1);
  const [isPanning, setIsPanning] = useState(false);
  const [panStart, setPanStart] = useState({ x: 0, y: 0 });
  const [panOffset, setPanOffset] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const containerRef = useRef<HTMLDivElement>(null);

  // Toggle node expansion
  const toggleNode = (nodeId: string) => {
    setExpandedNodes(prev => {
      const newSet = new Set(prev);
      if (newSet.has(nodeId)) {
        newSet.delete(nodeId);
      } else {
        newSet.add(nodeId);
      }
      return newSet;
    });
  };

  // Handle node click
  const handleNodeClick = (node: MindMapNode, e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedNode(node.id);
    onNodeClick?.(node);
  };

  // Handle node edit
  const handleEditStart = (node: MindMapNode, e: React.MouseEvent) => {
    e.stopPropagation();
    setEditingNode(node.id);
    setEditContent(node.label);
  };

  // Save edited content
  const handleEditSave = (node: MindMapNode) => {
    if (editingNode && onNodeEdit) {
      onNodeEdit(node, editContent);
    }
    setEditingNode(null);
    setEditContent('');
  };

  // Cancel editing
  const handleEditCancel = () => {
    setEditingNode(null);
    setEditContent('');
  };

  // Handle node deletion
  const handleDelete = (node: MindMapNode, e: React.MouseEvent) => {
    e.stopPropagation();
    if (onNodeDelete) {
      onNodeDelete(node);
    }
  };

  // Handle adding child node
  const handleAddChild = (parent: MindMapNode, e: React.MouseEvent) => {
    e.stopPropagation();
    if (onNodeAdd) {
      onNodeAdd(parent, 'New idea...');
    }
  };

  // Zoom controls
  const handleZoomIn = () => setZoomLevel(prev => Math.min(prev + 0.2, 2));
  const handleZoomOut = () => setZoomLevel(prev => Math.max(prev - 0.2, 0.5));
  const handleResetZoom = () => {
    setZoomLevel(1);
    setPanOffset({ x: 0, y: 0 });
  };

  // Pan handling
  const handleMouseDown = (e: React.MouseEvent) => {
    if (e.button !== 1 && e.button !== 0) return; // Only middle mouse or left mouse
    setIsPanning(true);
    setIsDragging(true);
    setPanStart({ x: e.clientX, y: e.clientY });
    setDragStart({ x: e.clientX, y: e.clientY });
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isPanning) {
      const deltaX = e.clientX - panStart.x;
      const deltaY = e.clientY - panStart.y;
      setPanOffset(prev => ({
        x: prev.x + deltaX,
        y: prev.y + deltaY
      }));
      setPanStart({ x: e.clientX, y: e.clientY });
    }
  };

  const handleMouseUp = () => {
    setIsPanning(false);
    setIsDragging(false);
  };

  // Handle wheel zoom
  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    const delta = e.deltaY > 0 ? -0.1 : 0.1;
    setZoomLevel(prev => Math.min(Math.max(prev + delta, 0.5), 2));
  };

  // Render node with enhanced features
  const renderNode = (node: MindMapNode, depth: number = 0) => {
    const isRoot = depth === 0;
    const isCategory = depth === 1;
    const isIdea = depth === 2;
    const isExpanded = expandedNodes.has(node.id);
    const isSelected = selectedNode === node.id;
    const isEditing = editingNode === node.id;
    
    return (
      <motion.div
        key={node.id}
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3, delay: depth * 0.1 }}
        className={cn(
          'relative flex flex-col items-center',
          isRoot && 'mb-8',
          isCategory && 'mb-6',
          isIdea && 'mb-4'
        )}
      >
        {/* Node */}
        <motion.div
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={(e) => handleNodeClick(node, e)}
          className={cn(
            'px-4 py-2 rounded-lg cursor-pointer shadow-md backdrop-blur-sm border relative',
            isRoot && 'bg-primary text-primary-foreground text-xl font-bold py-3 px-6',
            isCategory && 'bg-secondary text-secondary-foreground font-semibold',
            isIdea && 'bg-background text-foreground border-muted-foreground/30',
            isSelected && 'ring-2 ring-primary ring-offset-2',
            'hover:shadow-lg transition-all duration-200'
          )}
        >
          <div className="text-center">
            {isEditing ? (
              <div className="flex flex-col gap-2">
                <input
                  type="text"
                  value={editContent}
                  onChange={(e) => setEditContent(e.target.value)}
                  className="px-2 py-1 border rounded text-sm"
                  autoFocus
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') handleEditSave(node);
                    if (e.key === 'Escape') handleEditCancel();
                  }}
                />
                <div className="flex gap-1">
                  <Button 
                    size="sm" 
                    onClick={() => handleEditSave(node)}
                    className="h-6 px-2 text-xs"
                  >
                    Save
                  </Button>
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={handleEditCancel}
                    className="h-6 px-2 text-xs"
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            ) : (
              <>
                <div className="flex items-center gap-2">
                  {node.children && node.children.length > 0 && (
                    <button 
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleNode(node.id);
                      }}
                      className="p-0.5 hover:bg-muted rounded"
                    >
                      {isExpanded ? (
                        <ChevronDown className="h-3 w-3" />
                      ) : (
                        <ChevronRight className="h-3 w-3" />
                      )}
                    </button>
                  )}
                  <span>{node.label}</span>
                </div>
                {node.metadata && (
                  <div className="text-xs mt-1 opacity-75">
                    {node.metadata.score && `Score: ${node.metadata.score}/10`}
                    {node.metadata.confidence !== undefined && (
                      <span className="ml-2">
                        Conf: {(node.metadata.confidence * 100).toFixed(0)}%
                      </span>
                    )}
                  </div>
                )}
              </>
            )}
          </div>
          
          {/* Node action buttons */}
          {!isEditing && (
            <div className="absolute -top-2 -right-2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
              {node.children && node.children.length > 0 && (
                <Button
                  size="icon"
                  variant="ghost"
                  className="h-5 w-5"
                  onClick={(e) => handleAddChild(node, e)}
                >
                  <Plus className="h-3 w-3" />
                </Button>
              )}
              <Button
                size="icon"
                variant="ghost"
                className="h-5 w-5"
                onClick={(e) => handleEditStart(node, e)}
              >
                <Edit3 className="h-3 w-3" />
              </Button>
              <Button
                size="icon"
                variant="ghost"
                className="h-5 w-5"
                onClick={(e) => handleDelete(node, e)}
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
          )}
        </motion.div>
        
        {/* Children */}
        {node.children && node.children.length > 0 && isExpanded && (
          <div className={cn(
            'flex mt-4',
            isRoot ? 'flex-row flex-wrap justify-center gap-8' : 'flex-col items-center gap-4'
          )}>
            {node.children.map(child => (
              <div key={child.id} className="flex flex-col items-center">
                {/* Connector line */}
                <div className={cn(
                  'bg-muted-foreground/30',
                  isRoot ? 'w-1 h-8' : 'h-1 w-8'
                )} />
                {renderNode(child, depth + 1)}
              </div>
            ))}
          </div>
        )}
      </motion.div>
    );
  };

  return (
    <div 
      ref={containerRef}
      className={cn('w-full h-full overflow-hidden relative', className)}
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseUp}
      onWheel={handleWheel}
    >
      <div className="absolute top-2 right-2 flex flex-col gap-1 z-10">
        <Button 
          size="icon" 
          variant="outline" 
          className="h-8 w-8"
          onClick={handleZoomIn}
        >
          <ZoomIn className="h-4 w-4" />
        </Button>
        <Button 
          size="icon" 
          variant="outline" 
          className="h-8 w-8"
          onClick={handleZoomOut}
        >
          <ZoomOut className="h-4 w-4" />
        </Button>
        <Button 
          size="icon" 
          variant="outline" 
          className="h-8 w-8"
          onClick={handleResetZoom}
        >
          <Move className="h-4 w-4" />
        </Button>
      </div>
      
      <div 
        className="w-full h-full p-4"
        style={{
          transform: `scale(${zoomLevel}) translate(${panOffset.x}px, ${panOffset.y}px)`,
          transformOrigin: 'center center',
          cursor: isPanning ? 'grabbing' : 'grab'
        }}
      >
        <div className="flex justify-center min-h-full">
          {renderNode(data)}
        </div>
      </div>
    </div>
  );
};

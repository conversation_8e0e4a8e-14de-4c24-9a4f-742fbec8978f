import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Brain, 
  Code2, 
  Server, 
  Shield, 
  Zap, 
  Search, 
  TestTube, 
  Wrench, 
  Cloud, 
  GraduationCap, 
  FileText,
  Sparkles 
} from 'lucide-react';
import type { SuperClaudePersona } from '@/types/superClaude';

interface AgentNotificationProps {
  agents: SuperClaudePersona[];
  onClose?: () => void;
}

const agentIcons: Record<SuperClaudePersona, React.ReactNode> = {
  architect: <Brain className="h-4 w-4" />,
  frontend: <Code2 className="h-4 w-4" />,
  backend: <Server className="h-4 w-4" />,
  security: <Shield className="h-4 w-4" />,
  performance: <Zap className="h-4 w-4" />,
  analyzer: <Search className="h-4 w-4" />,
  qa: <TestTube className="h-4 w-4" />,
  refactorer: <Wrench className="h-4 w-4" />,
  devops: <Cloud className="h-4 w-4" />,
  mentor: <GraduationCap className="h-4 w-4" />,
  scribe: <FileText className="h-4 w-4" />,
  requirements: <FileText className="h-4 w-4" />,
};

const agentColors: Record<SuperClaudePersona, string> = {
  architect: 'from-blue-500 to-blue-600',
  frontend: 'from-green-500 to-green-600',
  backend: 'from-purple-500 to-purple-600',
  security: 'from-red-500 to-red-600',
  performance: 'from-orange-500 to-orange-600',
  analyzer: 'from-cyan-500 to-cyan-600',
  qa: 'from-pink-500 to-pink-600',
  refactorer: 'from-yellow-500 to-yellow-600',
  devops: 'from-indigo-500 to-indigo-600',
  mentor: 'from-teal-500 to-teal-600',
  scribe: 'from-gray-500 to-gray-600',
  requirements: 'from-blue-400 to-blue-500',
};

const agentDescriptions: Record<SuperClaudePersona, string> = {
  architect: 'Systems architecture & long-term thinking',
  frontend: 'UI/UX & user-facing development',
  backend: 'Server-side & infrastructure',
  security: 'Threat modeling & vulnerability assessment',
  performance: 'Optimization & bottleneck elimination',
  analyzer: 'Root cause analysis & investigation',
  qa: 'Quality assurance & testing',
  refactorer: 'Code quality & technical debt',
  devops: 'Infrastructure & deployment automation',
  mentor: 'Knowledge transfer & education',
  scribe: 'Documentation & localization',
  requirements: 'Requirements analysis & specification',
};

export const AgentNotification: React.FC<AgentNotificationProps> = ({ agents, onClose }) => {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false);
      onClose?.();
    }, 5000);

    return () => clearTimeout(timer);
  }, [onClose]);

  if (agents.length === 0) return null;

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: -20, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: -20, scale: 0.95 }}
          transition={{ duration: 0.3 }}
          className="fixed top-4 right-4 z-50 max-w-md"
        >
          <div className="bg-gray-900 border border-gray-700 rounded-lg shadow-2xl overflow-hidden">
            {/* Header */}
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 px-4 py-2">
              <div className="flex items-center gap-2">
                <Sparkles className="h-4 w-4 text-white animate-pulse" />
                <span className="text-white font-semibold text-sm">
                  SuperClaude Agents Activated
                </span>
              </div>
            </div>

            {/* Agent List */}
            <div className="p-3 space-y-2">
              {agents.map((agent, index) => (
                <motion.div
                  key={agent}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-start gap-3"
                >
                  {/* Agent Icon */}
                  <div className={`p-2 rounded-lg bg-gradient-to-br ${agentColors[agent]} text-white`}>
                    {agentIcons[agent]}
                  </div>
                  
                  {/* Agent Info */}
                  <div className="flex-1">
                    <div className="font-medium text-gray-100 capitalize">
                      {agent}
                    </div>
                    <div className="text-xs text-gray-400">
                      {agentDescriptions[agent]}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Footer */}
            <div className="px-4 py-2 bg-gray-800 border-t border-gray-700">
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-400">
                  {agents.length} agent{agents.length > 1 ? 's' : ''} enhancing your prompt
                </span>
                <button
                  onClick={() => {
                    setIsVisible(false);
                    onClose?.();
                  }}
                  className="text-xs text-gray-400 hover:text-gray-200 transition-colors"
                >
                  Dismiss
                </button>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
import React, { use<PERSON>emo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  X,
  Keyboard,
  Sparkles,
  Zap,
  Target,
  ChevronRight,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { superClaudeService } from "@/services/superClaude";
import type { SuperClaudeCommand, SuperClaudeMode } from "@/types/superClaude";
import { cn } from "@/lib/utils";

interface SuperClaudeQuickReferenceProps {
  isOpen: boolean;
  onClose: () => void;
  currentMode: SuperClaudeMode;
  onCommandSelect?: (command: SuperClaudeCommand) => void;
  className?: string;
}

// Context-specific command suggestions could be added here
// const contextCommands: Record<string, string[]> = {
//   newProject: ['analyze', 'design', 'build', 'implement'],
//   debugging: ['troubleshoot', 'debug', 'analyze', 'explain'],
//   improvement: ['improve', 'refactor', 'optimize', 'review'],
//   documentation: ['document', 'explain', 'analyze'],
//   testing: ['test', 'troubleshoot', 'review'],
// };

const keyboardShortcuts = [
  { keys: 'Cmd+Shift+S', action: 'Open SuperClaude commands' },
  { keys: 'Cmd+?', action: 'Toggle quick reference' },
  { keys: 'Cmd+Shift+H', action: 'Open help panel' },
  { keys: 'Esc', action: 'Close dialogs' },
];

const modeEffects: Record<SuperClaudeMode, string[]> = {
  normal: ['Standard responses', 'Direct answers', 'No special enhancements'],
  brainstorming: ['Asks clarifying questions', 'Explores options', 'Collaborative approach'],
  introspection: ['Shows reasoning process', 'Transparent thinking', 'Meta-analysis'],
  'task-management': ['Organized execution', 'Progress tracking', 'Systematic approach'],
  orchestration: ['Smart tool selection', 'Resource optimization', 'Parallel execution'],
  'token-efficiency': ['Compressed output', 'Symbol usage', '30-50% token savings'],
  'business-panel': ['Multiple expert views', 'Strategic analysis', 'Business alignment'],
};

export const SuperClaudeQuickReference: React.FC<SuperClaudeQuickReferenceProps> = ({
  isOpen,
  onClose,
  currentMode,
  onCommandSelect,
  className,
}) => {
  const commands = superClaudeService.getAllCommands();

  // Determine context-relevant commands
  const suggestedCommands = useMemo(() => {
    // In a real implementation, this would analyze the current context
    // For now, we'll return common commands
    const commonCommands = ['analyze', 'build', 'improve', 'test', 'document'];
    return commands.filter(cmd => commonCommands.includes(cmd.name));
  }, [commands]);

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: 20, scale: 0.95 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        exit={{ opacity: 0, y: 20, scale: 0.95 }}
        transition={{ duration: 0.2 }}
        className={cn(
          "fixed bottom-4 right-4 w-96 z-50",
          className
        )}
      >
        <Card className="shadow-lg border-2">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Sparkles className="h-4 w-4 text-primary" />
                <CardTitle className="text-base">Quick Reference</CardTitle>
              </div>
              <Button variant="ghost" size="icon" className="h-6 w-6" onClick={onClose}>
                <X className="h-3 w-3" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Suggested Commands */}
            <div>
              <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                <Target className="h-3 w-3" />
                Suggested Commands
              </h4>
              <div className="space-y-1">
                {suggestedCommands.slice(0, 5).map((command) => (
                  <Button
                    key={command.name}
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start text-xs h-7"
                    onClick={() => onCommandSelect?.(command)}
                  >
                    <span className="font-mono">/sc:{command.name}</span>
                    <span className="ml-auto text-muted-foreground">
                      {command.description.split(' ').slice(0, 3).join(' ')}...
                    </span>
                  </Button>
                ))}
              </div>
            </div>

            <Separator />

            {/* Current Mode Effects */}
            <div>
              <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                <Zap className="h-3 w-3" />
                Current Mode: {currentMode}
              </h4>
              <div className="space-y-1">
                {modeEffects[currentMode].map((effect, idx) => (
                  <div key={idx} className="flex items-start gap-2 text-xs text-muted-foreground">
                    <ChevronRight className="h-3 w-3 mt-0.5" />
                    <span>{effect}</span>
                  </div>
                ))}
              </div>
            </div>

            <Separator />

            {/* Keyboard Shortcuts */}
            <div>
              <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                <Keyboard className="h-3 w-3" />
                Keyboard Shortcuts
              </h4>
              <div className="space-y-1">
                {keyboardShortcuts.map((shortcut, idx) => (
                  <div key={idx} className="flex items-center justify-between text-xs">
                    <span className="text-muted-foreground">{shortcut.action}</span>
                    <Badge variant="secondary" className="text-xs font-mono">
                      {shortcut.keys}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>

            <Separator />

            {/* Quick Tips */}
            <div>
              <h4 className="text-sm font-medium mb-2">Quick Tips</h4>
              <div className="space-y-1 text-xs text-muted-foreground">
                <p>• Use <code className="bg-muted px-1 rounded">@path</code> to target specific directories</p>
                <p>• Add <code className="bg-muted px-1 rounded">--focus</code> to target specific areas</p>
                <p>• Wave mode auto-activates for complex tasks</p>
                <p>• Personas activate automatically based on context</p>
              </div>
            </div>

            {/* Active Features */}
            <div className="flex gap-2 flex-wrap">
              {currentMode !== 'normal' && (
                <Badge variant="outline" className="text-xs">
                  Mode: {currentMode}
                </Badge>
              )}
              <Badge variant="outline" className="text-xs">
                22 Commands
              </Badge>
              <Badge variant="outline" className="text-xs">
                11 Personas
              </Badge>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </AnimatePresence>
  );
};
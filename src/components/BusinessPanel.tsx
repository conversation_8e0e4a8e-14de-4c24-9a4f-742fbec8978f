import React from "react";
import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";
import type { BusinessAnalysis } from "@/services/businessPanel/types";

interface BusinessPanelProps {
  analyses: BusinessAnalysis[];
  mode: string;
  content: string;
  className?: string;
}

const expertColors: Record<string, string> = {
  christensen: "bg-blue-500/10 text-blue-500 border-blue-500/20",
  porter: "bg-red-500/10 text-red-500 border-red-500/20",
  drucker: "bg-green-500/10 text-green-500 border-green-500/20",
  godin: "bg-yellow-500/10 text-yellow-500 border-yellow-500/20",
  kim_mauborgne: "bg-purple-500/10 text-purple-500 border-purple-500/20",
  collins: "bg-indigo-500/10 text-indigo-500 border-indigo-500/20",
  taleb: "bg-orange-500/10 text-orange-500 border-orange-500/20",
  meadows: "bg-teal-500/10 text-teal-500 border-teal-500/20",
  doumont: "bg-cyan-500/10 text-cyan-500 border-cyan-500/20",
};

const modeColors: Record<string, string> = {
  discussion: "bg-blue-500/10 text-blue-500 border-blue-500/20",
  debate: "bg-red-500/10 text-red-500 border-red-500/20",
  socratic: "bg-green-500/10 text-green-500 border-green-500/20",
};

const modeIcons: Record<string, string> = {
  discussion: "🗨️",
  debate: "⚔️",
  socratic: "❓",
};

export const BusinessPanel: React.FC<BusinessPanelProps> = ({
  analyses,
  mode,
  content,
  className,
}) => {
  if (analyses.length === 0) {
    return (
      <div className={cn("flex items-center justify-center h-full", className)}>
        <div className="text-center text-muted-foreground">
          <div className="text-4xl mb-2">💼</div>
          <p>Business panel analysis in progress...</p>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 8 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={cn("w-full space-y-6", className)}
    >
      {/* Header */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="text-2xl">💼</div>
            <h2 className="text-xl font-semibold">Business Panel Analysis</h2>
          </div>
          <Badge
            variant="outline"
            className={cn(
              "flex items-center gap-1 px-3 py-1 text-sm capitalize",
              modeColors[mode] || "bg-gray-500/10 text-gray-500 border-gray-500/20"
            )}
          >
            <span>{modeIcons[mode] || "📊"}</span>
            <span>{mode.replace("-", " ")}</span>
          </Badge>
        </div>
        
        {content && (
          <Card className="bg-muted/30">
            <CardContent className="p-4">
              <p className="text-sm text-muted-foreground">{content}</p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Expert Analyses */}
      <div className="space-y-4">
        {analyses.map((analysis, index) => (
          <motion.div
            key={analysis.expert}
            initial={{ opacity: 0, y: 8 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
          >
            <Card className="overflow-hidden">
              <CardHeader className={cn(
                "pb-3",
                expertColors[analysis.expert] || "bg-gray-500/10"
              )}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="text-xl">{analysis.symbol}</div>
                    <CardTitle className="text-lg flex items-center gap-2">
                      {analysis.expert.replace("_", " ")}
                      <Badge variant="secondary" className="text-xs">
                        {analysis.framework}
                      </Badge>
                    </CardTitle>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4 pt-4">
                {/* Key Insights */}
                {analysis.keyInsights.length > 0 && (
                  <div>
                    <h3 className="font-medium mb-2 flex items-center gap-2">
                      <span>💡</span> Key Insights
                    </h3>
                    <ul className="space-y-1">
                      {analysis.keyInsights.map((insight, i) => (
                        <li key={i} className="text-sm flex items-start gap-2">
                          <span className="text-muted-foreground mt-0.5">•</span>
                          <span>{insight}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                <Separator />

                {/* Recommendations */}
                {analysis.recommendations.length > 0 && (
                  <div>
                    <h3 className="font-medium mb-2 flex items-center gap-2">
                      <span>✅</span> Recommendations
                    </h3>
                    <ul className="space-y-1">
                      {analysis.recommendations.map((rec, i) => (
                        <li key={i} className="text-sm flex items-start gap-2">
                          <span className="text-muted-foreground mt-0.5">•</span>
                          <span>{rec}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Questions */}
                {analysis.questions && analysis.questions.length > 0 && (
                  <>
                    <Separator />
                    <div>
                      <h3 className="font-medium mb-2 flex items-center gap-2">
                        <span>❓</span> Strategic Questions
                      </h3>
                      <ul className="space-y-1">
                        {analysis.questions.map((question, i) => (
                          <li key={i} className="text-sm flex items-start gap-2">
                            <span className="text-muted-foreground mt-0.5">•</span>
                            <span>{question}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Executive Summary */}
      {analyses.length > 1 && (
        <motion.div
          initial={{ opacity: 0, y: 8 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: analyses.length * 0.1 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <span>📋</span> Executive Summary
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <h3 className="font-medium mb-2">Convergent Insights</h3>
                <div className="flex flex-wrap gap-2">
                  {analyses.slice(0, 2).map((analysis, i) => (
                    <Badge
                      key={i}
                      variant="outline"
                      className="text-xs"
                    >
                      {analysis.expert.replace("_", " ")}: {analysis.keyInsights[0]}
                    </Badge>
                  ))}
                </div>
              </div>
              
              <div>
                <h3 className="font-medium mb-2">Key Recommendations</h3>
                <div className="flex flex-wrap gap-2">
                  {analyses.slice(0, 3).flatMap(analysis => 
                    analysis.recommendations.slice(0, 1).map((rec, i) => (
                      <Badge
                        key={`${analysis.expert}-${i}`}
                        variant="outline"
                        className="text-xs"
                      >
                        {rec}
                      </Badge>
                    ))
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </motion.div>
  );
};
import React from "react";
import {
  Brain,
  Lightbulb,
  Target,
  Cpu,
  Zap,
  <PERSON>riefcase,
  Sparkles,
  Info,
  ChevronRight,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import type { SuperClaudeMode } from "@/types/superClaude";
import { modeGuides } from "@/data/superClaudeUseCases";
import { cn } from "@/lib/utils";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface SuperClaudeModeSelectorProps {
  currentMode: SuperClaudeMode;
  onModeChange: (mode: SuperClaudeMode) => void;
  className?: string;
}

const modeConfig: Record<SuperClaudeMode, {
  label: string;
  description: string;
  icon: React.ReactNode;
  color: string;
}> = {
  normal: {
    label: "Normal",
    description: "Standard Claude behavior",
    icon: <Sparkles className="h-4 w-4" />,
    color: "text-gray-500",
  },
  brainstorming: {
    label: "Brainstorming",
    description: "Collaborative discovery and ideation",
    icon: <Lightbulb className="h-4 w-4" />,
    color: "text-yellow-500",
  },
  introspection: {
    label: "Introspection",
    description: "Meta-cognitive analysis and reasoning",
    icon: <Brain className="h-4 w-4" />,
    color: "text-purple-500",
  },
  "task-management": {
    label: "Task Management",
    description: "Structured workflow execution",
    icon: <Target className="h-4 w-4" />,
    color: "text-blue-500",
  },
  orchestration: {
    label: "Orchestration",
    description: "Intelligent tool selection and routing",
    icon: <Cpu className="h-4 w-4" />,
    color: "text-green-500",
  },
  "token-efficiency": {
    label: "Token Efficiency",
    description: "Compressed communication mode",
    icon: <Zap className="h-4 w-4" />,
    color: "text-orange-500",
  },
  "business-panel": {
    label: "Business Panel",
    description: "Expert panel strategic analysis",
    icon: <Briefcase className="h-4 w-4" />,
    color: "text-indigo-500",
  },
};

export const SuperClaudeModeSelector: React.FC<SuperClaudeModeSelectorProps> = ({
  currentMode,
  onModeChange,
  className,
}) => {
  const current = modeConfig[currentMode];

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={cn(
            "flex items-center gap-2 h-8",
            currentMode !== "normal" && "border-primary/50",
            className
          )}
        >
          <span className={current.color}>{current.icon}</span>
          <span className="text-xs font-medium">{current.label}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[380px] max-h-[600px] overflow-y-auto">
        <DropdownMenuLabel className="flex items-center justify-between">
          <span>SuperClaude Mode</span>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Info className="h-3 w-3 text-muted-foreground cursor-help" />
              </TooltipTrigger>
              <TooltipContent className="max-w-sm">
                <p className="text-xs">
                  Modes change how Claude approaches tasks. They activate automatically based on context or can be selected manually.
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        {(Object.keys(modeConfig) as SuperClaudeMode[]).map((mode) => {
          const config = modeConfig[mode];
          const guide = modeGuides.find(g => g.mode === mode);
          const isActive = mode === currentMode;
          
          return (
            <DropdownMenuItem
              key={mode}
              onClick={() => onModeChange(mode)}
              className={cn(
                "flex items-start gap-3 p-3 cursor-pointer",
                isActive && "bg-primary/5"
              )}
            >
              <div className={cn("mt-0.5", config.color)}>
                {config.icon}
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-sm">{config.label}</span>
                  {isActive && (
                    <Badge variant="secondary" className="text-xs px-1.5 py-0">
                      Active
                    </Badge>
                  )}
                </div>
                <p className="text-xs text-muted-foreground mt-0.5">
                  {config.description}
                </p>
                {guide && (
                  <div className="mt-2 space-y-1">
                    <div className="flex items-start gap-1">
                      <ChevronRight className="h-3 w-3 mt-0.5 text-primary" />
                      <p className="text-xs text-muted-foreground">
                        <span className="font-medium">Best for:</span> {guide.whenToUse[0]}
                      </p>
                    </div>
                    {guide.autoTriggers.length > 0 && (
                      <div className="flex items-start gap-1">
                        <ChevronRight className="h-3 w-3 mt-0.5 text-primary" />
                        <p className="text-xs text-muted-foreground">
                          <span className="font-medium">Auto-triggers:</span> {guide.autoTriggers[0]}
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </DropdownMenuItem>
          );
        })}
        <DropdownMenuSeparator />
        <div className="px-3 py-2">
          <p className="text-xs text-muted-foreground">
            Modes automatically activate based on context, or select manually above.
          </p>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
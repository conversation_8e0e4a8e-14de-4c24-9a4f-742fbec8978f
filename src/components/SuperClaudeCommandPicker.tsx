import React, { useState, useMemo } from "react";
import { motion } from "framer-motion";
import {
  Search,
  ChevronRight,
  Sparkles,
  Code,
  FileSearch,
  TestTube,
  FileText,
  Settings,
  Zap,
  Shield,
  Cpu,
  Target,
  HelpCircle,
  Info,
  Lightbulb,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { superClaudeService } from "@/services/superClaude";
import type { SuperClaudeCommand, SuperClaudeCommandCategory } from "@/types/superClaude";
import { enhancedCommands } from "@/data/superClaudeUseCases";
import { cn } from "@/lib/utils";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface SuperClaudeCommandPickerProps {
  onSelect: (command: SuperClaudeCommand) => void;
  onClose: () => void;
  onOpenHelp?: () => void;
  className?: string;
}

const categoryIcons: Record<SuperClaudeCommandCategory, React.ReactNode> = {
  analysis: <FileSearch className="h-4 w-4" />,
  development: <Code className="h-4 w-4" />,
  quality: <Shield className="h-4 w-4" />,
  documentation: <FileText className="h-4 w-4" />,
  planning: <Target className="h-4 w-4" />,
  testing: <TestTube className="h-4 w-4" />,
  meta: <Settings className="h-4 w-4" />,
};

const categoryColors: Record<SuperClaudeCommandCategory, string> = {
  analysis: "text-blue-500",
  development: "text-green-500",
  quality: "text-purple-500",
  documentation: "text-yellow-500",
  planning: "text-orange-500",
  testing: "text-red-500",
  meta: "text-gray-500",
};

export const SuperClaudeCommandPicker: React.FC<SuperClaudeCommandPickerProps> = ({
  onSelect,
  onClose,
  onOpenHelp,
  className,
}) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<SuperClaudeCommandCategory | "all">("all");
  const [selectedCommand, setSelectedCommand] = useState<SuperClaudeCommand | null>(null);

  const commands = useMemo(() => {
    return superClaudeService.getAllCommands();
  }, []);

  const categories = useMemo(() => {
    const cats = new Set<SuperClaudeCommandCategory>();
    commands.forEach(cmd => cats.add(cmd.category));
    return Array.from(cats).sort();
  }, [commands]);

  const filteredCommands = useMemo(() => {
    let filtered = commands;

    if (selectedCategory !== "all") {
      filtered = filtered.filter(cmd => cmd.category === selectedCategory);
    }

    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        cmd =>
          cmd.name.toLowerCase().includes(query) ||
          cmd.description.toLowerCase().includes(query) ||
          cmd.category.toLowerCase().includes(query)
      );
    }

    return filtered;
  }, [commands, selectedCategory, searchQuery]);

  const handleSelectCommand = (command: SuperClaudeCommand) => {
    setSelectedCommand(command);
    onSelect(command);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Escape") {
      onClose();
    }
  };

  return (
    <div
      className={cn(
        "fixed inset-0 z-50 flex items-center justify-center bg-background/80 backdrop-blur-sm",
        className
      )}
      onClick={onClose}
      onKeyDown={handleKeyDown}
    >
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        transition={{ duration: 0.2 }}
        className="relative w-full max-w-3xl bg-background border rounded-lg shadow-lg"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center gap-3 mb-6">
            <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-primary/10">
              <Sparkles className="h-5 w-5 text-primary" />
            </div>
            <div>
              <h2 className="text-lg font-semibold">SuperClaude Commands</h2>
              <p className="text-sm text-muted-foreground">
                Select a command to enhance your development workflow
              </p>
            </div>
          </div>

          {/* Search */}
          <div className="relative mb-4">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Search commands..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
              autoFocus
            />
          </div>

          {/* Category Filter */}
          <div className="flex items-center gap-2 mb-4 overflow-x-auto pb-2">
            <Button
              variant={selectedCategory === "all" ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory("all")}
            >
              All
            </Button>
            {categories.map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(category)}
                className="flex items-center gap-2"
              >
                <span className={categoryColors[category]}>
                  {categoryIcons[category]}
                </span>
                <span className="capitalize">{category}</span>
              </Button>
            ))}
          </div>

          {/* Commands List */}
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {filteredCommands.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No commands found matching your search
              </div>
            ) : (
              filteredCommands.map((command) => {
                const enhanced = enhancedCommands[command.name];
                return (
                <motion.button
                  key={command.name}
                  whileHover={{ scale: 1.01 }}
                  whileTap={{ scale: 0.99 }}
                  onClick={() => handleSelectCommand(command)}
                  className={cn(
                    "w-full text-left p-3 rounded-lg border transition-colors",
                    selectedCommand?.name === command.name
                      ? "bg-primary/10 border-primary"
                      : "hover:bg-muted/50 border-border"
                  )}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className={categoryColors[command.category]}>
                          {categoryIcons[command.category]}
                        </span>
                        <span className="font-mono text-sm">/sc:{command.name}</span>
                        {command.waveEnabled && (
                          <Badge variant="secondary" className="text-xs">
                            <Zap className="h-3 w-3 mr-1" />
                            Wave
                          </Badge>
                        )}
                        {command.performanceProfile === "complex" && (
                          <Badge variant="outline" className="text-xs">
                            <Cpu className="h-3 w-3 mr-1" />
                            Complex
                          </Badge>
                        )}
                        {enhanced?.complexity && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Badge variant="outline" className="text-xs">
                                  {enhanced.complexity === 'beginner' && '🟢'}
                                  {enhanced.complexity === 'intermediate' && '🟡'}
                                  {enhanced.complexity === 'advanced' && '🔴'}
                                </Badge>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{enhanced.complexity} level command</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">
                        {command.description}
                      </p>
                      {enhanced?.bestFor && (
                        <div className="mb-2">
                          <p className="text-xs text-muted-foreground">
                            <span className="font-medium text-primary">Best for:</span> {enhanced.bestFor}
                          </p>
                        </div>
                      )}
                      {command.autoPersonas.length > 0 && (
                        <div className="flex items-center gap-2 text-xs">
                          <span className="text-muted-foreground">Personas:</span>
                          <div className="flex gap-1 flex-wrap">
                            {command.autoPersonas.map((persona) => (
                              <Badge key={persona} variant="outline" className="text-xs">
                                {persona}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                      {command.examples && command.examples.length > 0 && (
                        <div className="mt-2">
                          <p className="text-xs text-muted-foreground mb-1">Examples:</p>
                          <div className="space-y-1">
                            {command.examples.map((example, idx) => (
                              <code
                                key={idx}
                                className="block text-xs bg-muted px-2 py-1 rounded"
                              >
                                {example}
                              </code>
                            ))}
                          </div>
                        </div>
                      )}
                      {enhanced?.tips && enhanced.tips.length > 0 && (
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div className="inline-flex items-center gap-1 text-xs text-yellow-600 dark:text-yellow-400 cursor-help">
                                <Lightbulb className="h-3 w-3" />
                                <span>Pro tips available</span>
                              </div>
                            </TooltipTrigger>
                            <TooltipContent className="max-w-sm">
                              <div className="space-y-1">
                                {enhanced.tips.slice(0, 3).map((tip, idx) => (
                                  <p key={idx} className="text-xs">• {tip}</p>
                                ))}
                              </div>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      {enhanced && (
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button variant="ghost" size="icon" className="h-6 w-6">
                                <Info className="h-3 w-3" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent className="max-w-md">
                              <div className="space-y-2">
                                <p className="font-medium text-sm">When to use {command.name}:</p>
                                {enhanced.useCases?.slice(0, 3).map((useCase, idx) => (
                                  <p key={idx} className="text-xs">
                                    • <span className="font-medium">{useCase.title}:</span> {useCase.description}
                                  </p>
                                ))}
                                {enhanced.notFor && (
                                  <p className="text-xs text-yellow-600 dark:text-yellow-400 mt-2">
                                    <span className="font-medium">Not for:</span> {enhanced.notFor}
                                  </p>
                                )}
                              </div>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      )}
                      <ChevronRight className="h-4 w-4 text-muted-foreground" />
                    </div>
                  </div>
                </motion.button>
                );
              })
            )}
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between mt-6 pt-4 border-t">
            <div className="text-xs text-muted-foreground">
              {filteredCommands.length} commands available
            </div>
            <div className="flex gap-2">
              {onOpenHelp && (
                <Button variant="outline" size="sm" onClick={onOpenHelp}>
                  <HelpCircle className="h-3 w-3 mr-1" />
                  Help
                </Button>
              )}
              <Button variant="outline" size="sm" onClick={onClose}>
                Cancel
              </Button>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};
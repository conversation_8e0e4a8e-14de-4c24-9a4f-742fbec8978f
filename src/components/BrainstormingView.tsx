import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Brain, Lightbulb, Users, Target, CheckCircle2, Zap, Sparkles, RotateCcw } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { MindMapVisualization } from '@/components/MindMapVisualization';
import { 
  BrainstormingMode, 
  type BrainstormingSession, 
  type BrainstormingIdea,
  type MindMapNode
} from '@/services/modes/brainstorming';
import { claudeBrainstormingService } from '@/services/modes/claudeBrainstorming';
import { cn } from '@/lib/utils';

interface BrainstormingViewProps {
  topic: string;
  onIdeaSubmit: (idea: string, category: string) => void;
  className?: string;
}

export const BrainstormingView: React.FC<BrainstormingViewProps> = ({
  topic,
  onIdeaSubmit,
  className
}) => {
  const [session, setSession] = useState<BrainstormingSession | null>(null);
  const [currentPhase, setCurrentPhase] = useState<'divergent' | 'convergent' | 'synthesis'>('divergent');
  const [newIdea, setNewIdea] = useState('');
  const [ideaCategory, setIdeaCategory] = useState('general');
  const [mindMapData, setMindMapData] = useState<MindMapNode | null>(null);
  const [creativePrompts, setCreativePrompts] = useState<string[]>([]);
  const [showCreativePrompts, setShowCreativePrompts] = useState(false);
  
  const brainstormingMode = new BrainstormingMode();
  
  // Initialize session
  useEffect(() => {
    if (topic) {
      const newSession = brainstormingMode.orchestrateSession(topic, {
        keywords: topic.split(' '),
        complexity: 0.5
      });
      setSession(newSession);
      
      // Generate initial creative prompts
      const prompts = claudeBrainstormingService.generateCreativePrompts(topic, newSession);
      setCreativePrompts(prompts);
    }
  }, [topic]);
  
  // Update mind map when ideas change
  useEffect(() => {
    if (session?.ideas.length) {
      const mindMap = brainstormingMode.createMindMap(session.ideas);
      setMindMapData(mindMap);
    } else {
      setMindMapData({
        id: 'root',
        label: topic || 'Brainstorming Session',
        children: []
      });
    }
  }, [session?.ideas, topic]);
  
  const handleSubmitIdea = () => {
    if (newIdea.trim()) {
      onIdeaSubmit(newIdea, ideaCategory);
      
      // Add to local session
      if (session) {
        const idea: BrainstormingIdea = {
          content: newIdea,
          category: ideaCategory,
          score: Math.floor(Math.random() * 5) + 5, // Random score 5-10
          confidence: Math.random()
        };
        
        brainstormingMode.addIdea(session, idea);
        setSession({...session});
        setNewIdea('');
      }
    }
  };
  
  const handleClusterIdeas = () => {
    if (session) {
      const clusters = brainstormingMode.clusterIdeas(session.ideas);
      const recommendations = brainstormingMode.generateRecommendations(clusters);
      
      setSession({
        ...session,
        clusters,
        recommendations
      });
      
      setCurrentPhase('convergent');
    }
  };
  
  const handleGenerateNewPrompts = () => {
    if (session && topic) {
      const prompts = claudeBrainstormingService.generateCreativePrompts(topic, session);
      setCreativePrompts(prompts);
      setShowCreativePrompts(true);
    }
  };
  
  const handleApplyPrompt = (prompt: string) => {
    setNewIdea(prompt);
    setShowCreativePrompts(false);
  };
  
  const handleSCAMPER = () => {
    if (session && topic) {
      const scamperPrompts = claudeBrainstormingService.generateSCAMPERPrompts(topic);
      const prompts = Object.values(scamperPrompts);
      setCreativePrompts(prompts);
      setShowCreativePrompts(true);
    }
  };
  
  const discoveryMarkers = brainstormingMode.getConfig().markers;
  
  return (
    <div className={cn('space-y-6', className)}>
      {/* Session Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div className="flex items-center gap-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <Brain className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h2 className="text-2xl font-bold">Brainstorming Session</h2>
            <p className="text-muted-foreground">{topic}</p>
          </div>
        </div>
        
        <Badge variant="secondary" className="text-sm">
          {currentPhase === 'divergent' && 'Divergent Thinking'}
          {currentPhase === 'convergent' && 'Convergent Thinking'}
          {currentPhase === 'synthesis' && 'Synthesis'}
        </Badge>
      </motion.div>
      
      {/* Phase Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lightbulb className="h-5 w-5 text-yellow-500" />
            Current Phase: {session?.phases.find(p => p.id === currentPhase)?.name}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            {session?.phases.find(p => p.id === currentPhase)?.description}
          </p>
          <div className="flex flex-wrap gap-2 mt-3">
            {session?.phases.find(p => p.id === currentPhase)?.techniques.map(technique => (
              <Badge key={technique} variant="outline">{technique}</Badge>
            ))}
          </div>
        </CardContent>
      </Card>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Idea Input */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-primary" />
              Add New Idea
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Idea Content</label>
              <Textarea
                value={newIdea}
                onChange={(e) => setNewIdea(e.target.value)}
                placeholder="Describe your idea..."
                rows={3}
              />
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Category</label>
              <Input
                value={ideaCategory}
                onChange={(e) => setIdeaCategory(e.target.value)}
                placeholder="e.g., UI, Backend, Security"
              />
            </div>
            
            <div className="flex gap-2">
              <Button onClick={handleSubmitIdea} className="flex-1">
                Add Idea
              </Button>
              <Button 
                variant="outline" 
                onClick={handleGenerateNewPrompts}
                className="flex-1"
              >
                <Sparkles className="h-4 w-4 mr-2" />
                Creative Prompts
              </Button>
            </div>
          </CardContent>
        </Card>
        
        {/* Statistics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5 text-green-500" />
              Session Statistics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-primary/5 rounded-lg">
                <div className="text-2xl font-bold">{session?.ideas.length || 0}</div>
                <div className="text-sm text-muted-foreground">Ideas</div>
              </div>
              <div className="text-center p-3 bg-primary/5 rounded-lg">
                <div className="text-2xl font-bold">{session?.clusters.length || 0}</div>
                <div className="text-sm text-muted-foreground">Clusters</div>
              </div>
              <div className="text-center p-3 bg-primary/5 rounded-lg">
                <div className="text-2xl font-bold">{session?.recommendations.length || 0}</div>
                <div className="text-sm text-muted-foreground">Recommendations</div>
              </div>
              <div className="text-center p-3 bg-primary/5 rounded-lg">
                <div className="text-2xl font-bold">0</div>
                <div className="text-sm text-muted-foreground">Votes</div>
              </div>
            </div>
            
            <div className="mt-4">
              <Button 
                variant="outline" 
                onClick={handleSCAMPER}
                className="w-full"
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                SCAMPER Techniques
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Creative Prompts Panel */}
      <AnimatePresence>
        {showCreativePrompts && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Sparkles className="h-5 w-5 text-purple-500" />
                  Creative Prompt Suggestions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {creativePrompts.map((prompt, index) => (
                    <Button
                      key={index}
                      variant="outline"
                      onClick={() => handleApplyPrompt(prompt)}
                      className="text-left justify-start"
                    >
                      {prompt}
                    </Button>
                  ))}
                </div>
                <div className="mt-3 flex justify-end">
                  <Button 
                    variant="ghost" 
                    onClick={() => setShowCreativePrompts(false)}
                  >
                    Close
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Mind Map Visualization */}
      {mindMapData && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5 text-blue-500" />
              Idea Mind Map
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="border rounded-lg bg-muted/5 p-4 min-h-[300px]">
              <MindMapVisualization 
                data={mindMapData} 
                onNodeEdit={(node, content) => {
                  // Update the idea content in the session
                  if (session) {
                    const ideaIndex = session.ideas.findIndex(i => i.id === node.id);
                    if (ideaIndex !== -1) {
                      session.ideas[ideaIndex].content = content;
                      setSession({...session});
                    }
                  }
                }}
                onNodeDelete={(node) => {
                  // Remove the idea from the session
                  if (session) {
                    const ideaIndex = session.ideas.findIndex(i => i.id === node.id);
                    if (ideaIndex !== -1) {
                      session.ideas.splice(ideaIndex, 1);
                      setSession({...session});
                    }
                  }
                }}
                onNodeAdd={(parent, content) => {
                  // Add a new idea to the session
                  if (session) {
                    const idea: BrainstormingIdea = {
                      content,
                      category: parent.label,
                      score: Math.floor(Math.random() * 5) + 5,
                      confidence: Math.random()
                    };
                    
                    brainstormingMode.addIdea(session, idea);
                    setSession({...session});
                  }
                }}
              />
            </div>
          </CardContent>
        </Card>
      )}
      
      {/* Actions */}
      <div className="flex justify-end gap-3">
        <Button 
          variant="outline" 
          onClick={handleClusterIdeas}
          disabled={!session?.ideas.length}
        >
          Cluster Ideas
        </Button>
        <Button 
          variant="default"
          disabled={!session?.clusters.length}
          onClick={() => setCurrentPhase('synthesis')}
        >
          Generate Recommendations
        </Button>
      </div>
      
      {/* Recommendations */}
      <AnimatePresence>
        {session?.recommendations && session.recommendations.length > 0 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle2 className="h-5 w-5 text-green-500" />
                  Top Recommendations
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {session.recommendations.map((rec, index) => (
                    <div key={rec.id} className="flex items-start gap-3 p-3 bg-muted/10 rounded-lg">
                      <Badge variant="secondary">{index + 1}</Badge>
                      <div>
                        <h4 className="font-medium">{rec.title}</h4>
                        <p className="text-sm text-muted-foreground">{rec.description}</p>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant="outline" className="text-xs">
                            Confidence: {Math.round(rec.confidence * 100)}%
                          </Badge>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

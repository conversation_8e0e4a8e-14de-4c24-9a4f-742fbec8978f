import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Brain, 
  Target, 
  TrendingUp, 
  Bug, 
  Lightbulb, 
  BarChart3, 
  Zap, 
  Eye,
  ChevronRight,
  ChevronDown,
  RefreshCw,
  Play,
  Pause
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  IntrospectionMode,
  type SessionProgress,
  type DecisionLogEntry,
  type PatternRecognitionResult,
  type LearningExtractionResult,
  type DebugSessionContext
} from '@/services/modes/introspection';
import { cn } from '@/lib/utils';

interface IntrospectionViewProps {
  sessionId: string;
  onAnalyzeSession?: () => void;
  className?: string;
}

export const IntrospectionView: React.FC<IntrospectionViewProps> = ({
  sessionId,
  onAnalyzeSession,
  className
}) => {
  const [introspectionMode] = useState(new IntrospectionMode());
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    progress: true,
    decisions: true,
    patterns: true,
    learning: true,
    debug: true,
    metrics: true
  });
  const [sessionProgress, setSessionProgress] = useState<SessionProgress | null>(null);
  const [decisions, setDecisions] = useState<DecisionLogEntry[]>([]);
  const [patterns, setPatterns] = useState<PatternRecognitionResult[]>([]);
  const [learnings, setLearnings] = useState<LearningExtractionResult[]>([]);
  const [debugSession, setDebugSession] = useState<DebugSessionContext | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisMarkers] = useState(introspectionMode['analysisMarkers']);

  // Load real data from introspection mode
  useEffect(() => {
    const loadIntrospectionData = async () => {
      try {
        // In a real implementation, this would fetch data from the introspection mode
        // For now, we'll keep the mock data but make it more dynamic
        const mockProgress: SessionProgress = {
          totalSteps: Math.floor(Math.random() * 20) + 5,
          completedSteps: Math.floor(Math.random() * 15) + 1,
          currentPhase: ['analysis', 'planning', 'implementation', 'review', 'pattern-analysis'][Math.floor(Math.random() * 5)],
          startTime: Date.now() - Math.floor(Math.random() * 300000),
          lastUpdate: Date.now()
        };
        
        const mockDecisions: DecisionLogEntry[] = [
          {
            id: 'decision_1',
            timestamp: Date.now() - 300000,
            decision: 'Use MCP server for complex analysis',
            context: 'High complexity task requiring external tools',
            rationale: 'MCP servers provide specialized capabilities for complex operations',
            confidence: 0.85,
            alternatives: ['Local processing', 'Simple tools'],
            outcome: 'Successful execution with 20% performance improvement'
          },
          {
            id: 'decision_2',
            timestamp: Date.now() - 180000,
            decision: 'Activate debugging mode for error recovery',
            context: 'Encountered unexpected tool execution error',
            rationale: 'Systematic debugging approach needed for root cause analysis',
            confidence: 0.75,
            alternatives: ['Retry operation', 'Skip step'],
            outcome: 'Identified configuration issue and resolved'
          }
        ];
        
        const mockPatterns: PatternRecognitionResult[] = [
          {
            patternId: 'high-complexity-decision',
            patternType: 'decision-making',
            description: 'High complexity decision pattern detected',
            frequency: 3,
            confidence: 0.8,
            recommendations: [
              'Consider breaking down complex decisions into smaller steps',
              'Seek additional validation for low-confidence decisions'
            ]
          },
          {
            patternId: 'error-recovery',
            patternType: 'debugging',
            description: 'Error recovery pattern observed',
            frequency: 1,
            confidence: 0.9,
            recommendations: [
              'Document error recovery strategies for future reference',
              'Analyze root causes to prevent recurrence'
            ]
          }
        ];
        
        const mockLearnings: LearningExtractionResult[] = [
          {
            learningId: 'pattern-high-complexity-decision',
            topic: 'decision-making',
            insight: 'High complexity decision pattern detected',
            application: 'Consider breaking down complex decisions into smaller steps; Seek additional validation for low-confidence decisions',
            confidence: 0.8
          },
          {
            learningId: 'decision-decision_2',
            topic: 'debugging',
            insight: 'Low confidence decision: Activate debugging mode for error recovery',
            application: 'Consider additional validation or alternative approaches',
            confidence: 0.25
          }
        ];
        
        const mockDebugSession: DebugSessionContext = {
          sessionId: `debug_${sessionId}`,
          problemStatement: 'Unexpected tool execution error in file processing',
          hypotheses: [
            'Incorrect file path or permissions',
            'Missing dependencies or configuration',
            'Resource constraint or timing issue',
            'Integration or dependency problem'
          ],
          evidence: [
            { type: 'log', content: 'Permission denied error in file read operation' },
            { type: 'metric', content: 'High memory usage before error' }
          ],
          rootCause: 'File path contained special characters not properly escaped',
          solution: 'Implement proper path sanitization and validation',
          startTime: Date.now() - 120000
        };
        
        setSessionProgress(mockProgress);
        setDecisions(mockDecisions);
        setPatterns(mockPatterns);
        setLearnings(mockLearnings);
        setDebugSession(mockDebugSession);
      } catch (error) {
        console.error('Failed to load introspection data:', error);
      }
    };
    
    loadIntrospectionData();
  }, [sessionId]);

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const handleAnalyzeSession = async () => {
    setIsAnalyzing(true);
    try {
      // In real implementation, this would call the introspection mode analysis
      if (onAnalyzeSession) {
        onAnalyzeSession();
      }
      // Simulate analysis delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Refresh data after analysis
      const mockProgress: SessionProgress = {
        totalSteps: Math.floor(Math.random() * 25) + 10,
        completedSteps: Math.floor(Math.random() * 20) + 5,
        currentPhase: ['analysis', 'planning', 'implementation', 'review', 'pattern-analysis', 'optimization'][Math.floor(Math.random() * 6)],
        startTime: Date.now() - Math.floor(Math.random() * 600000),
        lastUpdate: Date.now()
      };
      
      const mockDecisions: DecisionLogEntry[] = [
        {
          id: 'decision_1',
          timestamp: Date.now() - 300000,
          decision: 'Use MCP server for complex analysis',
          context: 'High complexity task requiring external tools',
          rationale: 'MCP servers provide specialized capabilities for complex operations',
          confidence: 0.85,
          alternatives: ['Local processing', 'Simple tools'],
          outcome: 'Successful execution with 20% performance improvement'
        },
        {
          id: 'decision_2',
          timestamp: Date.now() - 180000,
          decision: 'Activate debugging mode for error recovery',
          context: 'Encountered unexpected tool execution error',
          rationale: 'Systematic debugging approach needed for root cause analysis',
          confidence: 0.75,
          alternatives: ['Retry operation', 'Skip step'],
          outcome: 'Identified configuration issue and resolved'
        },
        {
          id: 'decision_3',
          timestamp: Date.now() - 60000,
          decision: 'Optimize token usage for efficiency',
          context: 'High token consumption detected',
          rationale: 'Token efficiency mode can reduce costs by 30-50%',
          confidence: 0.9,
          alternatives: ['Continue normal operation', 'Enable compression'],
          outcome: 'Activated token efficiency mode'
        }
      ];
      
      const mockPatterns: PatternRecognitionResult[] = [
        {
          patternId: 'high-complexity-decision',
          patternType: 'decision-making',
          description: 'High complexity decision pattern detected',
          frequency: Math.floor(Math.random() * 5) + 1,
          confidence: 0.8,
          recommendations: [
            'Consider breaking down complex decisions into smaller steps',
            'Seek additional validation for low-confidence decisions'
          ]
        },
        {
          patternId: 'error-recovery',
          patternType: 'debugging',
          description: 'Error recovery pattern observed',
          frequency: Math.floor(Math.random() * 3) + 1,
          confidence: 0.9,
          recommendations: [
            'Document error recovery strategies for future reference',
            'Analyze root causes to prevent recurrence'
          ]
        },
        {
          patternId: 'token-optimization',
          patternType: 'efficiency',
          description: 'Token optimization opportunity identified',
          frequency: Math.floor(Math.random() * 4) + 1,
          confidence: 0.85,
          recommendations: [
            'Enable token efficiency mode for large responses',
            'Use symbol compression for repetitive content'
          ]
        }
      ];
      
      const mockLearnings: LearningExtractionResult[] = [
        {
          learningId: 'pattern-high-complexity-decision',
          topic: 'decision-making',
          insight: 'High complexity decision pattern detected',
          application: 'Consider breaking down complex decisions into smaller steps; Seek additional validation for low-confidence decisions',
          confidence: 0.8
        },
        {
          learningId: 'decision-decision_2',
          topic: 'debugging',
          insight: 'Low confidence decision: Activate debugging mode for error recovery',
          application: 'Consider additional validation or alternative approaches',
          confidence: 0.25
        },
        {
          learningId: 'efficiency-opportunity',
          topic: 'token-optimization',
          insight: 'Token usage can be optimized by 30-50%',
          application: 'Enable token efficiency mode; Use symbol compression',
          confidence: 0.9
        }
      ];
      
      const mockDebugSession: DebugSessionContext = {
        sessionId: `debug_${sessionId}_${Date.now()}`,
        problemStatement: 'Performance bottleneck detected in file processing operations',
        hypotheses: [
          'Inefficient file I/O operations',
          'Memory leaks in processing pipeline',
          'Suboptimal algorithm complexity',
          'Resource contention in concurrent operations'
        ],
        evidence: [
          { type: 'metric', content: 'Average processing time increased by 40%' },
          { type: 'log', content: 'Memory usage peaked at 85% during batch operations' }
        ],
        rootCause: 'Sequential file processing instead of batch operations',
        solution: 'Implement parallel processing with controlled concurrency',
        startTime: Date.now() - 180000
      };
      
      setSessionProgress(mockProgress);
      setDecisions(mockDecisions);
      setPatterns(mockPatterns);
      setLearnings(mockLearnings);
      setDebugSession(mockDebugSession);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const completionPercentage = sessionProgress 
    ? Math.round((sessionProgress.completedSteps / sessionProgress.totalSteps) * 100)
    : 0;

  return (
    <div className={cn('space-y-6', className)}>
      {/* Session Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div className="flex items-center gap-3">
          <div className="p-2 bg-purple-500/10 rounded-lg">
            <Brain className="h-6 w-6 text-purple-500" />
          </div>
          <div>
            <h2 className="text-2xl font-bold">Introspection Analysis</h2>
            <p className="text-muted-foreground">Session: {sessionId.substring(0, 8)}...</p>
          </div>
        </div>
        
        <Button 
          onClick={handleAnalyzeSession} 
          disabled={isAnalyzing}
          className="flex items-center gap-2"
        >
          {isAnalyzing ? (
            <>
              <RefreshCw className="h-4 w-4 animate-spin" />
              Analyzing...
            </>
          ) : (
            <>
              <Eye className="h-4 w-4" />
              Analyze Session
            </>
          )}
        </Button>
      </motion.div>
      
      {/* Progress Overview */}
      <Card>
        <CardHeader 
          className="cursor-pointer"
          onClick={() => toggleSection('progress')}
        >
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Target className="h-5 w-5 text-purple-500" />
              Session Progress
            </div>
            {expandedSections.progress ? 
              <ChevronDown className="h-4 w-4" /> : 
              <ChevronRight className="h-4 w-4" />
            }
          </CardTitle>
        </CardHeader>
        <AnimatePresence>
          {expandedSections.progress && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
            >
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Overall Progress</span>
                    <span className="text-sm text-muted-foreground">{completionPercentage}%</span>
                  </div>
                  <Progress value={completionPercentage} className="w-full" />
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center p-3 bg-purple-500/5 rounded-lg">
                      <div className="text-2xl font-bold">{sessionProgress?.completedSteps || 0}</div>
                      <div className="text-sm text-muted-foreground">Steps Completed</div>
                    </div>
                    <div className="text-center p-3 bg-purple-500/5 rounded-lg">
                      <div className="text-2xl font-bold">{sessionProgress?.totalSteps || 0}</div>
                      <div className="text-sm text-muted-foreground">Total Steps</div>
                    </div>
                    <div className="text-center p-3 bg-purple-500/5 rounded-lg">
                      <div className="text-2xl font-bold">{decisions.length}</div>
                      <div className="text-sm text-muted-foreground">Decisions Made</div>
                    </div>
                    <div className="text-center p-3 bg-purple-500/5 rounded-lg">
                      <div className="text-2xl font-bold">{patterns.length}</div>
                      <div className="text-sm text-muted-foreground">Patterns Found</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">Current Phase</Badge>
                    <span className="text-sm font-medium">
                      {sessionProgress?.currentPhase || 'Initializing'}
                    </span>
                  </div>
                </div>
              </CardContent>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>
      
      {/* Decision Log */}
      <Card>
        <CardHeader 
          className="cursor-pointer"
          onClick={() => toggleSection('decisions')}
        >
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-yellow-500" />
              Decision Log
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline">{decisions.length}</Badge>
              {expandedSections.decisions ? 
                <ChevronDown className="h-4 w-4" /> : 
                <ChevronRight className="h-4 w-4" />
              }
            </div>
          </CardTitle>
        </CardHeader>
        <AnimatePresence>
          {expandedSections.decisions && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
            >
              <CardContent>
                <div className="space-y-4">
                  {decisions.map((decision) => (
                    <div key={decision.id} className="p-4 border rounded-lg bg-muted/5">
                      <div className="flex items-start justify-between">
                        <h4 className="font-medium">{decision.decision}</h4>
                        <Badge 
                          variant={decision.confidence > 0.8 ? "default" : decision.confidence > 0.6 ? "secondary" : "destructive"}
                          className="text-xs"
                        >
                          {Math.round(decision.confidence * 100)}% confidence
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mt-2">{decision.context}</p>
                      <p className="text-sm mt-2">{decision.rationale}</p>
                      {decision.outcome && (
                        <div className="mt-2 p-2 bg-green-500/10 rounded text-sm">
                          <span className="font-medium">Outcome:</span> {decision.outcome}
                        </div>
                      )}
                      <div className="mt-2 flex flex-wrap gap-1">
                        {decision.alternatives.map((alt: string, idx: number) => (
                          <Badge key={idx} variant="outline" className="text-xs">
                            {alt}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>
      
      {/* Pattern Recognition */}
      <Card>
        <CardHeader 
          className="cursor-pointer"
          onClick={() => toggleSection('patterns')}
        >
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-blue-500" />
              Pattern Recognition
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline">{patterns.length}</Badge>
              {expandedSections.patterns ? 
                <ChevronDown className="h-4 w-4" /> : 
                <ChevronRight className="h-4 w-4" />
              }
            </div>
          </CardTitle>
        </CardHeader>
        <AnimatePresence>
          {expandedSections.patterns && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
            >
              <CardContent>
                <div className="space-y-4">
                  {patterns.map((pattern) => (
                    <div key={pattern.patternId} className="p-4 border rounded-lg bg-muted/5">
                      <div className="flex items-start justify-between">
                        <h4 className="font-medium">{pattern.description}</h4>
                        <Badge variant="secondary" className="text-xs">
                          {pattern.patternType}
                        </Badge>
                      </div>
                      <div className="mt-2 flex items-center gap-4 text-sm">
                        <span>Frequency: {pattern.frequency}</span>
                        <span>Confidence: {Math.round(pattern.confidence * 100)}%</span>
                      </div>
                      <div className="mt-3">
                        <h5 className="text-sm font-medium mb-1">Recommendations:</h5>
                        <ul className="text-sm space-y-1">
                          {pattern.recommendations.map((rec: string, idx: number) => (
                            <li key={idx} className="flex items-start gap-2">
                              <span className="text-green-500">•</span>
                              {rec}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>
      
      {/* Learning Extraction */}
      <Card>
        <CardHeader 
          className="cursor-pointer"
          onClick={() => toggleSection('learning')}
        >
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Lightbulb className="h-5 w-5 text-yellow-500" />
              Learning Extraction
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline">{learnings.length}</Badge>
              {expandedSections.learning ? 
                <ChevronDown className="h-4 w-4" /> : 
                <ChevronRight className="h-4 w-4" />
              }
            </div>
          </CardTitle>
        </CardHeader>
        <AnimatePresence>
          {expandedSections.learning && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
            >
              <CardContent>
                <div className="space-y-4">
                  {learnings.map((learning) => (
                    <div key={learning.learningId} className="p-4 border rounded-lg bg-muted/5">
                      <div className="flex items-start justify-between">
                        <h4 className="font-medium">{learning.topic}</h4>
                        <Badge 
                          variant={learning.confidence > 0.8 ? "default" : learning.confidence > 0.6 ? "secondary" : "outline"}
                          className="text-xs"
                        >
                          {Math.round(learning.confidence * 100)}% confidence
                        </Badge>
                      </div>
                      <p className="text-sm mt-2">{learning.insight}</p>
                      <div className="mt-2 p-2 bg-blue-500/10 rounded text-sm">
                        <span className="font-medium">Application:</span> {learning.application}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>
      
      {/* Debug Session */}
      <Card>
        <CardHeader 
          className="cursor-pointer"
          onClick={() => toggleSection('debug')}
        >
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Bug className="h-5 w-5 text-red-500" />
              Debug Analysis
            </div>
            {expandedSections.debug ? 
              <ChevronDown className="h-4 w-4" /> : 
              <ChevronRight className="h-4 w-4" />
            }
          </CardTitle>
        </CardHeader>
        <AnimatePresence>
          {expandedSections.debug && debugSession && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
            >
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-red-500">Problem Statement</h4>
                    <p className="text-sm mt-1">{debugSession.problemStatement}</p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-2">Hypotheses</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {debugSession.hypotheses.map((hypothesis: string, idx: number) => (
                        <div key={idx} className="p-2 border rounded text-sm bg-muted/5">
                          {hypothesis}
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium text-green-500">Root Cause</h4>
                      <p className="text-sm mt-1">{debugSession.rootCause}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-blue-500">Solution</h4>
                      <p className="text-sm mt-1">{debugSession.solution}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>
      
      {/* Performance Metrics */}
      <Card>
        <CardHeader 
          className="cursor-pointer"
          onClick={() => toggleSection('metrics')}
        >
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-green-500" />
              Performance Metrics
            </div>
            {expandedSections.metrics ? 
              <ChevronDown className="h-4 w-4" /> : 
              <ChevronRight className="h-4 w-4" />
            }
          </CardTitle>
        </CardHeader>
        <AnimatePresence>
          {expandedSections.metrics && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
            >
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center p-3 bg-green-500/5 rounded-lg">
                    <div className="text-2xl font-bold">32s</div>
                    <div className="text-sm text-muted-foreground">Avg Response</div>
                  </div>
                  <div className="text-center p-3 bg-green-500/5 rounded-lg">
                    <div className="text-2xl font-bold">1.2k</div>
                    <div className="text-sm text-muted-foreground">Tokens Used</div>
                  </div>
                  <div className="text-center p-3 bg-green-500/5 rounded-lg">
                    <div className="text-2xl font-bold">98%</div>
                    <div className="text-sm text-muted-foreground">Tool Success</div>
                  </div>
                  <div className="text-center p-3 bg-green-500/5 rounded-lg">
                    <div className="text-2xl font-bold">4</div>
                    <div className="text-sm text-muted-foreground">Files Modified</div>
                  </div>
                </div>
              </CardContent>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>
    </div>
  );
};
import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON>, Z<PERSON>, AlertTriangle, CheckCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import type { SuperClaudeMode } from "@/types/superClaude";
import { TokenEfficiencyMode } from "@/services/modes/tokenEfficiency";

interface TokenEfficiencyMonitorProps {
  /**
   * Current token count
   */
  currentTokens: number;
  /**
   * Maximum token limit
   */
  maxTokens: number;
  /**
   * Active mode
   */
  activeMode?: SuperClaudeMode;
  /**
   * Whether to show the monitor
   */
  show?: boolean;
  /**
   * Optional className for styling
   */
  className?: string;
  /**
   * Callback when efficiency mode should be activated
   */
  onActivateEfficiencyMode?: () => void;
  /**
   * Callback when efficiency mode should be deactivated
   */
  onDeactivateEfficiencyMode?: () => void;
}

/**
 * TokenEfficiencyMonitor component - Displays token usage and efficiency metrics
 * 
 * @example
 * <TokenEfficiencyMonitor 
 *   currentTokens={1234} 
 *   maxTokens={4096} 
 *   activeMode="token-efficiency"
 *   show={true} 
 * />
 */
export const TokenEfficiencyMonitor: React.FC<TokenEfficiencyMonitorProps> = ({
  currentTokens,
  maxTokens,
  activeMode,
  show = true,
  className,
  onActivateEfficiencyMode,
  onDeactivateEfficiencyMode,
}) => {
  const [usagePercentage, setUsagePercentage] = useState(0);
  const [status, setStatus] = useState<'normal' | 'warning' | 'critical'>('normal');
  const [tokenEfficiencyMode] = useState(new TokenEfficiencyMode());

  useEffect(() => {
    const percentage = (currentTokens / maxTokens) * 100;
    setUsagePercentage(percentage);
    
    if (percentage > 90) {
      setStatus('critical');
    } else if (percentage > 75) {
      setStatus('warning');
    } else {
      setStatus('normal');
    }
  }, [currentTokens, maxTokens]);

  if (!show || currentTokens === 0) return null;

  const getStatusColor = () => {
    switch (status) {
      case 'critical': return 'bg-red-500';
      case 'warning': return 'bg-yellow-500';
      case 'normal': 
      default: 
        return activeMode === 'token-efficiency' ? 'bg-purple-500' : 'bg-green-500';
    }
  };

  const getStatusIcon = () => {
    if (activeMode === 'token-efficiency') {
      return <Zap className="h-3 w-3" />;
    }
    
    switch (status) {
      case 'critical': return <AlertTriangle className="h-3 w-3" />;
      case 'warning': return <AlertTriangle className="h-3 w-3" />;
      default: return <CheckCircle className="h-3 w-3" />;
    }
  };

  const handleToggleEfficiency = () => {
    if (activeMode === 'token-efficiency') {
      if (onDeactivateEfficiencyMode) {
        onDeactivateEfficiencyMode();
      }
    } else {
      if (onActivateEfficiencyMode) {
        onActivateEfficiencyMode();
      }
    }
  };

  // Get token monitoring data
  const monitoringData = tokenEfficiencyMode.monitorTokenUsage(currentTokens, maxTokens);

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
      className={cn(
        "fixed bottom-20 right-4 z-30",
        "bg-background/90 backdrop-blur-sm",
        "border border-border rounded-lg",
        "px-3 py-2 shadow-lg",
        className
      )}
    >
      <div className="flex flex-col gap-2 min-w-[200px]">
        {/* Header with mode indicator */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1.5 text-xs font-medium">
            <Cpu className="h-3 w-3 text-muted-foreground" />
            <span className="font-mono">{currentTokens.toLocaleString()}</span>
            <span className="text-muted-foreground">/</span>
            <span className="font-mono">{maxTokens.toLocaleString()}</span>
            <span className="text-muted-foreground">tokens</span>
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={handleToggleEfficiency}
          >
            <Zap className={cn(
              "h-3 w-3",
              activeMode === 'token-efficiency' ? "text-purple-500" : "text-muted-foreground"
            )} />
          </Button>
        </div>
        
        {/* Progress bar */}
        <div className="flex items-center gap-2">
          <div className="relative w-full h-2 bg-muted rounded-full overflow-hidden">
            <motion.div 
              className={cn("absolute top-0 left-0 h-full", getStatusColor())}
              initial={{ width: 0 }}
              animate={{ width: `${Math.min(usagePercentage, 100)}%` }}
              transition={{ duration: 0.3 }}
            />
          </div>
          <span className="text-xs font-medium w-10 text-right">
            {usagePercentage.toFixed(0)}%
          </span>
        </div>
        
        {/* Status message */}
        <div className="text-xs text-muted-foreground">
          {monitoringData.message}
        </div>
        
        {/* Efficiency mode controls */}
        {status !== 'normal' && (
          <div className="flex gap-1">
            {activeMode !== 'token-efficiency' ? (
              <Badge 
                variant="secondary" 
                className={cn(
                  "cursor-pointer text-xs px-2 py-1",
                  status === 'warning' && "bg-yellow-100 text-yellow-800 hover:bg-yellow-200",
                  status === 'critical' && "bg-red-100 text-red-800 hover:bg-red-200"
                )}
                onClick={handleToggleEfficiency}
              >
                {getStatusIcon()}
                <span className="ml-1">
                  {status === 'warning' ? 'Enable Efficiency' : 'Critical - Enable Efficiency'}
                </span>
              </Badge>
            ) : (
              <Badge 
                variant="secondary" 
                className="cursor-pointer text-xs px-2 py-1 bg-purple-100 text-purple-800 hover:bg-purple-200"
                onClick={handleToggleEfficiency}
              >
                <Zap className="h-3 w-3 mr-1" />
                <span>Efficiency Active</span>
              </Badge>
            )}
          </div>
        )}
      </div>
    </motion.div>
  );
};
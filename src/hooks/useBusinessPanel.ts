import { useState, useEffect } from "react";
import { invoke } from "@tauri-apps/api/core";
import type { BusinessExpert, BusinessAnalysis, BusinessPanelSession } from "@/services/businessPanel/types";

interface UseBusinessPanelProps {
  initialContent?: string;
  initialMode?: string;
  initialExperts?: string[];
}

export interface BusinessPanelHook {
  experts: BusinessExpert[];
  session: BusinessPanelSession | null;
  analyses: BusinessAnalysis[];
  loading: boolean;
  error: string | null;
  selectedExperts: string[];
  mode: string;
  listExperts: () => Promise<void>;
  createSession: (content: string, mode?: string, experts?: string[]) => Promise<void>;
  executeAnalysis: () => Promise<void>;
  setSelectedExperts: (experts: string[]) => void;
  setMode: (mode: string) => void;
}

export const useBusinessPanel = ({
  initialContent: _initialContent = "",
  initialMode = "discussion",
  initialExperts = [],
}: UseBusinessPanelProps = {}): BusinessPanelHook => {
  const [experts, setExperts] = useState<BusinessExpert[]>([]);
  const [session, setSession] = useState<BusinessPanelSession | null>(null);
  const [analyses, setAnalyses] = useState<BusinessAnalysis[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedExperts, setSelectedExperts] = useState<string[]>(initialExperts);
  const [mode, setMode] = useState(initialMode);

  const listExperts = async () => {
    try {
      setLoading(true);
      setError(null);
      const expertList = await invoke<BusinessExpert[]>("list_business_experts");
      setExperts(expertList);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load experts");
      console.error("Failed to list business experts:", err);
    } finally {
      setLoading(false);
    }
  };

  const createSession = async (content: string, sessionMode?: string, experts?: string[]) => {
    try {
      setLoading(true);
      setError(null);
      const newSession = await invoke<BusinessPanelSession>("create_business_panel_session", {
        content,
        mode: sessionMode,
        selectedExperts: experts,
      });
      setSession(newSession);
      setAnalyses([]);
      setMode(sessionMode || initialMode);
      setSelectedExperts(experts || initialExperts);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to create session");
      console.error("Failed to create business panel session:", err);
    } finally {
      setLoading(false);
    }
  };

  const executeAnalysis = async () => {
    if (!session) {
      setError("No session available");
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const results = await invoke<BusinessAnalysis[]>("execute_business_analysis", {
        sessionId: session.id,
      });
      setAnalyses(results);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to execute analysis");
      console.error("Failed to execute business analysis:", err);
    } finally {
      setLoading(false);
    }
  };

  // Load experts on mount
  useEffect(() => {
    listExperts();
  }, []);

  return {
    experts,
    session,
    analyses,
    loading,
    error,
    selectedExperts,
    mode,
    listExperts,
    createSession,
    executeAnalysis,
    setSelectedExperts,
    setMode,
  };
};
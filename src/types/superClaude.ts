/**
 * TypeScript type definitions for SuperClaude Framework integration
 */

/**
 * SuperClaude command types
 */
export type SuperClaudeCommandCategory = 
  | 'analysis'
  | 'development'
  | 'quality'
  | 'documentation'
  | 'planning'
  | 'testing'
  | 'meta';

export interface SuperClaudeCommand {
  name: string;
  description: string;
  category: SuperClaudeCommandCategory;
  waveEnabled: boolean;
  performanceProfile: 'optimization' | 'standard' | 'complex';
  autoPersonas: string[];
  mcpServers: string[];
  arguments?: string;
  flags?: string[];
  examples?: string[];
}

/**
 * SuperClaude modes
 */
export type SuperClaudeMode = 
  | 'normal'
  | 'brainstorming'
  | 'introspection'
  | 'task-management'
  | 'orchestration'
  | 'token-efficiency'
  | 'business-panel';

export interface SuperClaudeModeConfig {
  id: SuperClaudeMode;
  name: string;
  description: string;
  activationTriggers: string[];
  behaviorChanges: string[];
  symbolsEnabled: boolean;
}

/**
 * SuperClaude personas
 */
export type SuperClaudePersona = 
  | 'architect'
  | 'frontend'
  | 'backend'
  | 'security'
  | 'performance'
  | 'analyzer'
  | 'qa'
  | 'refactorer'
  | 'devops'
  | 'mentor'
  | 'scribe'
  | 'requirements';

export interface SuperClaudePersonaConfig {
  id: SuperClaudePersona;
  name: string;
  description: string;
  priorityHierarchy: string[];
  corePrinciples: string[];
  mcpPreferences: {
    primary: string[];
    secondary: string[];
    avoided: string[];
  };
  autoActivationTriggers: string[];
}

/**
 * Business Panel experts
 */
export type BusinessPanelExpert = 
  | 'christensen'
  | 'porter'
  | 'drucker'
  | 'godin'
  | 'kim_mauborgne'
  | 'collins'
  | 'taleb'
  | 'meadows'
  | 'doumont';

export interface BusinessPanelExpertConfig {
  id: BusinessPanelExpert;
  name: string;
  framework: string;
  focusAreas: string[];
  communicationStyle: string;
  symbol: string;
}

/**
 * SuperClaude symbols for token efficiency
 */
export interface SuperClaudeSymbol {
  symbol: string;
  meaning: string;
  domain?: string;
  example?: string;
}

export interface SuperClaudeSymbolCategory {
  name: string;
  description: string;
  symbols: SuperClaudeSymbol[];
}

/**
 * Wave orchestration
 */
export interface SuperClaudeWave {
  waveNumber: number;
  phase: 'review' | 'planning' | 'implementation' | 'validation' | 'optimization';
  operations: string[];
  persona?: SuperClaudePersona;
  mcpServers?: string[];
}

export interface SuperClaudeWaveStrategy {
  name: 'progressive' | 'systematic' | 'adaptive' | 'enterprise';
  description: string;
  waves: SuperClaudeWave[];
  validationGates: boolean;
}

/**
 * MCP Server configuration
 */
export interface SuperClaudeMCPServer {
  name: string;
  purpose: string;
  primaryUseCase: string[];
  activationPatterns: string[];
  errorRecovery: string;
}

/**
 * SuperClaude configuration
 */
export interface SuperClaudeConfig {
  commands: SuperClaudeCommand[];
  modes: SuperClaudeModeConfig[];
  personas: SuperClaudePersonaConfig[];
  businessPanelExperts: BusinessPanelExpertConfig[];
  symbolCategories: SuperClaudeSymbolCategory[];
  mcpServers: SuperClaudeMCPServer[];
  waveStrategies: SuperClaudeWaveStrategy[];
}

/**
 * SuperClaude session state
 */
export interface SuperClaudeSessionState {
  activeMode: SuperClaudeMode;
  activePersonas: SuperClaudePersona[];
  activeWaveStrategy?: SuperClaudeWaveStrategy;
  currentWave?: number;
  tokenEfficiencyEnabled: boolean;
  businessPanelActive: boolean;
  activeExperts?: BusinessPanelExpert[];
  mcpServersActive: string[];
}

/**
 * Command execution context
 */
export interface SuperClaudeCommandContext {
  command: string;
  arguments?: string;
  flags?: string[];
  mode?: SuperClaudeMode;
  personas?: SuperClaudePersona[];
  waveStrategy?: SuperClaudeWaveStrategy;
  mcpServers?: string[];
}

/**
 * Command result
 */
export interface SuperClaudeCommandResult {
  success: boolean;
  output?: string;
  error?: string;
  tokensUsed?: number;
  wavesCompleted?: number;
  artifactsCreated?: string[];
}

/**
 * Mode activation context
 */
export interface ModeActivationContext {
  resourceUsage?: number;
  complexity?: number;
  multiToolOperation?: boolean;
  errorRecovery?: boolean;
  fileCount?: number;
  directoryCount?: number;
}

/**
 * Persona activation context
 */
export interface PersonaActivationContext {
  domain?: 'frontend' | 'backend' | 'infrastructure' | 'security' | 'documentation';
  operationType?: 'analysis' | 'creation' | 'modification' | 'debugging';
  complexity?: number;
  userHistory?: Record<string, any>;
  performanceMetrics?: Record<string, number>;
}
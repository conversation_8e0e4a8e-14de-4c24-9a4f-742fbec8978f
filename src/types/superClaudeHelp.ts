/**
 * SuperClaude Help System Types
 */

export interface CommandExample {
  command: string;
  description: string;
  scenario: string;
  result?: string;
}

export interface CommandUseCase {
  title: string;
  description: string;
  icon?: string;
}

export interface EnhancedSuperClaudeCommand {
  name: string;
  description: string;
  category: string;
  waveEnabled: boolean;
  performanceProfile: 'optimization' | 'standard' | 'complex';
  autoPersonas: string[];
  mcpServers: string[];
  arguments?: string;
  examples?: CommandExample[];
  useCases: CommandUseCase[];
  bestFor: string;
  notFor: string;
  relatedCommands: string[];
  tips: string[];
  complexity: 'beginner' | 'intermediate' | 'advanced';
  commonMistakes?: string[];
  successIndicators?: string[];
}

export interface ModeGuide {
  mode: string;
  label: string;
  description: string;
  whenToUse: string[];
  benefits: string[];
  examples: string[];
  autoTriggers: string[];
  icon: string;
  color: string;
}

export interface PersonaGuide {
  name: string;
  role: string;
  expertise: string[];
  activatesOn: string[];
  bestFor: string[];
  worksWith: string[];
  icon: string;
  color: string;
}

export interface WorkflowGuide {
  name: string;
  description: string;
  steps: WorkflowStep[];
  category: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
}

export interface WorkflowStep {
  step: number;
  command: string;
  description: string;
  expectedOutcome: string;
}

export interface QuickReferenceItem {
  command: string;
  shortDescription: string;
  keyboardShortcut?: string;
  frequency: 'common' | 'occasional' | 'rare';
}

export interface HelpSection {
  id: string;
  title: string;
  content: string;
  examples?: CommandExample[];
  tips?: string[];
  relatedSections?: string[];
}

export interface TutorialStep {
  id: string;
  title: string;
  content: string;
  action?: string;
  highlightElement?: string;
  position?: 'top' | 'bottom' | 'left' | 'right' | 'center';
}

export interface CommandRecommendation {
  command: string;
  reason: string;
  confidence: number;
  context: string;
}
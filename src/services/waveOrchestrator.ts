/**
 * Wave Orchestration Engine for SuperClaude Framework
 * Handles multi-stage command execution with compound intelligence
 */

import type {
  SuperClaudeWave,
  SuperClaudeWaveStrategy,
  SuperClaudeCommand,
  SuperClaudePersona,
  SuperClaudeCommandContext,
  SuperClaudeCommandResult,
} from '@/types/superClaude';
// import { superClaudeService } from './superClaude';
import { api } from '@/lib/api';
import { useSuperClaudeStore } from '@/stores/superClaudeStore';

/**
 * Wave execution options
 */
export interface WaveExecutionOptions {
  validateBetweenWaves?: boolean;
  parallelExecution?: boolean;
  adaptiveWaveSize?: boolean;
  checkpointAfterWave?: boolean;
  maxRetries?: number;
  timeoutPerWave?: number; // milliseconds
}

/**
 * Wave validation result
 */
interface WaveValidationResult {
  passed: boolean;
  issues: string[];
  recommendations: string[];
  canProceed: boolean;
}

/**
 * Wave execution result
 */
export interface WaveExecutionResult {
  waveNumber: number;
  phase: string;
  success: boolean;
  output?: string;
  artifacts?: string[];
  tokensUsed?: number;
  duration?: number;
  validation?: WaveValidationResult;
}

/**
 * Wave orchestrator class
 */
export class WaveOrchestrator {
  private currentStrategy?: SuperClaudeWaveStrategy;
  private currentWave: number = 0;
  private isExecuting: boolean = false;
  private abortController?: AbortController;
  private waveResults: Map<number, WaveExecutionResult> = new Map();
  
  /**
   * Determine if a command should use wave execution
   */
  static shouldUseWaveExecution(
    command: SuperClaudeCommand,
    context?: Partial<SuperClaudeCommandContext>
  ): boolean {
    // Check if command is wave-enabled
    if (!command.waveEnabled) {
      return false;
    }
    
    // Check complexity indicators
    const complexityScore = this.calculateComplexityScore(command, context);
    
    // Check scale indicators
    const scaleScore = this.calculateScaleScore(context);
    
    // Wave activation threshold
    const WAVE_THRESHOLD = 0.7;
    const totalScore = (complexityScore * 0.6) + (scaleScore * 0.4);
    
    return totalScore >= WAVE_THRESHOLD;
  }
  
  /**
   * Calculate complexity score for wave activation
   */
  private static calculateComplexityScore(
    command: SuperClaudeCommand,
    context?: Partial<SuperClaudeCommandContext>
  ): number {
    let score = 0;
    
    // Command complexity
    if (command.performanceProfile === 'complex') score += 0.4;
    else if (command.performanceProfile === 'optimization') score += 0.3;
    else score += 0.1;
    
    // Multiple personas indicate complexity
    if (context?.personas && context.personas.length > 2) score += 0.2;
    
    // Multiple MCP servers indicate complexity
    if (context?.mcpServers && context.mcpServers.length > 2) score += 0.2;
    
    // Flags that increase complexity
    const complexFlags = ['--comprehensive', '--systematic', '--thorough', '--enterprise'];
    if (context?.flags?.some(f => complexFlags.includes(f))) score += 0.2;
    
    return Math.min(score, 1.0);
  }
  
  /**
   * Calculate scale score for wave activation
   */
  private static calculateScaleScore(context?: Partial<SuperClaudeCommandContext>): number {
    let score = 0;
    
    // Check for scale indicators in arguments
    if (context?.arguments) {
      const scaleKeywords = ['entire', 'complete', 'full', 'comprehensive', 'all', 'system-wide'];
      const hasScaleKeyword = scaleKeywords.some(k => 
        context.arguments?.toLowerCase().includes(k)
      );
      if (hasScaleKeyword) score += 0.5;
    }
    
    // Check for file/directory scope
    if (context?.arguments?.includes('@') && context.arguments.includes('/**')) {
      score += 0.3; // Recursive directory operation
    }
    
    // Check for multi-domain operations
    const domains = ['frontend', 'backend', 'database', 'infrastructure', 'security'];
    const mentionedDomains = domains.filter(d => 
      context?.arguments?.toLowerCase().includes(d)
    );
    if (mentionedDomains.length > 2) score += 0.2;
    
    return Math.min(score, 1.0);
  }
  
  /**
   * Select appropriate wave strategy
   */
  static selectWaveStrategy(
    command: SuperClaudeCommand,
    context?: Partial<SuperClaudeCommandContext>
  ): SuperClaudeWaveStrategy {
    // Check for explicit strategy flag
    if (context?.flags?.includes('--progressive-waves')) {
      return this.createProgressiveStrategy(command);
    }
    if (context?.flags?.includes('--systematic-waves')) {
      return this.createSystematicStrategy(command);
    }
    if (context?.flags?.includes('--adaptive-waves')) {
      return this.createAdaptiveStrategy(command);
    }
    if (context?.flags?.includes('--enterprise-waves')) {
      return this.createEnterpriseStrategy(command);
    }
    
    // Auto-select based on command and context
    if (command.category === 'analysis') {
      return this.createSystematicStrategy(command);
    }
    if (command.category === 'quality' && command.name === 'improve') {
      return this.createProgressiveStrategy(command);
    }
    if (command.performanceProfile === 'complex') {
      return this.createEnterpriseStrategy(command);
    }
    
    // Default to adaptive strategy
    return this.createAdaptiveStrategy(command);
  }
  
  /**
   * Create progressive wave strategy
   */
  private static createProgressiveStrategy(command: SuperClaudeCommand): SuperClaudeWaveStrategy {
    const waves: SuperClaudeWave[] = [
      {
        waveNumber: 1,
        phase: 'review',
        operations: ['analyze current state', 'identify improvement areas'],
        persona: 'analyzer',
        mcpServers: ['sequential'],
      },
      {
        waveNumber: 2,
        phase: 'planning',
        operations: ['prioritize improvements', 'create execution plan'],
        persona: 'architect',
        mcpServers: ['sequential', 'context7'],
      },
      {
        waveNumber: 3,
        phase: 'implementation',
        operations: ['apply improvements incrementally', 'validate each change'],
        persona: command.autoPersonas[0] as SuperClaudePersona,
        mcpServers: command.mcpServers,
      },
      {
        waveNumber: 4,
        phase: 'validation',
        operations: ['run tests', 'verify improvements'],
        persona: 'qa',
        mcpServers: ['playwright', 'sequential'],
      },
      {
        waveNumber: 5,
        phase: 'optimization',
        operations: ['fine-tune results', 'document changes'],
        persona: 'performance',
        mcpServers: ['sequential'],
      },
    ];
    
    return {
      name: 'progressive',
      description: 'Incremental enhancement with continuous validation',
      waves,
      validationGates: true,
    };
  }
  
  /**
   * Create systematic wave strategy
   */
  private static createSystematicStrategy(command: SuperClaudeCommand): SuperClaudeWaveStrategy {
    const waves: SuperClaudeWave[] = [
      {
        waveNumber: 1,
        phase: 'review',
        operations: ['comprehensive analysis', 'document current state'],
        persona: 'analyzer',
        mcpServers: ['sequential'],
      },
      {
        waveNumber: 2,
        phase: 'planning',
        operations: ['systematic planning', 'dependency mapping'],
        persona: 'architect',
        mcpServers: ['sequential', 'context7'],
      },
      {
        waveNumber: 3,
        phase: 'implementation',
        operations: ['methodical implementation', 'structured execution'],
        persona: command.autoPersonas[0] as SuperClaudePersona,
        mcpServers: command.mcpServers,
      },
      {
        waveNumber: 4,
        phase: 'validation',
        operations: ['systematic validation', 'compliance checking'],
        persona: 'qa',
        mcpServers: ['sequential'],
      },
    ];
    
    return {
      name: 'systematic',
      description: 'Methodical analysis and execution',
      waves,
      validationGates: true,
    };
  }
  
  /**
   * Create adaptive wave strategy
   */
  private static createAdaptiveStrategy(command: SuperClaudeCommand): SuperClaudeWaveStrategy {
    const waves: SuperClaudeWave[] = [
      {
        waveNumber: 1,
        phase: 'review',
        operations: ['quick assessment', 'identify critical paths'],
        persona: 'analyzer',
        mcpServers: ['sequential'],
      },
      {
        waveNumber: 2,
        phase: 'implementation',
        operations: ['adaptive execution', 'dynamic adjustments'],
        persona: command.autoPersonas[0] as SuperClaudePersona,
        mcpServers: command.mcpServers,
      },
      {
        waveNumber: 3,
        phase: 'validation',
        operations: ['validate results', 'adjust if needed'],
        persona: 'qa',
        mcpServers: ['sequential'],
      },
    ];
    
    return {
      name: 'adaptive',
      description: 'Dynamic configuration based on feedback',
      waves,
      validationGates: false,
    };
  }
  
  /**
   * Create enterprise wave strategy
   */
  private static createEnterpriseStrategy(command: SuperClaudeCommand): SuperClaudeWaveStrategy {
    const waves: SuperClaudeWave[] = [
      {
        waveNumber: 1,
        phase: 'review',
        operations: ['enterprise assessment', 'stakeholder analysis', 'risk evaluation'],
        persona: 'architect',
        mcpServers: ['sequential', 'context7'],
      },
      {
        waveNumber: 2,
        phase: 'planning',
        operations: ['strategic planning', 'resource allocation', 'timeline creation'],
        persona: 'architect',
        mcpServers: ['sequential'],
      },
      {
        waveNumber: 3,
        phase: 'implementation',
        operations: ['phased rollout', 'parallel execution', 'continuous monitoring'],
        persona: command.autoPersonas[0] as SuperClaudePersona,
        mcpServers: command.mcpServers,
      },
      {
        waveNumber: 4,
        phase: 'validation',
        operations: ['comprehensive testing', 'security audit', 'performance validation'],
        persona: 'qa',
        mcpServers: ['playwright', 'sequential'],
      },
      {
        waveNumber: 5,
        phase: 'optimization',
        operations: ['enterprise optimization', 'scalability testing', 'documentation'],
        persona: 'performance',
        mcpServers: ['sequential', 'context7'],
      },
    ];
    
    return {
      name: 'enterprise',
      description: 'Enterprise-grade execution with comprehensive validation',
      waves,
      validationGates: true,
    };
  }
  
  /**
   * Execute a wave strategy
   */
  async executeWaveStrategy(
    strategy: SuperClaudeWaveStrategy,
    context: SuperClaudeCommandContext,
    projectPath: string,
    options: WaveExecutionOptions = {}
  ): Promise<WaveExecutionResult[]> {
    if (this.isExecuting) {
      throw new Error('Wave execution already in progress');
    }
    
    this.currentStrategy = strategy;
    this.currentWave = 0;
    this.isExecuting = true;
    this.waveResults.clear();
    this.abortController = new AbortController();
    
    const store = useSuperClaudeStore.getState();
    store.startWaveExecution(strategy);
    
    const results: WaveExecutionResult[] = [];
    
    try {
      for (const wave of strategy.waves) {
        // Check for abort
        if (this.abortController.signal.aborted) {
          break;
        }
        
        this.currentWave = wave.waveNumber;
        store.updateWaveProgress(wave.waveNumber, wave.phase, 0);
        
        // Execute wave
        const result = await this.executeWave(
          wave,
          context,
          projectPath,
          options
        );
        
        results.push(result);
        this.waveResults.set(wave.waveNumber, result);
        
        // Validation gate
        if (strategy.validationGates && options.validateBetweenWaves !== false) {
          const validation = await this.validateWave(result, wave);
          result.validation = validation;
          
          if (!validation.canProceed) {
            console.warn(`Wave ${wave.waveNumber} validation failed:`, validation.issues);
            store.failWave(wave.waveNumber, validation.issues.join(', '));
            break;
          }
        }
        
        // Checkpoint after wave if requested
        if (options.checkpointAfterWave) {
          await this.createWaveCheckpoint(wave, result, projectPath);
        }
        
        store.completeWave(wave.waveNumber, result);
        store.updateWaveProgress(wave.waveNumber, wave.phase, 100);
      }
      
      return results;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      store.failWave(this.currentWave, errorMessage);
      throw error;
    } finally {
      this.isExecuting = false;
      store.stopWaveExecution();
    }
  }
  
  /**
   * Execute a single wave
   */
  private async executeWave(
    wave: SuperClaudeWave,
    context: SuperClaudeCommandContext,
    projectPath: string,
    options: WaveExecutionOptions
  ): Promise<WaveExecutionResult> {
    const startTime = Date.now();
    const store = useSuperClaudeStore.getState();
    
    try {
      // Set wave-specific persona
      if (wave.persona) {
        store.setPersonas([wave.persona]);
      }
      
      // Set wave-specific MCP servers
      if (wave.mcpServers) {
        store.setMCPServers(wave.mcpServers);
      }
      
      // Build wave prompt
      const wavePrompt = this.buildWavePrompt(wave, context);
      
      // Execute through Claude
      const result = await this.executeClaudeCommand(
        wavePrompt,
        projectPath,
        options.timeoutPerWave
      );
      
      const duration = Date.now() - startTime;
      
      return {
        waveNumber: wave.waveNumber,
        phase: wave.phase,
        success: result.success,
        output: result.output,
        artifacts: result.artifactsCreated,
        tokensUsed: result.tokensUsed,
        duration,
      };
    } catch (error) {
      return {
        waveNumber: wave.waveNumber,
        phase: wave.phase,
        success: false,
        output: error instanceof Error ? error.message : 'Wave execution failed',
        duration: Date.now() - startTime,
      };
    }
  }
  
  /**
   * Build prompt for wave execution
   */
  private buildWavePrompt(wave: SuperClaudeWave, context: SuperClaudeCommandContext): string {
    const parts: string[] = [];
    
    // Wave header
    parts.push(`[Wave ${wave.waveNumber}: ${wave.phase}]`);
    
    // Wave operations
    parts.push(`Operations: ${wave.operations.join(', ')}`);
    
    // Original command context
    parts.push(`Command: ${context.command} ${context.arguments || ''}`);
    
    // Wave-specific instructions
    if (wave.phase === 'review') {
      parts.push('Perform comprehensive analysis and document findings');
    } else if (wave.phase === 'planning') {
      parts.push('Create detailed execution plan with clear milestones');
    } else if (wave.phase === 'implementation') {
      parts.push('Execute planned changes with careful validation');
    } else if (wave.phase === 'validation') {
      parts.push('Thoroughly validate all changes and results');
    } else if (wave.phase === 'optimization') {
      parts.push('Optimize and fine-tune the implementation');
    }
    
    // Add flags
    if (context.flags) {
      parts.push(`Flags: ${context.flags.join(' ')}`);
    }
    
    return parts.join('\n');
  }
  
  /**
   * Execute Claude command
   */
  private async executeClaudeCommand(
    prompt: string,
    projectPath: string,
    _timeout?: number
  ): Promise<SuperClaudeCommandResult> {
    try {
      // This would integrate with the actual Claude execution
      // For now, return a mock result
      await api.executeClaudeCode(projectPath, prompt, 'opus');
      
      return {
        success: true,
        output: 'Wave executed successfully',
        tokensUsed: 1000,
        artifactsCreated: [],
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Execution failed',
      };
    }
  }
  
  /**
   * Validate wave results
   */
  private async validateWave(
    result: WaveExecutionResult,
    wave: SuperClaudeWave
  ): Promise<WaveValidationResult> {
    const issues: string[] = [];
    const recommendations: string[] = [];
    
    // Check success
    if (!result.success) {
      issues.push(`Wave ${wave.waveNumber} failed: ${result.output}`);
    }
    
    // Phase-specific validation
    if (wave.phase === 'implementation' && !result.artifacts?.length) {
      issues.push('No artifacts created during implementation phase');
      recommendations.push('Verify that changes were properly saved');
    }
    
    if (wave.phase === 'validation' && result.output?.includes('fail')) {
      issues.push('Validation phase detected failures');
      recommendations.push('Review and fix validation errors before proceeding');
    }
    
    // Token usage check
    if (result.tokensUsed && result.tokensUsed > 10000) {
      recommendations.push('High token usage detected, consider enabling compression');
    }
    
    return {
      passed: issues.length === 0,
      issues,
      recommendations,
      canProceed: issues.length === 0 || wave.phase !== 'validation',
    };
  }
  
  /**
   * Create checkpoint after wave
   */
  private async createWaveCheckpoint(
    wave: SuperClaudeWave,
    _result: WaveExecutionResult,
    _projectPath: string
  ): Promise<void> {
    try {
      // TODO: Need sessionId and projectId from context
      // await api.createCheckpoint(sessionId, projectId, projectPath, 
      //   `Wave ${wave.waveNumber} (${wave.phase}) completed`, 
      //   {
      //     waveNumber: wave.waveNumber,
      //     phase: wave.phase,
      //     success: result.success,
      //     tokensUsed: result.tokensUsed,
      //   });
      console.log(`Wave ${wave.waveNumber} checkpoint would be created here`);
    } catch (error) {
      console.error('Failed to create wave checkpoint:', error);
    }
  }
  
  /**
   * Abort wave execution
   */
  abortExecution(): void {
    if (this.abortController) {
      this.abortController.abort();
    }
    this.isExecuting = false;
    const store = useSuperClaudeStore.getState();
    store.stopWaveExecution();
  }
  
  /**
   * Get current wave status
   */
  getStatus(): {
    isExecuting: boolean;
    currentWave: number;
    currentStrategy?: SuperClaudeWaveStrategy;
    results: Map<number, WaveExecutionResult>;
  } {
    return {
      isExecuting: this.isExecuting,
      currentWave: this.currentWave,
      currentStrategy: this.currentStrategy,
      results: this.waveResults,
    };
  }
}

// Export singleton instance
export const waveOrchestrator = new WaveOrchestrator();
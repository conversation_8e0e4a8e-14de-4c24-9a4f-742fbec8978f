/**
 * SuperClaude Command Executor
 * Central command execution router with intelligent orchestration
 */

import type {
  SuperClaudeCommand,
  SuperClaudeCommandResult,
  SuperClaudeMode,
  SuperClaudePersona,
} from '@/types/superClaude';

import { slashCommandQueue } from './slashCommandQueue';
import { agentRegistry } from './agents';
import { modeActivationEngine } from './modes/activationEngine';
import { checkpointStrategy } from './checkpointStrategy';


export interface ExecutionOptions {
  priority?: 'critical' | 'high' | 'normal' | 'low';
  async?: boolean;
  waveEnabled?: boolean;
  checkpointEnabled?: boolean;
  personaOverride?: SuperClaudePersona[];
  modeOverride?: SuperClaudeMode;
  mcpServers?: string[];
  debugSessionId?: string;
}
export interface ExecutionContext {
  command: string;
  args: string[];
  flags: Record<string, any>;
  content?: string;
  path?: string;
  sessionId: string;
  projectId: string;
  userId?: string;
  timestamp: number;
}

export interface ExecutionPlan {
  phases: ExecutionPhase[];
  estimatedTime: number;
  resourceRequirements: ResourceRequirements;
  checkpoints: string[];
  rollbackStrategy?: RollbackStrategy;
}

export interface ExecutionPhase {
  name: string;
  operations: Operation[];
  personas: SuperClaudePersona[];
  mcpServers: string[];
  parallel: boolean;
  waveEnabled: boolean;
}

export interface Operation {
  id: string;
  type: 'analysis' | 'creation' | 'modification' | 'validation' | 'deployment';
  description: string;
  tools: string[];
  estimatedTime: number;
  dependencies: string[];
}

export interface ResourceRequirements {
  estimatedTokens: number;
  mcpServers: string[];
  personas: SuperClaudePersona[];
  memoryUsage: number;
  parallelOperations: number;
}

export interface RollbackStrategy {
  checkpointId?: string;
  restorePoints: string[];
  cleanupOperations: string[];
}

export class CommandExecutor {
  private executionHistory: Map<string, ExecutionContext>;
  private activeExecutions: Set<string>;
  private executionPlans: Map<string, ExecutionPlan>;
  
  constructor() {
    this.executionHistory = new Map();
    this.activeExecutions = new Set();
    this.executionPlans = new Map();
  }
  
  /**
   * Execute a SuperClaude command
   */
  async execute(
    command: SuperClaudeCommand,
    context: ExecutionContext,
    options: ExecutionOptions = {}
  ): Promise<SuperClaudeCommandResult> {
    const executionId = this.generateExecutionId();
    
    try {
      // Mark as active
      this.activeExecutions.add(executionId);
      this.executionHistory.set(executionId, context);
      
      // Create execution plan
      const plan = await this.createExecutionPlan(command, context, options);
      this.executionPlans.set(executionId, plan);
      
      // Checkpoint before execution if needed
      if (options.checkpointEnabled) {
        await this.createCheckpoint(context, 'pre-execution');
      }
      
      // Queue or execute based on priority
      if (options.async) {
        return this.queueExecution(command, context, options, plan);
      } else {
        return await this.executeImmediate(command, context, options, plan);
      }
    } finally {
      this.activeExecutions.delete(executionId);
    }
  }
  
  /**
   * Create an execution plan
   */
  private async createExecutionPlan(
    command: SuperClaudeCommand,
    context: ExecutionContext,
    _options: ExecutionOptions
  ): Promise<ExecutionPlan> {
    // Analyze command complexity
    const complexity = this.analyzeComplexity(command, context);
    
    // Determine phases based on command type
    const phases = this.determinePhases(command, context, complexity);
    
    // Calculate resource requirements
    const resourceRequirements = this.calculateResources(phases, complexity);
    
    // Identify checkpoint opportunities
    const checkpoints = this.identifyCheckpoints(phases);
    
    // Create rollback strategy if needed
    const rollbackStrategy = complexity > 0.7 ? 
      this.createRollbackStrategy(context) : undefined;
    
    return {
      phases,
      estimatedTime: this.estimateExecutionTime(phases),
      resourceRequirements,
      checkpoints,
      rollbackStrategy
    };
  }
  
  /**
   * Execute command immediately
   */
  private async executeImmediate(
    command: SuperClaudeCommand,
    context: ExecutionContext,
    options: ExecutionOptions,
    plan: ExecutionPlan
  ): Promise<SuperClaudeCommandResult> {
    // const startTime = Date.now();
    const results: any[] = [];
    const artifacts: string[] = [];
    
    try {
      // Activate appropriate mode
      const mode = options.modeOverride || this.selectMode(command, context);
      if (mode) {
        modeActivationEngine.transitionTo(mode, `Command: ${command.name}`);
      }
      
      // Activate personas
      const personas = options.personaOverride || 
                      (command.autoPersonas as SuperClaudePersona[]) || [];
      await this.activatePersonas(personas, context);
      
      // Special handling for orchestration mode
      if (mode === 'orchestration') {
        return await this.executeWithOrchestration(command, context, options);
      }
      
      // Special handling for analyze command
      if (command.name === 'analyze') {
        return await this.executeAnalyzeCommand(context, options);
      }
      
      // Special handling for troubleshoot command
      if (command.name === 'troubleshoot') {
        return await this.executeTroubleshootCommand(context, options);
      }
      
      // Special handling for requirements command
      if (command.name === 'requirements' || context.flags['requirements']) {
        return await this.executeRequirementsCommand(context, options);
      }
      
      // Special handling for explain command
      if (command.name === 'explain') {
        return await this.executeExplainCommand(context, options);
      }
      
      // Special handling for build command
      if (command.name === 'build') {
        return await this.executeBuildCommand(context, options);
      }
      
      // Special handling for test command
      if (command.name === 'test') {
        return await this.executeTestCommand(context, options);
      }
      
      // Execute phases
      for (const phase of plan.phases) {
        const phaseResult = await this.executePhase(phase, context, options);
        results.push(phaseResult);
        
        // Checkpoint after critical phases
        if (plan.checkpoints.includes(phase.name)) {
          await this.createCheckpoint(context, `phase-${phase.name}`);
        }
        
        // Collect artifacts
        if (phaseResult.artifacts) {
          artifacts.push(...phaseResult.artifacts);
        }
      }
      
      return {
        success: true,
        output: this.synthesizeResults(results),
        tokensUsed: this.calculateTokensUsed(results),
        wavesCompleted: options.waveEnabled ? plan.phases.length : undefined,
        artifactsCreated: artifacts
      };
    } catch (error) {
      // Rollback if needed
      if (plan.rollbackStrategy) {
        await this.rollback(plan.rollbackStrategy);
      }
      
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        tokensUsed: 0
      };
    } finally {
      // Cleanup - mode deactivation would happen here
    }
  }
  
  /**
   * Queue command for async execution
   */
  private async queueExecution(
    command: SuperClaudeCommand,
    context: ExecutionContext,
    options: ExecutionOptions,
    _plan: ExecutionPlan
  ): Promise<SuperClaudeCommandResult> {
    const priority = options.priority || 'normal';
    
    const queueId = slashCommandQueue.enqueue(
      command.name,
      context.args,
      context.flags,
      priority,
      command
    );
    
    return {
      success: true,
      output: `Command queued for execution (ID: ${queueId})`,
      tokensUsed: 0,
      artifactsCreated: []
    };
  }
  
  /**
   * Execute a single phase
   */
  private async executePhase(
    phase: ExecutionPhase,
    context: ExecutionContext,
    _options: ExecutionOptions
  ): Promise<any> {
    // Activate phase-specific personas
    if (phase.personas.length > 0) {
      await this.activatePersonas(phase.personas, context);
    }
    
    // Execute operations
    if (phase.parallel) {
      return await this.executeParallelOperations(phase.operations, context);
    } else {
      return await this.executeSequentialOperations(phase.operations, context);
    }
  }
  
  /**
   * Execute operations in parallel
   */
  private async executeParallelOperations(
    operations: Operation[],
    context: ExecutionContext
  ): Promise<any> {
    const promises = operations.map(op => this.executeOperation(op, context));
    return await Promise.all(promises);
  }
  
  /**
   * Execute operations sequentially
   */
  private async executeSequentialOperations(
    operations: Operation[],
    context: ExecutionContext
  ): Promise<any> {
    const results = [];
    for (const operation of operations) {
      const result = await this.executeOperation(operation, context);
      results.push(result);
    }
    return results;
  }
  
  /**
   * Execute a single operation
   */
  private async executeOperation(
    operation: Operation,
    _context: ExecutionContext
  ): Promise<any> {
    // Log operation
    console.log(`Executing operation: ${operation.description}`);
    
    // This is where actual tool execution would happen
    // For now, return a placeholder result
    return {
      operationId: operation.id,
      type: operation.type,
      success: true,
      result: `Completed: ${operation.description}`,
      artifacts: []
    };
  }

  /**
   * Execute command with orchestration mode
   */
  private async executeWithOrchestration(
    command: SuperClaudeCommand,
    context: ExecutionContext,
    options: ExecutionOptions
  ): Promise<SuperClaudeCommandResult> {
    try {
      // Get orchestration mode instance
      const orchestrationMode = modeActivationEngine.getMode('orchestration');
      if (!orchestrationMode) {
        throw new Error('Orchestration mode not available');
      }

      // Create mode context
      const modeContext = {
        resourceUsage: 50, // Placeholder - would come from system metrics
        complexity: this.analyzeComplexity(command, context),
        multiToolOperation: command.mcpServers.length > 1,
        fileCount: context.path ? 10 : 0, // Placeholder
        directoryCount: context.path ? 2 : 0, // Placeholder
        keywords: context.args
      };

      // Create orchestration plan
      const plan = (orchestrationMode as any).createOrchestrationPlan(command, modeContext);
      
      // Execute orchestration plan
      const executionContext = {
        ...context,
        startTime: Date.now(),
        command: command.name,
        options
      };
      
      const executionResult = await (orchestrationMode as any).executeOrchestrationPlan(plan, executionContext);
      
      // Optimize for next execution
      if (executionResult.success) {
        // We're not using the optimized plan in this version, but we could in the future
        (orchestrationMode as any).optimizeOrchestration(plan, executionResult);
        console.log('[Orchestration] Plan optimized for future executions');
      }
      
      return {
        success: executionResult.success,
        output: executionResult.success 
          ? `Orchestration completed successfully with ${executionResult.results.length} tool chains executed`
          : `Orchestration failed: ${executionResult.error}`,
        tokensUsed: executionResult.totalTokensUsed,
        artifactsCreated: []
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        tokensUsed: 0
      };
    }
  }
  
  /**
   * Analyze command complexity
   */
  private analyzeComplexity(
    command: SuperClaudeCommand,
    context: ExecutionContext
  ): number {
    let complexity = 0;
    
    // Base complexity from command type
    if (command.performanceProfile === 'complex') {
      complexity += 0.5;
    } else if (command.performanceProfile === 'optimization') {
      complexity += 0.3;
    } else {
      complexity += 0.1;
    }
    
    // Adjust for scope
    if (context.path?.includes('**')) {
      complexity += 0.2; // Recursive scope
    }
    
    // Adjust for flags
    if (context.flags['ultrathink'] || context.flags['comprehensive']) {
      complexity += 0.3;
    }
    
    // Adjust for wave mode
    if (command.waveEnabled) {
      complexity += 0.2;
    }
    
    return Math.min(complexity, 1.0);
  }
  
  /**
   * Determine execution phases
   */
  private determinePhases(
    command: SuperClaudeCommand,
    context: ExecutionContext,
    complexity: number
  ): ExecutionPhase[] {
    const phases: ExecutionPhase[] = [];
    
    // Analysis phase (if needed)
    if (command.category === 'analysis' || complexity > 0.5) {
      phases.push({
        name: 'analysis',
        operations: this.createAnalysisOperations(command, context),
        personas: ['analyzer', 'architect'] as SuperClaudePersona[],
        mcpServers: ['sequential', 'context7'],
        parallel: true,
        waveEnabled: command.waveEnabled
      });
    }
    
    // Planning phase (for complex operations)
    if (complexity > 0.7) {
      phases.push({
        name: 'planning',
        operations: this.createPlanningOperations(command, context),
        personas: ['architect'] as SuperClaudePersona[],
        mcpServers: ['sequential'],
        parallel: false,
        waveEnabled: false
      });
    }
    
    // Implementation phase
    if (command.category === 'development' || command.category === 'quality') {
      phases.push({
        name: 'implementation',
        operations: this.createImplementationOperations(command, context),
        personas: command.autoPersonas as SuperClaudePersona[],
        mcpServers: command.mcpServers,
        parallel: complexity < 0.5,
        waveEnabled: command.waveEnabled
      });
    }
    
    // Validation phase
    if (command.category === 'quality' || context.flags['validate']) {
      phases.push({
        name: 'validation',
        operations: this.createValidationOperations(command, context),
        personas: ['qa'] as SuperClaudePersona[],
        mcpServers: ['playwright', 'sequential'],
        parallel: true,
        waveEnabled: false
      });
    }
    
    // Optimization phase (if needed)
    if (command.name === 'optimize' || context.flags['optimize']) {
      phases.push({
        name: 'optimization',
        operations: this.createOptimizationOperations(command, context),
        personas: ['performance' as SuperClaudePersona],
        mcpServers: ['sequential'],
        parallel: false,
        waveEnabled: command.waveEnabled
      });
    }
    
    return phases;
  }
  
  /**
   * Create analysis operations
   */
  private createAnalysisOperations(
    _command: SuperClaudeCommand,
    _context: ExecutionContext
  ): Operation[] {
    return [
      {
        id: 'analyze-structure',
        type: 'analysis',
        description: 'Analyze code structure and architecture',
        tools: ['Read', 'Grep', 'Sequential'],
        estimatedTime: 5000,
        dependencies: []
      },
      {
        id: 'analyze-dependencies',
        type: 'analysis',
        description: 'Analyze dependencies and relationships',
        tools: ['Read', 'Sequential'],
        estimatedTime: 3000,
        dependencies: []
      }
    ];
  }
  
  /**
   * Create planning operations
   */
  private createPlanningOperations(
    _command: SuperClaudeCommand,
    _context: ExecutionContext
  ): Operation[] {
    return [
      {
        id: 'create-plan',
        type: 'analysis',
        description: 'Create execution plan',
        tools: ['Sequential', 'TodoWrite'],
        estimatedTime: 3000,
        dependencies: ['analyze-structure', 'analyze-dependencies']
      }
    ];
  }
  
  /**
   * Create implementation operations
   */
  private createImplementationOperations(
    command: SuperClaudeCommand,
    _context: ExecutionContext
  ): Operation[] {
    const operations: Operation[] = [];
    
    if (command.category === 'development') {
      operations.push({
        id: 'implement-feature',
        type: 'creation',
        description: 'Implement feature or component',
        tools: ['Write', 'Edit', 'MultiEdit'],
        estimatedTime: 10000,
        dependencies: ['create-plan']
      });
    }
    
    if (command.name === 'refactor') {
      operations.push({
        id: 'refactor-code',
        type: 'modification',
        description: 'Refactor code for quality',
        tools: ['Edit', 'MultiEdit', 'Sequential'],
        estimatedTime: 8000,
        dependencies: ['analyze-structure']
      });
    }
    
    return operations;
  }
  
  /**
   * Create validation operations
   */
  private createValidationOperations(
    _command: SuperClaudeCommand,
    _context: ExecutionContext
  ): Operation[] {
    return [
      {
        id: 'run-tests',
        type: 'validation',
        description: 'Run tests and validate',
        tools: ['Bash', 'Playwright'],
        estimatedTime: 5000,
        dependencies: ['implement-feature', 'refactor-code']
      },
      {
        id: 'check-quality',
        type: 'validation',
        description: 'Check code quality',
        tools: ['Bash', 'Sequential'],
        estimatedTime: 3000,
        dependencies: ['implement-feature', 'refactor-code']
      }
    ];
  }
  
  /**
   * Create optimization operations
   */
  private createOptimizationOperations(
    _command: SuperClaudeCommand,
    _context: ExecutionContext
  ): Operation[] {
    return [
      {
        id: 'optimize-performance',
        type: 'modification',
        description: 'Optimize for performance',
        tools: ['Edit', 'Sequential', 'Playwright'],
        estimatedTime: 8000,
        dependencies: ['run-tests']
      }
    ];
  }
  
  /**
   * Calculate resource requirements
   */
  private calculateResources(
    phases: ExecutionPhase[],
    complexity: number
  ): ResourceRequirements {
    const allPersonas = new Set<SuperClaudePersona>();
    const allServers = new Set<string>();
    let maxParallel = 0;
    
    for (const phase of phases) {
      phase.personas.forEach(p => allPersonas.add(p));
      phase.mcpServers.forEach(s => allServers.add(s));
      if (phase.parallel) {
        maxParallel = Math.max(maxParallel, phase.operations.length);
      }
    }
    
    return {
      estimatedTokens: Math.floor(10000 * complexity * phases.length),
      mcpServers: Array.from(allServers),
      personas: Array.from(allPersonas),
      memoryUsage: Math.floor(100 * complexity), // MB
      parallelOperations: maxParallel
    };
  }
  
  /**
   * Identify checkpoint opportunities
   */
  private identifyCheckpoints(phases: ExecutionPhase[]): string[] {
    const checkpoints: string[] = [];
    
    for (const phase of phases) {
      // Checkpoint after critical phases
      if (phase.name === 'implementation' || phase.name === 'optimization') {
        checkpoints.push(phase.name);
      }
    }
    
    return checkpoints;
  }
  
  /**
   * Create rollback strategy
   */
  private createRollbackStrategy(context: ExecutionContext): RollbackStrategy {
    return {
      checkpointId: `checkpoint-${context.timestamp}`,
      restorePoints: ['pre-execution', 'post-analysis'],
      cleanupOperations: ['remove-temp-files', 'restore-original']
    };
  }
  
  /**
   * Estimate execution time
   */
  private estimateExecutionTime(phases: ExecutionPhase[]): number {
    let totalTime = 0;
    
    for (const phase of phases) {
      if (phase.parallel) {
        // Parallel operations - take the longest
        const maxTime = Math.max(...phase.operations.map(op => op.estimatedTime));
        totalTime += maxTime;
      } else {
        // Sequential operations - sum all
        totalTime += phase.operations.reduce((sum, op) => sum + op.estimatedTime, 0);
      }
    }
    
    return totalTime;
  }
  
  /**
   * Select appropriate mode
   */
  private selectMode(
    command: SuperClaudeCommand,
    context: ExecutionContext
  ): SuperClaudeMode | null {
    // Check for explicit mode flags
    if (context.flags['brainstorm']) return 'brainstorming';
    if (context.flags['introspect']) return 'introspection';
    if (context.flags['task-manage']) return 'task-management';
    if (context.flags['orchestrate']) return 'orchestration';
    if (context.flags['uc'] || context.flags['ultracompressed']) return 'token-efficiency';
    
    // Auto-select based on command and context
    if (command.name === 'business-panel') return 'business-panel';
    if (command.waveEnabled) return 'orchestration';
    
    // Auto-select task management for complex multi-step operations
    if (context.args.some(arg => 
      ['task', 'workflow', 'project', 'implement', 'build', 'create'].some(term => 
        arg.toLowerCase().includes(term)))) {
      return 'task-management';
    }
    
    return null;
  }
  
  /**
   * Activate personas
   */
  private async activatePersonas(
    personas: SuperClaudePersona[],
    context: ExecutionContext
  ): Promise<void> {
    // Get agents from registry
    const agents = personas.map(personaId => {
      const agent = agentRegistry.get(personaId);
      if (agent) {
        return agent;
      }
      console.warn(`Agent '${personaId}' not found in registry`);
      return null;
    }).filter(agent => agent !== null);
    
    // Log activation
    console.log(`Activating ${agents.length} personas for context:`, context.command);
    
    // Agents are now ready for use in the execution context
    // They can analyze the context and provide recommendations
    for (const agent of agents) {
      const activationContext = {
        keywords: context.args,
        domain: this.determineDomain(context),
        complexity: this.analyzeComplexity({} as SuperClaudeCommand, context),
        operationType: this.determineOperationType(context.command)
      };
      
      const confidence = agent!.shouldActivate(activationContext);
      if (confidence > 0.5) {
        console.log(`Agent ${agent!.id} activated with confidence: ${confidence}`);
      }
    }
  }
  
  /**
   * Determine domain from context
   */
  private determineDomain(context: ExecutionContext): string {
    // Check for requirements-related keywords in args
    const requirementKeywords = ['requirement', 'specification', 'user story', 'acceptance', 'criteria'];
    const hasRequirements = context.args.some(arg => 
      requirementKeywords.some(keyword => arg.toLowerCase().includes(keyword))
    );
    
    if (hasRequirements) {
      return 'requirements';
    }
    
    if (context.path?.includes('frontend') || context.flags['ui']) {
      return 'frontend';
    }
    if (context.path?.includes('backend') || context.flags['api']) {
      return 'backend';
    }
    if (context.flags['security']) {
      return 'security';
    }
    if (context.path?.includes('test') || context.flags['test']) {
      return 'testing';
    }
    if (context.path?.includes('doc') || context.flags['document']) {
      return 'documentation';
    }
    return 'general';
  }
  
  /**
   * Determine operation type from command
   */
  private determineOperationType(command: string): 'analysis' | 'creation' | 'modification' | 'validation' | 'deployment' {
    if (command.includes('analyze') || command.includes('review')) {
      return 'analysis';
    }
    if (command.includes('create') || command.includes('build') || command.includes('implement')) {
      return 'creation';
    }
    if (command.includes('refactor') || command.includes('update') || command.includes('improve')) {
      return 'modification';
    }
    if (command.includes('test') || command.includes('validate')) {
      return 'validation';
    }
    if (command.includes('deploy') || command.includes('release')) {
      return 'deployment';
    }
    return 'analysis'; // Default to analysis
  }
  
  /**
   * Create checkpoint
   */
  private async createCheckpoint(
    context: ExecutionContext,
    description: string
  ): Promise<void> {
    checkpointStrategy.analyzeOperation({
      type: 'command_execution',
      path: context.path,
      impact: 'medium',
      reversible: true,
      timestamp: Date.now()
    });
    
    console.log(`Checkpoint created: ${description}`);
  }
  
  /**
   * Rollback to previous state
   */
  private async rollback(strategy: RollbackStrategy): Promise<void> {
    console.log(`Rolling back using strategy:`, strategy);
    // Implementation would restore from checkpoint
  }
  
  /**
   * Synthesize results from phases
   */
  private synthesizeResults(results: any[]): string {
    // Combine results into coherent output
    return results.map(r => 
      typeof r === 'string' ? r : JSON.stringify(r, null, 2)
    ).join('\n\n');
  }
  
  /**
   * Calculate total tokens used
   */
  private calculateTokensUsed(results: any[]): number {
    // Simplified calculation
    const text = JSON.stringify(results);
    return Math.floor(text.length / 4); // Rough approximation
  }
  
  /**
   * Generate unique execution ID
   */
  private generateExecutionId(): string {
    return `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  /**
   * Execute analyze command
   */
  private async executeAnalyzeCommand(
    context: ExecutionContext,
    _options: ExecutionOptions
  ): Promise<SuperClaudeCommandResult> {
    try {
      // Get technical-writer agent
      const agent = agentRegistry.get('technical-writer');
      if (!agent) {
        throw new Error('Technical Writer agent not available');
      }
      
      // Activate agent
      await this.activatePersonas(['technical-writer'] as unknown as SuperClaudePersona[], context);
      
      // Create analysis context
      const analysisContext = {
        keywords: context.args,
        domain: this.determineDomain(context),
        complexity: 0.7,
        operationType: 'analysis' as const
      };
      
      // Get agent analysis
      const analysis = agent.analyze(context.args.join(' '), analysisContext);
      
      // Format result
      const output = `
Analysis Results:
${analysis.analysis}

Recommendations:
${analysis.recommendations.map((r: string) => `- ${r}`).join('\n')}

Confidence: ${(analysis.confidence * 100).toFixed(1)}%
`;
      
      return {
        success: true,
        output,
        tokensUsed: 1500,
        artifactsCreated: []
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        tokensUsed: 0
      };
    }
  }
  
  /**
   * Execute troubleshoot command
   */
  private async executeTroubleshootCommand(
    context: ExecutionContext,
    _options: ExecutionOptions
  ): Promise<SuperClaudeCommandResult> {
    try {
      // Get analyzer agent
      const agent = agentRegistry.get('analyzer');
      if (!agent) {
        throw new Error('Analyzer agent not available');
      }
      
      // Activate agent
      await this.activatePersonas(['analyzer'] as unknown as SuperClaudePersona[], context);
      
      // Create troubleshooting context
      const troubleshootingContext = {
        keywords: context.args,
        domain: this.determineDomain(context),
        complexity: 0.8,
        operationType: 'analysis' as const,
        errors: context.flags['error'] || context.args.some(arg => arg.includes('error'))
      };
      
      // Get agent analysis
      const analysis = agent.analyze(context.args.join(' '), troubleshootingContext);
      
      // Format result
      const output = `
Troubleshooting Analysis:
${analysis.analysis}

Recommended Actions:
${analysis.recommendations.map((r: string) => `- ${r}`).join('\n')}

Confidence: ${(analysis.confidence * 100).toFixed(1)}%
`;
      
      return {
        success: true,
        output,
        tokensUsed: 1800,
        artifactsCreated: []
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        tokensUsed: 0
      };
    }
  }
  
  /**
   * Execute requirements command
   */
  private async executeRequirementsCommand(
    context: ExecutionContext,
    _options: ExecutionOptions
  ): Promise<SuperClaudeCommandResult> {
    try {
      // Get requirements agent
      const agent = agentRegistry.get('requirements');
      if (!agent) {
        throw new Error('Requirements agent not available');
      }
      
      // Activate agent
      await this.activatePersonas(['requirements'] as unknown as SuperClaudePersona[], context);
      
      // Create requirements context
      const requirementsContext = {
        keywords: context.args,
        domain: this.determineDomain(context),
        complexity: 0.7,
        operationType: 'analysis' as const
      };
      
      // Get agent analysis
      const analysis = agent.analyze(context.args.join(' '), requirementsContext);
      
      // Format result
      const output = `
Requirements Analysis:
${analysis.analysis}

Recommendations:
${analysis.recommendations.map((r: string) => `- ${r}`).join('\n')}

Confidence: ${(analysis.confidence * 100).toFixed(1)}%
`;
      
      return {
        success: true,
        output,
        tokensUsed: 1700,
        artifactsCreated: []
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        tokensUsed: 0
      };
    }
  }
  
  /**
   * Execute explain command
   */
  private async executeExplainCommand(
    context: ExecutionContext,
    _options: ExecutionOptions
  ): Promise<SuperClaudeCommandResult> {
    try {
      // Get technical-writer agent
      const agent = agentRegistry.get('technical-writer');
      if (!agent) {
        throw new Error('Technical Writer agent not available');
      }
      
      // Activate agent
      await this.activatePersonas(['technical-writer'] as unknown as SuperClaudePersona[], context);
      
      // Create explanation context
      const explanationContext = {
        keywords: context.args,
        domain: this.determineDomain(context),
        complexity: 0.6,
        operationType: 'analysis' as const
      };
      
      // Get agent analysis
      const analysis = agent.analyze(context.args.join(' '), explanationContext);
      
      // Format result
      const output = `
Explanation:
${analysis.analysis}

Key Concepts:
${analysis.recommendations.map((r: string) => `- ${r}`).join('\n')}

Confidence: ${(analysis.confidence * 100).toFixed(1)}%
`;
      
      return {
        success: true,
        output,
        tokensUsed: 1600,
        artifactsCreated: []
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        tokensUsed: 0
      };
    }
  }
  
  /**
   * Execute build command
   */
  private async executeBuildCommand(
    context: ExecutionContext,
    _options: ExecutionOptions
  ): Promise<SuperClaudeCommandResult> {
    try {
      // Get backend agent
      const agent = agentRegistry.get('backend');
      if (!agent) {
        throw new Error('Backend agent not available');
      }
      
      // Activate agent
      await this.activatePersonas(['backend'] as unknown as SuperClaudePersona[], context);
      
      // Create build context
      const buildContext = {
        keywords: context.args,
        domain: this.determineDomain(context),
        complexity: 0.7,
        operationType: 'creation' as const
      };
      
      // Get agent analysis
      const analysis = agent.analyze(context.args.join(' '), buildContext);
      
      // Format result
      const output = `
Build Plan:
${analysis.analysis}

Steps:
${analysis.recommendations.map((r: string) => `- ${r}`).join('\n')}

Confidence: ${(analysis.confidence * 100).toFixed(1)}%
`;
      
      return {
        success: true,
        output,
        tokensUsed: 2000,
        artifactsCreated: []
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        tokensUsed: 0
      };
    }
  }
  
  /**
   * Execute test command
   */
  private async executeTestCommand(
    context: ExecutionContext,
    _options: ExecutionOptions
  ): Promise<SuperClaudeCommandResult> {
    try {
      // Get QA agent
      const agent = agentRegistry.get('qa');
      if (!agent) {
        throw new Error('QA agent not available');
      }
      
      // Activate agent
      await this.activatePersonas(['qa'] as unknown as SuperClaudePersona[], context);
      
      // Create test context
      const testContext = {
        keywords: context.args,
        domain: this.determineDomain(context),
        complexity: 0.6,
        operationType: 'validation' as const
      };
      
      // Get agent analysis
      const analysis = agent.analyze(context.args.join(' '), testContext);
      
      // Format result
      const output = `
Test Plan:
${analysis.analysis}

Test Scenarios:
${analysis.recommendations.map((r: string) => `- ${r}`).join('\n')}

Confidence: ${(analysis.confidence * 100).toFixed(1)}%
`;
      
      return {
        success: true,
        output,
        tokensUsed: 1700,
        artifactsCreated: []
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        tokensUsed: 0
      };
    }
  }
  
  /**
   * Generate unique execution ID
   */
  
  /**
   * Get execution status
   */
  getExecutionStatus(executionId: string): {
    active: boolean;
    context?: ExecutionContext;
    plan?: ExecutionPlan;
  } {
    return {
      active: this.activeExecutions.has(executionId),
      context: this.executionHistory.get(executionId),
      plan: this.executionPlans.get(executionId)
    };
  }
  
  /**
   * Get all active executions
   */
  getActiveExecutions(): string[] {
    return Array.from(this.activeExecutions);
  }
  
  /**
   * Execute a command by name with arguments and flags
   */
  async executeCommand(
    commandName: string,
    args: string[] = [],
    flags: Record<string, any> = {},
    context: Omit<ExecutionContext, 'command' | 'args' | 'flags'>
  ): Promise<SuperClaudeCommandResult> {
    // Find the command in available commands
    const command = this.getAvailableCommands().find(cmd => cmd.name === commandName);
    
    if (!command) {
      return {
        success: false,
        error: `Command '${commandName}' not found`,
        tokensUsed: 0
      };
    }
    
    // Create full execution context
    const fullContext: ExecutionContext = {
      ...context,
      command: commandName,
      args,
      flags
    };
    
    // Execute the command
    return await this.execute(command, fullContext);
  }
  
  /**
   * Get all available SuperClaude commands
   */
  getAvailableCommands(): SuperClaudeCommand[] {
    // These commands are based on the enhancedCommands data in superClaudeUseCases.ts
    return [
      {
        name: 'analyze',
        description: 'Deep codebase analysis and understanding',
        category: 'analysis',
        waveEnabled: true,
        performanceProfile: 'complex',
        autoPersonas: ['analyzer', 'architect'],
        mcpServers: ['sequential', 'context7'],
        arguments: '[path] [focus]',
        flags: ['focus', 'depth', 'ultrathink', 'loop']
      },
      {
        name: 'build',
        description: 'Create new features or components',
        category: 'development',
        waveEnabled: true,
        performanceProfile: 'standard',
        autoPersonas: ['frontend', 'backend'],
        mcpServers: ['sequential'],
        arguments: '[feature] [type]',
        flags: ['type', 'framework', 'component']
      },
      {
        name: 'implement',
        description: 'Implement features from specifications',
        category: 'development',
        waveEnabled: true,
        performanceProfile: 'complex',
        autoPersonas: ['frontend', 'backend'],
        mcpServers: ['sequential'],
        arguments: '[feature] [requirements]',
        flags: ['type', 'framework']
      },
      {
        name: 'improve',
        description: 'Enhance existing code quality',
        category: 'quality',
        waveEnabled: true,
        performanceProfile: 'complex',
        autoPersonas: ['refactorer', 'performance'],
        mcpServers: ['sequential'],
        arguments: '[path] [focus]',
        flags: ['focus', 'loop', 'iterations']
      },
      {
        name: 'troubleshoot',
        description: 'Investigate and fix specific problems',
        category: 'analysis',
        waveEnabled: false,
        performanceProfile: 'standard',
        autoPersonas: ['analyzer'],
        mcpServers: ['sequential'],
        arguments: '[symptoms] [error]',
        flags: ['think', 'deep']
      },
      {
        name: 'document',
        description: 'Create comprehensive documentation',
        category: 'documentation',
        waveEnabled: false,
        performanceProfile: 'standard',
        autoPersonas: ['scribe'],
        mcpServers: ['sequential'],
        arguments: '[target] [type]',
        flags: ['type', 'lang', 'format']
      },
      {
        name: 'test',
        description: 'Create and run test suites',
        category: 'testing',
        waveEnabled: true,
        performanceProfile: 'standard',
        autoPersonas: ['qa'],
        mcpServers: ['sequential', 'playwright'],
        arguments: '[target] [type]',
        flags: ['type', 'framework', 'coverage']
      },
      {
        name: 'explain',
        description: 'Learn and understand code or concepts',
        category: 'analysis',
        waveEnabled: false,
        performanceProfile: 'standard',
        autoPersonas: ['mentor'],
        mcpServers: ['sequential'],
        arguments: '[topic] [concept]',
        flags: ['detailed', 'examples']
      },
      {
        name: 'design',
        description: 'Plan and design systems before implementation',
        category: 'planning',
        waveEnabled: true,
        performanceProfile: 'complex',
        autoPersonas: ['architect'],
        mcpServers: ['sequential'],
        arguments: '[feature] [requirements]',
        flags: ['architecture', 'patterns']
      },
      {
        name: 'load',
        description: 'Load and understand project context',
        category: 'analysis',
        waveEnabled: false,
        performanceProfile: 'optimization',
        autoPersonas: ['analyzer'],
        mcpServers: ['sequential'],
        arguments: '[path]',
        flags: ['deep', 'full']
      }
    ];
  }
  
  /**
   * Start an introspection debugging session
   */
  async startDebuggingSession(
    problem: string,
    context: ExecutionContext,
    options: ExecutionOptions = {}
  ): Promise<SuperClaudeCommandResult> {
    try {
      // Create a debug-focused execution plan
      const debugCommand: SuperClaudeCommand = {
        name: 'debug',
        description: 'Systematic debugging session',
        category: 'analysis',
        arguments: '[problem]',
        examples: [],
        autoPersonas: ['analyzer'],
        mcpServers: ['sequential'],
        performanceProfile: 'complex',
        waveEnabled: false
      };
      
      // Set debug mode override
      const debugOptions = {
        ...options,
        modeOverride: 'introspection' as SuperClaudeMode,
        personaOverride: ['analyzer'] as SuperClaudePersona[]
      };
      
      // Execute the debugging session
      return await this.execute(debugCommand, {
        ...context,
        command: `debug ${problem}`,
        args: [problem],
        flags: { ...context.flags, debug: true }
      }, debugOptions);
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        tokensUsed: 0
      };
    }
  }
}

// Export singleton instance
export const commandExecutor = new CommandExecutor();
/**
 * Business Panel Expert System Types
 * Multi-expert business analysis with adaptive interaction strategies
 */

/**
 * Business expert identifiers
 */
export type BusinessExpertId = 
  | 'christensen'
  | 'porter'
  | 'drucker'
  | 'godin'
  | 'kim_mauborgne'
  | 'collins'
  | 'taleb'
  | 'meadows'
  | 'doumont';

/**
 * Analysis mode types
 */
export type BusinessAnalysisMode = 
  | 'discussion'    // Collaborative analysis
  | 'debate'        // Adversarial analysis
  | 'socratic';     // Question-driven exploration

/**
 * Business expert configuration
 */
export interface BusinessExpert {
  id: BusinessExpertId;
  name: string;
  framework: string;
  focusAreas: string[];
  communicationStyle: string;
  symbol: string;
  
  // Methods
  analyze(content: string, mode: BusinessAnalysisMode): BusinessAnalysis;
  generateQuestions(content: string): string[];
  debate(otherExpert: BusinessExpert, topic: string): DebatePoint[];
}

/**
 * Business analysis result
 */
export interface BusinessAnalysis {
  expert: BusinessExpertId;
  framework: string;
  keyInsights: string[];
  recommendations: string[];
  risks?: string[];
  opportunities?: string[];
  questions?: string[];
  symbol: string;
}

/**
 * Debate point in adversarial analysis
 */
export interface DebatePoint {
  expert: BusinessExpertId;
  position: string;
  evidence: string[];
  counterTo?: BusinessExpertId;
  strength: 'strong' | 'moderate' | 'weak';
}

/**
 * Business panel configuration
 */
export interface BusinessPanelConfig {
  maxExperts: number;
  minExperts: number;
  autoSelect: boolean;
  diversityOptimization: boolean;
  mode: BusinessAnalysisMode;
  synthesisRequired: boolean;
  crossFrameworkValidation: boolean;
}

/**
 * Business panel session
 */
export interface BusinessPanelSession {
  id: string;
  experts: BusinessExpertId[];
  mode: BusinessAnalysisMode;
  content: string;
  analyses: BusinessAnalysis[];
  debates?: DebatePoint[];
  synthesis?: BusinessSynthesis;
  timestamp: Date;
}

/**
 * Cross-framework synthesis
 */
export interface BusinessSynthesis {
  convergentInsights: InsightCluster[];
  productiveTensions: TensionPoint[];
  systemPatterns: SystemPattern[];
  communicationClarity: CommunicationSummary;
  blindSpots: string[];
  strategicQuestions: string[];
}

/**
 * Insight cluster from multiple experts
 */
export interface InsightCluster {
  theme: string;
  experts: BusinessExpertId[];
  insights: string[];
  confidence: number;
}

/**
 * Productive tension between frameworks
 */
export interface TensionPoint {
  description: string;
  experts: BusinessExpertId[];
  positions: Record<BusinessExpertId, string>;
  resolution?: string;
  tradeoffs: string[];
}

/**
 * System pattern identified
 */
export interface SystemPattern {
  pattern: string;
  type: 'reinforcing' | 'balancing' | 'delay' | 'leverage';
  description: string;
  implications: string[];
}

/**
 * Communication summary
 */
export interface CommunicationSummary {
  coreMessage: string;
  actionPriorities: string[];
  audienceConsiderations: string[];
  clarityScore: number;
}

/**
 * Expert selection criteria
 */
export interface ExpertSelectionCriteria {
  domain?: string;
  analysisType?: string;
  requiredFrameworks?: string[];
  excludeExperts?: BusinessExpertId[];
  preferredExperts?: BusinessExpertId[];
}

/**
 * Business domain mapping
 */
export interface BusinessDomainMapping {
  innovation: BusinessExpertId[];
  strategy: BusinessExpertId[];
  marketing: BusinessExpertId[];
  risk: BusinessExpertId[];
  systems: BusinessExpertId[];
  communication: BusinessExpertId[];
  organization: BusinessExpertId[];
}

/**
 * Expert interaction pattern
 */
export interface ExpertInteraction {
  type: 'builds-on' | 'challenges' | 'synthesizes' | 'questions';
  fromExpert: BusinessExpertId;
  toExpert: BusinessExpertId;
  content: string;
  framework: string;
}

/**
 * Business panel analysis request
 */
export interface BusinessPanelRequest {
  content: string;
  mode?: BusinessAnalysisMode;
  experts?: BusinessExpertId[];
  autoSelectExperts?: boolean;
  focusAreas?: string[];
  synthesisOnly?: boolean;
  maxRounds?: number;
}

/**
 * Business panel analysis response
 */
export interface BusinessPanelResponse {
  session: BusinessPanelSession;
  primaryInsights: string[];
  actionableRecommendations: string[];
  criticalQuestions: string[];
  confidence: number;
  tokensUsed: number;
}

/**
 * Expert voice characteristics
 */
export interface ExpertVoice {
  expert: BusinessExpertId;
  tone: 'academic' | 'analytical' | 'conversational' | 'provocative' | 'wise' | 'precise';
  vocabulary: 'technical' | 'business' | 'accessible' | 'specialized';
  structure: 'systematic' | 'narrative' | 'questioning' | 'comparative';
  examples: string[];
}

/**
 * Business panel orchestrator interface
 */
export interface IBusinessPanelOrchestrator {
  // Configuration
  configure(config: Partial<BusinessPanelConfig>): void;
  getConfig(): BusinessPanelConfig;
  
  // Expert management
  selectExperts(criteria: ExpertSelectionCriteria): BusinessExpertId[];
  getExpert(id: BusinessExpertId): BusinessExpert;
  getAllExperts(): BusinessExpert[];
  
  // Analysis execution
  analyze(request: BusinessPanelRequest): Promise<BusinessPanelResponse>;
  runDiscussion(experts: BusinessExpert[], content: string): Promise<BusinessAnalysis[]>;
  runDebate(experts: BusinessExpert[], content: string): Promise<DebatePoint[]>;
  runSocratic(experts: BusinessExpert[], content: string): Promise<string[]>;
  
  // Synthesis
  synthesize(analyses: BusinessAnalysis[], debates?: DebatePoint[]): BusinessSynthesis;
  identifyConvergence(analyses: BusinessAnalysis[]): InsightCluster[];
  identifyTensions(analyses: BusinessAnalysis[]): TensionPoint[];
  
  // Utilities
  mapDomain(content: string): string[];
  assessComplexity(content: string): number;
  generateExecutiveSummary(response: BusinessPanelResponse): string;
}
/**
 * Business Panel Orchestrator
 * Manages multi-expert business analysis sessions with synthesis
 */

import {
  IBusinessPanelOrchestrator,
  BusinessPanelConfig,
  BusinessExpertId,
  BusinessExpert,
  ExpertSelectionCriteria,
  BusinessPanelRequest,
  BusinessPanelResponse,
  BusinessPanelSession,
  BusinessAnalysis,
  DebatePoint,
  BusinessSynthesis,
  InsightCluster,
  TensionPoint,
  SystemPattern,
  CommunicationSummary,
} from './types';

import { ExpertFactory } from './experts';

export class BusinessPanelOrchestrator implements IBusinessPanelOrchestrator {
  private config: BusinessPanelConfig;
  private currentSession: BusinessPanelSession | null = null;
  
  constructor(config?: Partial<BusinessPanelConfig>) {
    this.config = {
      maxExperts: config?.maxExperts || 5,
      minExperts: config?.minExperts || 3,
      autoSelect: config?.autoSelect !== false,
      diversityOptimization: config?.diversityOptimization !== false,
      mode: config?.mode || 'discussion',
      synthesisRequired: config?.synthesisRequired !== false,
      crossFrameworkValidation: config?.crossFrameworkValidation !== false,
    };
  }
  
  /**
   * Configure orchestrator
   */
  configure(config: Partial<BusinessPanelConfig>): void {
    this.config = { ...this.config, ...config };
  }
  
  /**
   * Get configuration
   */
  getConfig(): BusinessPanelConfig {
    return { ...this.config };
  }
  
  /**
   * Select experts based on criteria
   */
  selectExperts(criteria: ExpertSelectionCriteria): BusinessExpertId[] {
    const selected: BusinessExpertId[] = [];
    const allExperts = ExpertFactory.getAllExperts();
    
    // Add preferred experts first
    if (criteria.preferredExperts) {
      selected.push(...criteria.preferredExperts);
    }
    
    // Add domain-specific experts
    if (criteria.domain) {
      const domainExperts = ExpertFactory.getExpertsByDomain(criteria.domain);
      domainExperts.forEach(expert => {
        if (!selected.includes(expert.id) && 
            !criteria.excludeExperts?.includes(expert.id)) {
          selected.push(expert.id);
        }
      });
    }
    
    // Add framework-specific experts
    if (criteria.requiredFrameworks) {
      allExperts.forEach(expert => {
        if (criteria.requiredFrameworks!.some(framework => 
            expert.framework.toLowerCase().includes(framework.toLowerCase())) &&
            !selected.includes(expert.id) &&
            !criteria.excludeExperts?.includes(expert.id)) {
          selected.push(expert.id);
        }
      });
    }
    
    // Ensure diversity if enabled
    if (this.config.diversityOptimization && selected.length < this.config.minExperts) {
      const diverse = this.selectDiverseExperts(selected, criteria.excludeExperts);
      selected.push(...diverse);
    }
    
    // Ensure we have minimum experts
    while (selected.length < this.config.minExperts) {
      const remaining = allExperts.find(e => 
        !selected.includes(e.id) && 
        !criteria.excludeExperts?.includes(e.id)
      );
      if (remaining) {
        selected.push(remaining.id);
      } else {
        break;
      }
    }
    
    // Limit to maximum
    return selected.slice(0, this.config.maxExperts);
  }
  
  /**
   * Select diverse experts
   */
  private selectDiverseExperts(
    existing: BusinessExpertId[], 
    exclude?: BusinessExpertId[]
  ): BusinessExpertId[] {
    const diverse: BusinessExpertId[] = [];
    const needed = this.config.minExperts - existing.length;
    
    // Define diversity categories
    const categories = {
      innovation: ['christensen', 'drucker', 'godin'] as BusinessExpertId[],
      strategy: ['porter', 'kim_mauborgne', 'collins'] as BusinessExpertId[],
      systems: ['taleb', 'meadows', 'doumont'] as BusinessExpertId[],
    };
    
    // Try to add one from each category
    Object.values(categories).forEach(category => {
      if (diverse.length < needed) {
        const candidate = category.find(id => 
          !existing.includes(id) && 
          !diverse.includes(id) &&
          !exclude?.includes(id)
        );
        if (candidate) {
          diverse.push(candidate);
        }
      }
    });
    
    return diverse;
  }
  
  /**
   * Get expert by ID
   */
  getExpert(id: BusinessExpertId): BusinessExpert {
    return ExpertFactory.getExpert(id);
  }
  
  /**
   * Get all experts
   */
  getAllExperts(): BusinessExpert[] {
    return ExpertFactory.getAllExperts();
  }
  
  /**
   * Analyze content with business panel
   */
  async analyze(request: BusinessPanelRequest): Promise<BusinessPanelResponse> {
    // Select experts
    let expertIds: BusinessExpertId[];
    if (request.experts && request.experts.length > 0) {
      expertIds = request.experts;
    } else if (request.autoSelectExperts !== false) {
      const criteria: ExpertSelectionCriteria = {
        domain: this.mapDomain(request.content)[0],
      };
      expertIds = this.selectExperts(criteria);
    } else {
      expertIds = ['christensen', 'porter', 'drucker']; // Default experts
    }
    
    const experts = expertIds.map(id => this.getExpert(id));
    const mode = request.mode || this.config.mode;
    
    // Create session
    this.currentSession = {
      id: `session-${Date.now()}`,
      experts: expertIds,
      mode,
      content: request.content,
      analyses: [],
      timestamp: new Date(),
    };
    
    // Run analysis based on mode
    let analyses: BusinessAnalysis[] = [];
    let debates: DebatePoint[] | undefined;
    
    switch (mode) {
      case 'discussion':
        analyses = await this.runDiscussion(experts, request.content);
        break;
        
      case 'debate':
        analyses = await this.runDiscussion(experts, request.content);
        debates = await this.runDebate(experts, request.content);
        this.currentSession.debates = debates;
        break;
        
      case 'socratic':
        const questions = await this.runSocratic(experts, request.content);
        // Convert questions to analyses
        analyses = experts.map(expert => ({
          expert: expert.id,
          framework: expert.framework,
          keyInsights: [],
          recommendations: [],
          questions,
          symbol: expert.symbol,
        }));
        break;
    }
    
    this.currentSession.analyses = analyses;
    
    // Synthesize if required
    let synthesis: BusinessSynthesis | undefined;
    if (this.config.synthesisRequired && !request.synthesisOnly) {
      synthesis = this.synthesize(analyses, debates);
      this.currentSession.synthesis = synthesis;
    }
    
    // Generate response
    const response: BusinessPanelResponse = {
      session: this.currentSession,
      primaryInsights: this.extractPrimaryInsights(analyses, synthesis),
      actionableRecommendations: this.extractRecommendations(analyses, synthesis),
      criticalQuestions: this.extractQuestions(analyses),
      confidence: this.calculateConfidence(analyses, synthesis),
      tokensUsed: this.estimateTokens(analyses, debates, synthesis),
    };
    
    return response;
  }
  
  /**
   * Run discussion mode
   */
  async runDiscussion(experts: BusinessExpert[], content: string): Promise<BusinessAnalysis[]> {
    const analyses: BusinessAnalysis[] = [];
    
    // Each expert analyzes independently
    for (const expert of experts) {
      const analysis = expert.analyze(content, 'discussion');
      analyses.push(analysis);
    }
    
    // Experts build on each other's insights
    for (let i = 1; i < experts.length; i++) {
      const currentExpert = experts[i];
      const previousAnalysis = analyses[i - 1];
      
      // Add cross-reference insights
      const buildingInsight = `${currentExpert.symbol} Building on ${experts[i-1].name}: ${
        this.generateBuildingInsight(currentExpert, previousAnalysis)
      }`;
      analyses[i].keyInsights.push(buildingInsight);
    }
    
    return analyses;
  }
  
  /**
   * Run debate mode
   */
  async runDebate(experts: BusinessExpert[], content: string): Promise<DebatePoint[]> {
    const debates: DebatePoint[] = [];
    
    // Generate debate points between experts
    for (let i = 0; i < experts.length; i++) {
      for (let j = i + 1; j < experts.length; j++) {
        const expert1 = experts[i];
        const expert2 = experts[j];
        
        // Expert 1 challenges Expert 2
        const challenge: DebatePoint = {
          expert: expert1.id,
          position: this.generatePosition(expert1, content),
          evidence: this.generateEvidence(expert1, content),
          counterTo: expert2.id,
          strength: this.assessDebateStrength(expert1, expert2, content),
        };
        debates.push(challenge);
        
        // Expert 2 responds
        const response: DebatePoint = {
          expert: expert2.id,
          position: this.generateCounterPosition(expert2, expert1, content),
          evidence: this.generateEvidence(expert2, content),
          counterTo: expert1.id,
          strength: this.assessDebateStrength(expert2, expert1, content),
        };
        debates.push(response);
      }
    }
    
    return debates;
  }
  
  /**
   * Run Socratic mode
   */
  async runSocratic(experts: BusinessExpert[], content: string): Promise<string[]> {
    const questions: string[] = [];
    
    // Each expert generates questions
    for (const expert of experts) {
      const expertQuestions = expert.generateQuestions(content);
      questions.push(...expertQuestions);
    }
    
    // Remove duplicates and limit
    const uniqueQuestions = Array.from(new Set(questions));
    return uniqueQuestions.slice(0, 12); // Limit to 12 questions
  }
  
  /**
   * Synthesize analyses
   */
  synthesize(analyses: BusinessAnalysis[], debates?: DebatePoint[]): BusinessSynthesis {
    const synthesis: BusinessSynthesis = {
      convergentInsights: this.identifyConvergence(analyses),
      productiveTensions: this.identifyTensions(analyses),
      systemPatterns: this.identifySystemPatterns(analyses),
      communicationClarity: this.generateCommunicationSummary(analyses),
      blindSpots: this.identifyBlindSpots(analyses),
      strategicQuestions: this.generateStrategicQuestions(analyses),
    };
    
    // Enhance with debate insights if available
    if (debates && debates.length > 0) {
      this.enhanceSynthesisWithDebates(synthesis, debates);
    }
    
    return synthesis;
  }
  
  /**
   * Identify convergent insights
   */
  identifyConvergence(analyses: BusinessAnalysis[]): InsightCluster[] {
    const clusters: InsightCluster[] = [];
    const insightMap = new Map<string, BusinessExpertId[]>();
    
    // Group similar insights
    analyses.forEach(analysis => {
      analysis.keyInsights.forEach(insight => {
        const key = this.normalizeInsight(insight);
        if (!insightMap.has(key)) {
          insightMap.set(key, []);
        }
        insightMap.get(key)!.push(analysis.expert);
      });
    });
    
    // Create clusters where multiple experts agree
    insightMap.forEach((experts, insight) => {
      if (experts.length >= 2) {
        clusters.push({
          theme: insight,
          experts,
          insights: [insight],
          confidence: experts.length / analyses.length,
        });
      }
    });
    
    return clusters;
  }
  
  /**
   * Identify productive tensions
   */
  identifyTensions(analyses: BusinessAnalysis[]): TensionPoint[] {
    const tensions: TensionPoint[] = [];
    
    // Look for conflicting recommendations
    const topics = ['innovation', 'strategy', 'risk', 'growth'];
    
    topics.forEach(topic => {
      const positions: Partial<Record<BusinessExpertId, string>> = {};
      
      analyses.forEach(analysis => {
        const relevantRec = analysis.recommendations.find(rec => 
          rec.toLowerCase().includes(topic)
        );
        if (relevantRec) {
          positions[analysis.expert] = relevantRec;
        }
      });
      
      if (Object.keys(positions).length >= 2) {
        tensions.push({
          description: `Different approaches to ${topic}`,
          experts: Object.keys(positions) as BusinessExpertId[],
          positions: positions as Record<BusinessExpertId, string>,
          tradeoffs: [`Speed vs thoroughness`, `Risk vs reward`, `Innovation vs stability`],
        });
      }
    });
    
    return tensions;
  }
  
  /**
   * Identify system patterns
   */
  private identifySystemPatterns(analyses: BusinessAnalysis[]): SystemPattern[] {
    const patterns: SystemPattern[] = [];
    
    // Look for Meadows' system insights
    const meadowsAnalysis = analyses.find(a => a.expert === 'meadows');
    if (meadowsAnalysis) {
      patterns.push({
        pattern: 'Feedback Loop Dynamics',
        type: 'reinforcing',
        description: 'System reinforcing loops identified in business model',
        implications: ['Growth acceleration possible', 'Risk of runaway effects'],
      });
    }
    
    // Generic patterns
    patterns.push({
      pattern: 'Innovation-Stability Balance',
      type: 'balancing',
      description: 'Tension between innovation and operational stability',
      implications: ['Need for ambidextrous organization', 'Portfolio approach recommended'],
    });
    
    return patterns;
  }
  
  /**
   * Generate communication summary
   */
  private generateCommunicationSummary(_analyses: BusinessAnalysis[]): CommunicationSummary {
    return {
      coreMessage: 'Strategic transformation through multi-framework analysis',
      actionPriorities: [
        'Identify core job-to-be-done',
        'Build competitive moat',
        'Create systematic innovation',
      ],
      audienceConsiderations: [
        'Executive decision-makers need clear ROI',
        'Implementation teams need specific steps',
        'Stakeholders need risk mitigation plans',
      ],
      clarityScore: 0.85,
    };
  }
  
  /**
   * Identify blind spots
   */
  private identifyBlindSpots(analyses: BusinessAnalysis[]): string[] {
    const coveredAreas = new Set<string>();
    const allAreas = ['technology', 'regulation', 'culture', 'sustainability', 'ethics'];
    
    analyses.forEach(analysis => {
      analysis.keyInsights.forEach(insight => {
        allAreas.forEach(area => {
          if (insight.toLowerCase().includes(area)) {
            coveredAreas.add(area);
          }
        });
      });
    });
    
    return allAreas.filter(area => !coveredAreas.has(area))
      .map(area => `Limited analysis of ${area} implications`);
  }
  
  /**
   * Generate strategic questions
   */
  private generateStrategicQuestions(analyses: BusinessAnalysis[]): string[] {
    const questions: string[] = [];
    
    analyses.forEach(analysis => {
      if (analysis.questions && analysis.questions.length > 0) {
        questions.push(analysis.questions[0]); // Take top question from each expert
      }
    });
    
    return questions.slice(0, 5); // Top 5 strategic questions
  }
  
  /**
   * Enhance synthesis with debate insights
   */
  private enhanceSynthesisWithDebates(synthesis: BusinessSynthesis, debates: DebatePoint[]): void {
    // Add debate-based tensions
    const strongDebates = debates.filter(d => d.strength === 'strong');
    
    strongDebates.forEach(debate => {
      const tension: TensionPoint = {
        description: `Fundamental disagreement on approach`,
        experts: [debate.expert, debate.counterTo!],
        positions: {
          [debate.expert]: debate.position,
          [debate.counterTo!]: 'Counter-position',
        } as Record<BusinessExpertId, string>,
        tradeoffs: ['Different framework priorities', 'Time horizon differences'],
      };
      
      synthesis.productiveTensions.push(tension);
    });
  }
  
  /**
   * Helper methods
   */
  
  private generateBuildingInsight(expert: BusinessExpert, _previousAnalysis: BusinessAnalysis): string {
    return `Extending ${_previousAnalysis.framework} with ${expert.framework} perspective`;
  }
  
  private generatePosition(expert: BusinessExpert, _content: string): string {
    return `From ${expert.framework} perspective: Focus on ${expert.focusAreas[0]}`;
  }
  
  private generateCounterPosition(expert: BusinessExpert, otherExpert: BusinessExpert, _content: string): string {
    return `While ${otherExpert.name} emphasizes ${otherExpert.focusAreas[0]}, ${expert.framework} suggests ${expert.focusAreas[0]} is more critical`;
  }
  
  private generateEvidence(expert: BusinessExpert, _content: string): string[] {
    return [
      `${expert.framework} research shows...`,
      `Historical examples demonstrate...`,
      `Current market conditions indicate...`,
    ];
  }
  
  private assessDebateStrength(expert1: BusinessExpert, expert2: BusinessExpert, _content: string): 'strong' | 'moderate' | 'weak' {
    // Simulate debate strength assessment
    const overlappingAreas = expert1.focusAreas.filter(area => 
      expert2.focusAreas.includes(area)
    );
    
    if (overlappingAreas.length === 0) return 'strong'; // Very different perspectives
    if (overlappingAreas.length === 1) return 'moderate';
    return 'weak'; // Similar perspectives
  }
  
  private normalizeInsight(insight: string): string {
    return insight.toLowerCase().replace(/[^\w\s]/g, '').trim();
  }
  
  private extractPrimaryInsights(analyses: BusinessAnalysis[], synthesis?: BusinessSynthesis): string[] {
    const insights: string[] = [];
    
    // Add convergent insights first
    if (synthesis) {
      synthesis.convergentInsights
        .sort((a, b) => b.confidence - a.confidence)
        .slice(0, 3)
        .forEach(cluster => {
          insights.push(`✅ ${cluster.theme} (${cluster.experts.length} experts agree)`);
        });
    }
    
    // Add unique expert insights
    analyses.slice(0, 2).forEach(analysis => {
      if (analysis.keyInsights.length > 0) {
        insights.push(`${analysis.symbol} ${analysis.keyInsights[0]}`);
      }
    });
    
    return insights;
  }
  
  private extractRecommendations(analyses: BusinessAnalysis[], _synthesis?: BusinessSynthesis): string[] {
    const recommendations: string[] = [];
    const seen = new Set<string>();
    
    analyses.forEach(analysis => {
      analysis.recommendations.forEach(rec => {
        const normalized = this.normalizeInsight(rec);
        if (!seen.has(normalized)) {
          seen.add(normalized);
          recommendations.push(rec);
        }
      });
    });
    
    return recommendations.slice(0, 5);
  }
  
  private extractQuestions(analyses: BusinessAnalysis[]): string[] {
    const questions: string[] = [];
    
    analyses.forEach(analysis => {
      if (analysis.questions) {
        questions.push(...analysis.questions);
      }
    });
    
    return Array.from(new Set(questions)).slice(0, 5);
  }
  
  private calculateConfidence(analyses: BusinessAnalysis[], synthesis?: BusinessSynthesis): number {
    let confidence = 0.7; // Base confidence
    
    // Increase for convergence
    if (synthesis && synthesis.convergentInsights.length > 2) {
      confidence += 0.1;
    }
    
    // Increase for comprehensive analysis
    if (analyses.length >= 4) {
      confidence += 0.1;
    }
    
    // Decrease for blind spots
    if (synthesis && synthesis.blindSpots.length > 2) {
      confidence -= 0.05;
    }
    
    return Math.min(Math.max(confidence, 0), 1);
  }
  
  private estimateTokens(analyses: BusinessAnalysis[], debates?: DebatePoint[], synthesis?: BusinessSynthesis): number {
    let tokens = 0;
    
    // Estimate tokens for analyses
    analyses.forEach(analysis => {
      tokens += 500; // Base per expert
      tokens += analysis.keyInsights.length * 50;
      tokens += analysis.recommendations.length * 30;
    });
    
    // Add debate tokens
    if (debates) {
      tokens += debates.length * 100;
    }
    
    // Add synthesis tokens
    if (synthesis) {
      tokens += 1000;
    }
    
    return tokens;
  }
  
  /**
   * Map content to business domains
   */
  mapDomain(content: string): string[] {
    const domains: string[] = [];
    const lower = content.toLowerCase();
    
    const domainKeywords = {
      innovation: ['innovation', 'disrupt', 'new', 'create'],
      strategy: ['strategy', 'competitive', 'position', 'market'],
      marketing: ['marketing', 'customer', 'brand', 'audience'],
      risk: ['risk', 'uncertainty', 'threat', 'vulnerability'],
      systems: ['system', 'process', 'workflow', 'integration'],
      communication: ['communication', 'message', 'presentation', 'clarity'],
      organization: ['organization', 'culture', 'team', 'leadership'],
    };
    
    Object.entries(domainKeywords).forEach(([domain, keywords]) => {
      if (keywords.some(keyword => lower.includes(keyword))) {
        domains.push(domain);
      }
    });
    
    return domains.length > 0 ? domains : ['strategy']; // Default to strategy
  }
  
  /**
   * Assess content complexity
   */
  assessComplexity(content: string): number {
    let complexity = 0.5; // Base complexity
    
    // Increase for length
    if (content.length > 1000) complexity += 0.1;
    if (content.length > 5000) complexity += 0.2;
    
    // Increase for multiple topics
    const domains = this.mapDomain(content);
    complexity += domains.length * 0.1;
    
    // Increase for specific keywords
    const complexKeywords = ['transform', 'integrate', 'optimize', 'restructure'];
    complexKeywords.forEach(keyword => {
      if (content.toLowerCase().includes(keyword)) {
        complexity += 0.05;
      }
    });
    
    return Math.min(complexity, 1);
  }
  
  /**
   * Generate executive summary
   */
  generateExecutiveSummary(response: BusinessPanelResponse): string {
    const summary = [];
    
    summary.push('## 🎯 Executive Summary\n');
    
    // Key insights
    summary.push('### Key Insights');
    response.primaryInsights.slice(0, 3).forEach(insight => {
      summary.push(`- ${insight}`);
    });
    
    // Recommendations
    summary.push('\n### Recommended Actions');
    response.actionableRecommendations.slice(0, 3).forEach(rec => {
      summary.push(`- ${rec}`);
    });
    
    // Critical questions
    summary.push('\n### Strategic Questions');
    response.criticalQuestions.slice(0, 3).forEach(question => {
      summary.push(`- ${question}`);
    });
    
    // Confidence
    summary.push(`\n**Analysis Confidence**: ${(response.confidence * 100).toFixed(0)}%`);
    
    return summary.join('\n');
  }
}

// Export singleton instance
export const businessPanelOrchestrator = new BusinessPanelOrchestrator();
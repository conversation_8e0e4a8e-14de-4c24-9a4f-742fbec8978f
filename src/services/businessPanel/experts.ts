/**
 * Business Panel Expert Implementations
 * Individual expert personas with their unique frameworks and perspectives
 */

import {
  BusinessExpert,
  BusinessExpertId,
  BusinessAnalysis,
  BusinessAnalysisMode,
  DebatePoint,
} from './types';

/**
 * Base Business Expert class
 */
abstract class BaseBusinessExpert implements BusinessExpert {
  abstract id: BusinessExpertId;
  abstract name: string;
  abstract framework: string;
  abstract focusAreas: string[];
  abstract communicationStyle: string;
  abstract symbol: string;
  
  analyze(content: string, mode: BusinessAnalysisMode): BusinessAnalysis {
    const analysis: BusinessAnalysis = {
      expert: this.id,
      framework: this.framework,
      keyInsights: this.extractInsights(content),
      recommendations: this.generateRecommendations(content),
      risks: this.identifyRisks(content),
      opportunities: this.identifyOpportunities(content),
      questions: mode === 'socratic' ? this.generateQuestions(content) : undefined,
      symbol: this.symbol,
    };
    
    return analysis;
  }
  
  generateQuestions(_content: string): string[] {
    // Override in specific experts
    return [];
  }
  
  debate(_otherExpert: BusinessExpert, _topic: string): DebatePoint[] {
    // Override in specific experts
    return [];
  }
  
  protected abstract extractInsights(content: string): string[];
  protected abstract generateRecommendations(content: string): string[];
  protected abstract identifyRisks(content: string): string[];
  protected abstract identifyOpportunities(content: string): string[];
}

/**
 * Clayton Christensen - Disruption Theory Expert
 */
export class ChristensenExpert extends BaseBusinessExpert {
  id: BusinessExpertId = 'christensen';
  name = 'Clayton Christensen';
  framework = 'Disruption Theory & Jobs-to-be-Done';
  focusAreas = ['innovation', 'disruption', 'customer needs', 'value creation'];
  communicationStyle = 'academic, methodical';
  symbol = '🔨';
  
  protected extractInsights(content: string): string[] {
    const insights = [];
    const lower = content.toLowerCase();
    
    if (lower.includes('innovation') || lower.includes('new')) {
      insights.push('📚 Potential for disruptive innovation through underserved market segments');
    }
    if (lower.includes('customer') || lower.includes('user')) {
      insights.push('🔨 Focus on the job-to-be-done rather than product features');
    }
    if (lower.includes('market') || lower.includes('competition')) {
      insights.push('📊 Incumbents vulnerable to disruption from below');
    }
    
    return insights;
  }
  
  protected generateRecommendations(_content: string): string[] {
    return [
      'Identify the core job customers are hiring your product to do',
      'Look for overserved customers creating disruption opportunities',
      'Consider modular architecture for rapid innovation cycles',
      'Focus on good-enough solutions for new market segments',
    ];
  }
  
  protected identifyRisks(_content: string): string[] {
    return [
      'Risk of sustaining innovation trap - improving what exists vs creating new',
      'Potential disruption from unexpected competitors',
      'Over-focusing on current profitable customers',
    ];
  }
  
  protected identifyOpportunities(_content: string): string[] {
    return [
      'Create new market disruption through simplification',
      'Target non-consumption with accessible solutions',
      'Build platform for third-party innovation',
    ];
  }
  
  generateQuestions(_content: string): string[] {
    return [
      '🔨 What job is this really being hired to do?',
      '📚 Who are the non-consumers we could serve?',
      '🎯 What would a simpler, more accessible solution look like?',
      '💡 Where is good-enough better than perfect?',
    ];
  }
}

/**
 * Michael Porter - Competitive Strategy Expert
 */
export class PorterExpert extends BaseBusinessExpert {
  id: BusinessExpertId = 'porter';
  name = 'Michael Porter';
  framework = 'Five Forces & Competitive Advantage';
  focusAreas = ['strategy', 'competition', 'value chain', 'industry analysis'];
  communicationStyle = 'analytical, data-driven';
  symbol = '⚔️';
  
  protected extractInsights(content: string): string[] {
    const insights = [];
    const lower = content.toLowerCase();
    
    if (lower.includes('competitive') || lower.includes('advantage')) {
      insights.push('⚔️ Competitive advantage must be sustainable and defensible');
    }
    if (lower.includes('industry') || lower.includes('market')) {
      insights.push('📊 Industry structure determines profitability potential');
    }
    if (lower.includes('value') || lower.includes('chain')) {
      insights.push('🔗 Value chain optimization creates cost or differentiation advantage');
    }
    
    return insights;
  }
  
  protected generateRecommendations(_content: string): string[] {
    return [
      'Analyze five forces to understand industry attractiveness',
      'Choose between cost leadership, differentiation, or focus strategy',
      'Build barriers to entry through strategic positioning',
      'Optimize value chain for competitive advantage',
    ];
  }
  
  protected identifyRisks(_content: string): string[] {
    return [
      'Threat of new entrants eroding market position',
      'Bargaining power shifts favoring suppliers or buyers',
      'Substitute products undermining value proposition',
    ];
  }
  
  protected identifyOpportunities(_content: string): string[] {
    return [
      'Create switching costs to lock in customers',
      'Vertical integration to control value chain',
      'Geographic expansion to new markets',
    ];
  }
  
  generateQuestions(_content: string): string[] {
    return [
      '⚔️ What creates sustainable competitive advantage here?',
      '📊 How attractive is this industry structurally?',
      '🔗 Where in the value chain can we create unique value?',
      '🛡️ What barriers to entry can we establish?',
    ];
  }
}

/**
 * Peter Drucker - Management Expert
 */
export class DruckerExpert extends BaseBusinessExpert {
  id: BusinessExpertId = 'drucker';
  name = 'Peter Drucker';
  framework = 'Management by Objectives & Innovation';
  focusAreas = ['management', 'effectiveness', 'innovation', 'knowledge work'];
  communicationStyle = 'wise, fundamental';
  symbol = '🧭';
  
  protected extractInsights(content: string): string[] {
    const insights = [];
    const lower = content.toLowerCase();
    
    if (lower.includes('management') || lower.includes('leadership')) {
      insights.push('🧭 Management is doing things right; leadership is doing the right things');
    }
    if (lower.includes('innovation') || lower.includes('change')) {
      insights.push('💡 Innovation is the specific function of entrepreneurship');
    }
    if (lower.includes('customer') || lower.includes('market')) {
      insights.push('🎯 The purpose of business is to create and keep a customer');
    }
    
    return insights;
  }
  
  protected generateRecommendations(_content: string): string[] {
    return [
      'Define clear objectives and key results',
      'Focus on contribution rather than effort',
      'Systematize innovation as a discipline',
      'Develop knowledge workers effectiveness',
    ];
  }
  
  protected identifyRisks(_content: string): string[] {
    return [
      'Confusing efficiency with effectiveness',
      'Neglecting systematic innovation',
      'Underestimating knowledge worker productivity',
    ];
  }
  
  protected identifyOpportunities(_content: string): string[] {
    return [
      'Create systematic innovation process',
      'Leverage knowledge worker productivity',
      'Build learning organization culture',
    ];
  }
  
  generateQuestions(_content: string): string[] {
    return [
      '🧭 What is our business and what should it be?',
      '🎯 Who is our customer and what do they value?',
      '💡 Where are the unexpected successes we can build on?',
      '📈 How do we measure what matters?',
    ];
  }
}

/**
 * Seth Godin - Marketing Expert
 */
export class GodinExpert extends BaseBusinessExpert {
  id: BusinessExpertId = 'godin';
  name = 'Seth Godin';
  framework = 'Purple Cow & Tribes';
  focusAreas = ['marketing', 'remarkability', 'tribes', 'permission'];
  communicationStyle = 'conversational, provocative';
  symbol = '🎪';
  
  protected extractInsights(content: string): string[] {
    const insights = [];
    const lower = content.toLowerCase();
    
    if (lower.includes('marketing') || lower.includes('attention')) {
      insights.push('🎪 Be remarkable or be invisible');
    }
    if (lower.includes('customer') || lower.includes('audience')) {
      insights.push('👥 Build a tribe, not just a customer base');
    }
    if (lower.includes('product') || lower.includes('service')) {
      insights.push('🐄 Purple Cow: Transform your business by being remarkable');
    }
    
    return insights;
  }
  
  protected generateRecommendations(_content: string): string[] {
    return [
      'Create something worth talking about',
      'Find your tribe and lead them',
      'Ship often and fail fast',
      'Build permission assets before selling',
    ];
  }
  
  protected identifyRisks(_content: string): string[] {
    return [
      'Being boring is the greatest risk',
      'Interruption marketing losing effectiveness',
      'Failing to connect with your tribe',
    ];
  }
  
  protected identifyOpportunities(_content: string): string[] {
    return [
      'Create remarkable products people talk about',
      'Build and lead a passionate tribe',
      'Use permission marketing for engagement',
    ];
  }
  
  generateQuestions(_content: string): string[] {
    return [
      '🎪 What makes this remarkable enough to talk about?',
      '👥 Who is the tribe and what do they believe?',
      '🐄 Where is the Purple Cow in this?',
      '📢 How do we earn attention rather than steal it?',
    ];
  }
}

/**
 * Kim & Mauborgne - Blue Ocean Strategy Experts
 */
export class KimMauborgneExpert extends BaseBusinessExpert {
  id: BusinessExpertId = 'kim_mauborgne';
  name = 'W. Chan Kim & Renée Mauborgne';
  framework = 'Blue Ocean Strategy';
  focusAreas = ['strategy', 'value innovation', 'market creation', 'competition'];
  communicationStyle = 'strategic, value-focused';
  symbol = '🌊';
  
  protected extractInsights(content: string): string[] {
    const insights = [];
    const lower = content.toLowerCase();
    
    if (lower.includes('competition') || lower.includes('market')) {
      insights.push('🌊 Create uncontested market space rather than compete');
    }
    if (lower.includes('value') || lower.includes('innovation')) {
      insights.push('💎 Value innovation: simultaneous pursuit of differentiation and low cost');
    }
    if (lower.includes('customer') || lower.includes('demand')) {
      insights.push('🎯 Focus on non-customers to expand market boundaries');
    }
    
    return insights;
  }
  
  protected generateRecommendations(_content: string): string[] {
    return [
      'Apply Four Actions Framework: Eliminate-Reduce-Raise-Create',
      'Reconstruct market boundaries through six paths',
      'Focus on the big picture, not the numbers',
      'Reach beyond existing demand to non-customers',
    ];
  }
  
  protected identifyRisks(_content: string): string[] {
    return [
      'Trapped in red ocean of bloody competition',
      'Incremental improvements vs value innovation',
      'Focusing only on existing customers',
    ];
  }
  
  protected identifyOpportunities(_content: string): string[] {
    return [
      'Create blue ocean of uncontested market space',
      'Make competition irrelevant through value innovation',
      'Expand market by converting non-customers',
    ];
  }
  
  generateQuestions(_content: string): string[] {
    return [
      '🌊 What factors can we eliminate that the industry takes for granted?',
      '💎 How can we break the value-cost trade-off?',
      '🎯 Who are the non-customers we could convert?',
      '📊 What would the strategy canvas look like?',
    ];
  }
}

/**
 * Jim Collins - Organizational Excellence Expert
 */
export class CollinsExpert extends BaseBusinessExpert {
  id: BusinessExpertId = 'collins';
  name = 'Jim Collins';
  framework = 'Good to Great & Built to Last';
  focusAreas = ['leadership', 'excellence', 'culture', 'sustainability'];
  communicationStyle = 'research-driven, disciplined';
  symbol = '🚀';
  
  protected extractInsights(content: string): string[] {
    const insights = [];
    const lower = content.toLowerCase();
    
    if (lower.includes('leadership') || lower.includes('team')) {
      insights.push('🚀 Level 5 Leadership: Personal humility + professional will');
    }
    if (lower.includes('strategy') || lower.includes('focus')) {
      insights.push('🎯 Hedgehog Concept: Intersection of passion, best at, economic engine');
    }
    if (lower.includes('culture') || lower.includes('people')) {
      insights.push('🚌 First who, then what: Get the right people on the bus');
    }
    
    return insights;
  }
  
  protected generateRecommendations(_content: string): string[] {
    return [
      'Confront the brutal facts while maintaining faith',
      'Build Level 5 leadership throughout organization',
      'Create culture of discipline with entrepreneurial ethic',
      'Build momentum through flywheel effect',
    ];
  }
  
  protected identifyRisks(_content: string): string[] {
    return [
      'Doom loop from inconsistent direction',
      'Wrong people in key positions',
      'Lack of disciplined thought and action',
    ];
  }
  
  protected identifyOpportunities(_content: string): string[] {
    return [
      'Build sustainable greatness through discipline',
      'Create breakthrough via Hedgehog Concept',
      'Accelerate momentum with flywheel effect',
    ];
  }
  
  generateQuestions(_content: string): string[] {
    return [
      '🚀 What can we be the best in the world at?',
      '🎯 Where do passion, excellence, and economics intersect?',
      '🚌 Do we have the right people in the right seats?',
      '🔄 What is our flywheel and how do we accelerate it?',
    ];
  }
}

/**
 * Nassim Taleb - Risk and Uncertainty Expert
 */
export class TalebExpert extends BaseBusinessExpert {
  id: BusinessExpertId = 'taleb';
  name = 'Nassim Taleb';
  framework = 'Antifragility & Black Swan Theory';
  focusAreas = ['risk', 'uncertainty', 'robustness', 'optionality'];
  communicationStyle = 'contrarian, risk-aware';
  symbol = '🎲';
  
  protected extractInsights(content: string): string[] {
    const insights = [];
    const lower = content.toLowerCase();
    
    if (lower.includes('risk') || lower.includes('uncertainty')) {
      insights.push('🎲 Build antifragility: gain from disorder rather than break');
    }
    if (lower.includes('prediction') || lower.includes('forecast')) {
      insights.push('🦢 Black Swans: Prepare for unpredictable high-impact events');
    }
    if (lower.includes('strategy') || lower.includes('planning')) {
      insights.push('💪 Via negativa: Focus on what not to do');
    }
    
    return insights;
  }
  
  protected generateRecommendations(_content: string): string[] {
    return [
      'Build optionality with limited downside, unlimited upside',
      'Implement barbell strategy: conservative + aggressive',
      'Reduce fragility through redundancy and buffers',
      'Focus on robustness over optimization',
    ];
  }
  
  protected identifyRisks(_content: string): string[] {
    return [
      'Hidden fragilities from over-optimization',
      'Turkey problem: Past success creating complacency',
      'Model error and overconfidence in predictions',
    ];
  }
  
  protected identifyOpportunities(_content: string): string[] {
    return [
      'Create convex payoffs with limited downside',
      'Build antifragile systems that improve with stress',
      'Exploit optionality in uncertain environments',
    ];
  }
  
  generateQuestions(_content: string): string[] {
    return [
      '🎲 What makes this antifragile vs merely robust?',
      '🦢 What Black Swans could destroy or transform this?',
      '💪 What should we stop doing (via negativa)?',
      '📊 Where is our exposure to model error?',
    ];
  }
}

/**
 * Donella Meadows - Systems Thinking Expert
 */
export class MeadowsExpert extends BaseBusinessExpert {
  id: BusinessExpertId = 'meadows';
  name = 'Donella Meadows';
  framework = 'Systems Thinking & Leverage Points';
  focusAreas = ['systems', 'leverage', 'feedback', 'complexity'];
  communicationStyle = 'holistic, systems-focused';
  symbol = '🕸️';
  
  protected extractInsights(content: string): string[] {
    const insights = [];
    const lower = content.toLowerCase();
    
    if (lower.includes('system') || lower.includes('complex')) {
      insights.push('🕸️ System structure drives behavior patterns');
    }
    if (lower.includes('change') || lower.includes('intervention')) {
      insights.push('🎯 Leverage points: Places to intervene in systems');
    }
    if (lower.includes('feedback') || lower.includes('loop')) {
      insights.push('🔄 Feedback loops determine system dynamics');
    }
    
    return insights;
  }
  
  protected generateRecommendations(_content: string): string[] {
    return [
      'Map system structure including feedback loops',
      'Identify high-leverage intervention points',
      'Design balancing feedback for stability',
      'Create reinforcing loops for growth',
    ];
  }
  
  protected identifyRisks(_content: string): string[] {
    return [
      'Unintended consequences from system interventions',
      'Policy resistance from balancing feedback',
      'System traps: tragedy of commons, drift to low performance',
    ];
  }
  
  protected identifyOpportunities(_content: string): string[] {
    return [
      'Transform system through paradigm shifts',
      'Use leverage points for maximum impact',
      'Design resilient self-organizing systems',
    ];
  }
  
  generateQuestions(_content: string): string[] {
    return [
      '🕸️ What is the system structure creating this behavior?',
      '🎯 Where are the highest leverage points for change?',
      '🔄 What feedback loops are driving dynamics?',
      '⚖️ How can we shift from reinforcing problems to solutions?',
    ];
  }
}

/**
 * Jean-luc Doumont - Communication Expert
 */
export class DoumontExpert extends BaseBusinessExpert {
  id: BusinessExpertId = 'doumont';
  name = 'Jean-luc Doumont';
  framework = 'Trees, Maps, and Theorems';
  focusAreas = ['communication', 'clarity', 'structure', 'visualization'];
  communicationStyle = 'precise, clarity-focused';
  symbol = '✏️';
  
  protected extractInsights(content: string): string[] {
    const insights = [];
    const lower = content.toLowerCase();
    
    if (lower.includes('communication') || lower.includes('message')) {
      insights.push('✏️ Optimize signal-to-noise ratio in all communication');
    }
    if (lower.includes('presentation') || lower.includes('document')) {
      insights.push('📐 Structure determines understanding and retention');
    }
    if (lower.includes('audience') || lower.includes('user')) {
      insights.push('👥 Adapt to audience needs, context, and constraints');
    }
    
    return insights;
  }
  
  protected generateRecommendations(_content: string): string[] {
    return [
      'Structure messages hierarchically for clarity',
      'Minimize cognitive load through progressive disclosure',
      'Use visual hierarchy to guide attention',
      'Test comprehension with target audience',
    ];
  }
  
  protected identifyRisks(_content: string): string[] {
    return [
      'Information overload reducing comprehension',
      'Poor structure obscuring key messages',
      'Misalignment with audience mental models',
    ];
  }
  
  protected identifyOpportunities(_content: string): string[] {
    return [
      'Enhance clarity through structured communication',
      'Improve retention with visual hierarchy',
      'Increase impact through audience adaptation',
    ];
  }
  
  generateQuestions(_content: string): string[] {
    return [
      '✏️ What is the one key message to convey?',
      '📐 How can we structure this for maximum clarity?',
      '👥 What does the audience need to know, feel, and do?',
      '📊 Where can visuals replace text effectively?',
    ];
  }
}

/**
 * Expert factory
 */
export class ExpertFactory {
  private static experts: Map<BusinessExpertId, BusinessExpert> = new Map();
  
  static {
    // Initialize all experts
    this.experts.set('christensen', new ChristensenExpert());
    this.experts.set('porter', new PorterExpert());
    this.experts.set('drucker', new DruckerExpert());
    this.experts.set('godin', new GodinExpert());
    this.experts.set('kim_mauborgne', new KimMauborgneExpert());
    this.experts.set('collins', new CollinsExpert());
    this.experts.set('taleb', new TalebExpert());
    this.experts.set('meadows', new MeadowsExpert());
    this.experts.set('doumont', new DoumontExpert());
  }
  
  static getExpert(id: BusinessExpertId): BusinessExpert {
    const expert = this.experts.get(id);
    if (!expert) {
      throw new Error(`Expert ${id} not found`);
    }
    return expert;
  }
  
  static getAllExperts(): BusinessExpert[] {
    return Array.from(this.experts.values());
  }
  
  static getExpertsByDomain(domain: string): BusinessExpert[] {
    const domainMapping: Record<string, BusinessExpertId[]> = {
      innovation: ['christensen', 'drucker', 'godin'],
      strategy: ['porter', 'kim_mauborgne', 'collins'],
      risk: ['taleb', 'meadows'],
      marketing: ['godin', 'christensen'],
      systems: ['meadows', 'drucker'],
      communication: ['doumont', 'godin'],
      organization: ['collins', 'drucker'],
    };
    
    const expertIds = domainMapping[domain] || [];
    return expertIds.map(id => this.getExpert(id));
  }
}
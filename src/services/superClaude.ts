/**
 * SuperClaude Framework Service
 * Handles integration with SuperClaude commands, modes, personas, and features
 */

import type {
  SuperClaudeCommand,
  SuperClaudeMode,
  SuperClaudePersona,
  SuperClaudeConfig,
  SuperClaudeSessionState,
  SuperClaudeCommandContext,
  BusinessPanelExpert,
  ModeActivationContext,
  PersonaActivationContext,
} from '@/types/superClaude';

import { 
  agentRegistry, 
  AgentActivationContext,
  AgentBehaviorResult
} from './agents';

import {
  waveEngine,
  WaveExecutionContext,
  WaveOrchestrationOptions,
} from './wave';

import {
  businessPanelOrchestrator,
  BusinessPanelRequest,
  BusinessAnalysisMode,
} from './businessPanel';

import {
  mcpOrchestrator,
  MCPServerId,
  MCPActivationContext,
  MCPOrchestrationOptions,
  serenaMCP,
  morphllmMCP,
} from './mcp';

import { tokenCompressionService } from './tokenCompressionService';

/**
 * MCP Server definitions
 */
// const MCP_SERVERS: Record<MCPServerId, any> = {
//   context7: { name: 'Context7', purpose: 'Documentation & patterns' },
//   sequential: { name: 'Sequential', purpose: 'Complex analysis' },
//   magic: { name: 'Magic', purpose: 'UI generation' },
//   playwright: { name: 'Playwright', purpose: 'Browser testing' },
//   serena: { name: 'Serena', purpose: 'Symbol operations & memory' },
//   morphllm: { name: 'Morphllm', purpose: 'Pattern-based edits' },
// };

/**
 * SuperClaude command definitions
 */
const SUPERCLAUDE_COMMANDS: SuperClaudeCommand[] = [
  // Analysis Commands
  {
    name: 'analyze',
    description: 'Multi-dimensional code and system analysis',
    category: 'analysis',
    waveEnabled: true,
    performanceProfile: 'complex',
    autoPersonas: ['analyzer', 'architect', 'security'],
    mcpServers: ['sequential', 'context7'],
    arguments: '[target] @<path> !<command> --<flags>',
    examples: ['/sc:analyze @src/', '/sc:analyze architecture --ultrathink'],
  },
  {
    name: 'troubleshoot',
    description: 'Problem investigation and root cause analysis',
    category: 'analysis',
    waveEnabled: false,
    performanceProfile: 'standard',
    autoPersonas: ['analyzer', 'qa'],
    mcpServers: ['sequential', 'playwright'],
    arguments: '[symptoms] [flags]',
  },
  {
    name: 'explain',
    description: 'Educational explanations and documentation',
    category: 'documentation',
    waveEnabled: false,
    performanceProfile: 'standard',
    autoPersonas: ['mentor', 'scribe'],
    mcpServers: ['context7', 'sequential'],
    arguments: '[topic] [flags]',
  },
  
  // Development Commands
  {
    name: 'build',
    description: 'Project builder with framework detection',
    category: 'development',
    waveEnabled: true,
    performanceProfile: 'optimization',
    autoPersonas: ['frontend', 'backend', 'architect', 'scribe'],
    mcpServers: ['magic', 'context7', 'sequential'],
    arguments: '[target] @<path> !<command> --<flags>',
    examples: ['/sc:build component --magic', '/sc:build api --framework express'],
  },
  {
    name: 'implement',
    description: 'Feature and code implementation with intelligent persona activation',
    category: 'development',
    waveEnabled: true,
    performanceProfile: 'standard',
    autoPersonas: ['frontend', 'backend', 'architect', 'security'],
    mcpServers: ['magic', 'context7', 'sequential'],
    arguments: '[feature-description] --type component|api|service|feature --framework <name>',
    examples: ['/sc:implement authentication --type api', '/sc:implement dashboard --type component'],
  },
  {
    name: 'design',
    description: 'Design orchestration for UI and architecture',
    category: 'development',
    waveEnabled: true,
    performanceProfile: 'standard',
    autoPersonas: ['architect', 'frontend'],
    mcpServers: ['magic', 'sequential', 'context7'],
    arguments: '[domain] [flags]',
  },
  
  // Quality Commands
  {
    name: 'improve',
    description: 'Evidence-based code enhancement',
    category: 'quality',
    waveEnabled: true,
    performanceProfile: 'optimization',
    autoPersonas: ['refactorer', 'performance', 'architect', 'qa'],
    mcpServers: ['sequential', 'context7', 'magic'],
    arguments: '[target] @<path> !<command> --<flags>',
    examples: ['/sc:improve performance --focus backend', '/sc:improve --loop --iterations 3'],
  },
  {
    name: 'cleanup',
    description: 'Project cleanup and technical debt reduction',
    category: 'quality',
    waveEnabled: false,
    performanceProfile: 'standard',
    autoPersonas: ['refactorer'],
    mcpServers: ['sequential', 'morphllm', 'serena'],
    arguments: '[target] [flags]',
  },
  {
    name: 'test',
    description: 'Testing workflows and test generation',
    category: 'testing',
    waveEnabled: false,
    performanceProfile: 'standard',
    autoPersonas: ['qa'],
    mcpServers: ['playwright', 'sequential'],
    arguments: '[type] [flags]',
  },
  
  // Documentation Commands
  {
    name: 'document',
    description: 'Documentation generation and maintenance',
    category: 'documentation',
    waveEnabled: false,
    performanceProfile: 'standard',
    autoPersonas: ['scribe', 'mentor'],
    mcpServers: ['context7', 'sequential'],
    arguments: '[target] [flags]',
  },
  
  // Planning Commands
  {
    name: 'estimate',
    description: 'Evidence-based estimation and planning',
    category: 'planning',
    waveEnabled: false,
    performanceProfile: 'standard',
    autoPersonas: ['analyzer', 'architect'],
    mcpServers: ['sequential', 'context7'],
    arguments: '[target] [flags]',
  },
  {
    name: 'task',
    description: 'Long-term project management',
    category: 'planning',
    waveEnabled: true,
    performanceProfile: 'standard',
    autoPersonas: ['architect', 'analyzer'],
    mcpServers: ['sequential'],
    arguments: '[operation] [flags]',
  },
  {
    name: 'workflow',
    description: 'Workflow design and optimization',
    category: 'planning',
    waveEnabled: true,
    performanceProfile: 'complex',
    autoPersonas: ['architect', 'devops'],
    mcpServers: ['sequential', 'context7'],
    arguments: '[workflow-type] [flags]',
  },
  
  // Meta Commands
  {
    name: 'index',
    description: 'Command catalog browsing and discovery',
    category: 'meta',
    waveEnabled: false,
    performanceProfile: 'standard',
    autoPersonas: ['mentor', 'analyzer'],
    mcpServers: ['sequential'],
    arguments: '[query] [flags]',
  },
  {
    name: 'load',
    description: 'Project context loading and initialization',
    category: 'meta',
    waveEnabled: false,
    performanceProfile: 'standard',
    autoPersonas: ['analyzer', 'architect', 'scribe'],
    mcpServers: [],
    arguments: '[path] [flags]',
  },
  {
    name: 'spawn',
    description: 'Task orchestration and sub-agent spawning',
    category: 'meta',
    waveEnabled: false,
    performanceProfile: 'complex',
    autoPersonas: ['analyzer', 'architect', 'devops'],
    mcpServers: [],
    arguments: '[mode] [flags]',
  },
  {
    name: 'git',
    description: 'Git workflow assistant',
    category: 'development',
    waveEnabled: false,
    performanceProfile: 'standard',
    autoPersonas: ['devops', 'scribe', 'qa'],
    mcpServers: ['sequential'],
    arguments: '[operation] [flags]',
  },
  
  // Business Panel
  {
    name: 'business-panel',
    description: 'Business analysis with expert panel discussion',
    category: 'analysis',
    waveEnabled: false,
    performanceProfile: 'complex',
    autoPersonas: ['analyzer', 'architect'],
    mcpServers: ['sequential', 'context7'],
    arguments: '@<document> --mode discussion|debate|socratic --experts <list>',
    examples: ['/sc:business-panel @strategy.pdf', '/sc:business-panel "market analysis" --mode debate'],
  },
  
  // Additional Commands
  {
    name: 'refactor',
    description: 'Systematic code refactoring with quality focus',
    category: 'quality',
    waveEnabled: true,
    performanceProfile: 'optimization',
    autoPersonas: ['refactorer', 'architect', 'qa'],
    mcpServers: ['sequential', 'context7'],
    arguments: '@<path> --focus <aspect> --preserve-behavior',
    examples: ['/sc:refactor @src/ --focus readability', '/sc:refactor @component.tsx --preserve-behavior'],
  },
  {
    name: 'debug',
    description: 'Systematic debugging and root cause analysis',
    category: 'analysis',
    waveEnabled: false,
    performanceProfile: 'complex',
    autoPersonas: ['analyzer', 'qa', 'backend'],
    mcpServers: ['sequential', 'playwright'],
    arguments: '[issue] @<path> --reproduce --isolate',
    examples: ['/sc:debug "memory leak" @app/', '/sc:debug "crash on login" --reproduce'],
  },
  {
    name: 'optimize',
    description: 'Performance optimization with metrics',
    category: 'quality',
    waveEnabled: true,
    performanceProfile: 'optimization',
    autoPersonas: ['performance', 'backend', 'architect'],
    mcpServers: ['sequential', 'playwright'],
    arguments: '@<path> --target <metric> --benchmark',
    examples: ['/sc:optimize @api/ --target response-time', '/sc:optimize @frontend/ --benchmark'],
  },
  {
    name: 'review',
    description: 'Comprehensive code review with multi-aspect analysis',
    category: 'quality',
    waveEnabled: false,
    performanceProfile: 'complex',
    autoPersonas: ['qa', 'security', 'architect', 'refactorer'],
    mcpServers: ['sequential', 'context7'],
    arguments: '@<path> --focus <aspects> --checklist',
    examples: ['/sc:review @pr/feature', '/sc:review @src/ --focus security,performance'],
  },
];

/**
 * Mode configurations
 */
const SUPERCLAUDE_MODES = {
  normal: {
    id: 'normal' as SuperClaudeMode,
    name: 'Normal',
    description: 'Standard Claude behavior',
    activationTriggers: [],
    behaviorChanges: [],
    symbolsEnabled: false,
  },
  brainstorming: {
    id: 'brainstorming' as SuperClaudeMode,
    name: 'Brainstorming',
    description: 'Collaborative discovery mindset',
    activationTriggers: ['brainstorm', 'explore', 'discuss', 'figure out', 'not sure'],
    behaviorChanges: ['Socratic dialogue', 'Non-presumptive', 'Collaborative exploration'],
    symbolsEnabled: true,
  },
  introspection: {
    id: 'introspection' as SuperClaudeMode,
    name: 'Introspection',
    description: 'Meta-cognitive analysis',
    activationTriggers: ['analyze reasoning', 'reflect', 'self-analysis'],
    behaviorChanges: ['Self-examination', 'Transparency', 'Pattern detection'],
    symbolsEnabled: true,
  },
  'task-management': {
    id: 'task-management' as SuperClaudeMode,
    name: 'Task Management',
    description: 'Structured workflow execution',
    activationTriggers: ['task', 'todo', 'plan', 'workflow'],
    behaviorChanges: ['Evidence-based progress', 'Single focus', 'Real-time updates'],
    symbolsEnabled: true,
  },
  orchestration: {
    id: 'orchestration' as SuperClaudeMode,
    name: 'Orchestration',
    description: 'Intelligent tool selection',
    activationTriggers: ['optimize', 'coordinate', 'parallel'],
    behaviorChanges: ['Smart tool selection', 'Resource awareness', 'Parallel thinking'],
    symbolsEnabled: true,
  },
  'token-efficiency': {
    id: 'token-efficiency' as SuperClaudeMode,
    name: 'Token Efficiency',
    description: 'Symbol-enhanced communication',
    activationTriggers: ['--uc', '--ultracompressed', 'compress'],
    behaviorChanges: ['Symbol communication', 'Abbreviation', '30-50% token reduction'],
    symbolsEnabled: true,
  },
  'business-panel': {
    id: 'business-panel' as SuperClaudeMode,
    name: 'Business Panel',
    description: 'Multi-expert business analysis',
    activationTriggers: ['business-panel', 'strategic analysis'],
    behaviorChanges: ['Expert discussions', 'Framework integration', 'Strategic synthesis'],
    symbolsEnabled: true,
  },
};

/**
 * SuperClaude Service Class
 */
export class SuperClaudeService {
  private config: SuperClaudeConfig;
  private sessionState: SuperClaudeSessionState;

  constructor() {
    this.config = {
      commands: SUPERCLAUDE_COMMANDS,
      modes: Object.values(SUPERCLAUDE_MODES),
      personas: [], // To be populated
      businessPanelExperts: [], // To be populated
      symbolCategories: [], // To be populated
      mcpServers: [], // To be populated
      waveStrategies: [], // To be populated
    };

    this.sessionState = {
      activeMode: 'normal',
      activePersonas: [],
      tokenEfficiencyEnabled: false,
      businessPanelActive: false,
      mcpServersActive: [],
    };
  }

  /**
   * Parse a command string to extract SuperClaude command and context
   */
  parseCommand(input: string): SuperClaudeCommandContext | null {
    // Trim any whitespace and normalize the input
    const trimmedInput = input.trim();
    
    // More robust regex that handles multiline and edge cases
    // The 's' flag allows . to match newlines
    const scPattern = /^\/sc:(\w+)(?:\s+(.*))?$/s;
    const match = trimmedInput.match(scPattern);

    // Enhanced logging for debugging
    console.log('[SuperClaude.parseCommand] Parsing details:', {
      originalInput: input,
      trimmedInput: trimmedInput,
      inputLength: input.length,
      hasMatch: !!match,
      matchGroups: match ? match.slice(1) : null,
      pattern: scPattern.toString()
    });

    if (!match) {
      console.warn('[SuperClaude.parseCommand] Failed to parse:', {
        input: trimmedInput,
        reason: 'Pattern mismatch',
        suggestion: 'Use format: /sc:command [arguments] [--flags]'
      });
      return null;
    }

    const [, commandName, args] = match;
    const command = this.config.commands.find(cmd => cmd.name === commandName);

    console.log('[SuperClaude.parseCommand] Command lookup:', { 
      commandName, 
      args,
      found: command ? true : false 
    });

    if (!command) {
      console.warn('[SuperClaude.parseCommand] Command not found:', {
        attempted: commandName,
        available: this.config.commands.map(c => c.name).join(', ')
      });
      return null;
    }

    // Parse flags from arguments
    const flags: string[] = [];
    let cleanedArgs = args || '';
    
    const flagPattern = /--(\w+)(?:=([^\s]+))?/g;
    let flagMatch;
    
    while ((flagMatch = flagPattern.exec(cleanedArgs)) !== null) {
      flags.push(flagMatch[1]);
      if (flagMatch[2]) {
        flags.push(flagMatch[2]);
      }
    }
    
    // Remove flags from arguments
    cleanedArgs = cleanedArgs.replace(flagPattern, '').trim();

    const context = {
      command: commandName,
      arguments: cleanedArgs,
      flags,
      personas: (command.autoPersonas as SuperClaudePersona[]) || [],
      mcpServers: command.mcpServers || [],
    };

    console.log('[SuperClaude.parseCommand] Returning context:', context);
    return context;
  }

  /**
   * Detect mode from input
   */
  detectMode(input: string): SuperClaudeMode {
    const lowerInput = input.toLowerCase();

    for (const mode of this.config.modes) {
      if (mode.activationTriggers.some(trigger => lowerInput.includes(trigger))) {
        return mode.id;
      }
    }

    return 'normal';
  }

  /**
   * Get command by name
   */
  getCommand(name: string): SuperClaudeCommand | undefined {
    return this.config.commands.find(cmd => cmd.name === name);
  }

  /**
   * Get required MCP servers for a command
   */
  getRequiredMCPServers(commandName: string): string[] {
    const command = this.getCommand(commandName);
    return command?.mcpServers || [];
  }

  /**
   * Check if MCP servers are available
   */
  async checkMCPServersAvailable(_servers: string[]): Promise<boolean> {
    // This would check with the backend if the MCP servers are configured
    // For now, return true to indicate they're available
    return true;
  }

  /**
   * Get all commands
   */
  getAllCommands(): SuperClaudeCommand[] {
    return this.config.commands;
  }

  /**
   * Get commands by category
   */
  getCommandsByCategory(category: string): SuperClaudeCommand[] {
    return this.config.commands.filter(cmd => cmd.category === category);
  }

  /**
   * Get current session state
   */
  getSessionState(): SuperClaudeSessionState {
    return this.sessionState;
  }

  /**
   * Update session state
   */
  updateSessionState(updates: Partial<SuperClaudeSessionState>): void {
    this.sessionState = {
      ...this.sessionState,
      ...updates,
    };
  }

  /**
   * Format command for Claude Code execution
   */
  formatCommandForExecution(context: SuperClaudeCommandContext): string {
    const command = this.getCommand(context.command);
    if (!command) {
      return '';
    }

    // Preserve the /sc: prefix for SuperClaude commands
    let formattedPrompt = `/sc:${command.name}`;
    
    if (context.arguments) {
      formattedPrompt += ` ${context.arguments}`;
    }

    // Add mode indicators
    if (this.sessionState.activeMode !== 'normal') {
      formattedPrompt = `[Mode: ${this.sessionState.activeMode}] ${formattedPrompt}`;
    }

    // Add persona indicators
    if (context.personas && context.personas.length > 0) {
      formattedPrompt = `[Personas: ${context.personas.join(', ')}] ${formattedPrompt}`;
    }

    // Add wave strategy if applicable
    if (command.waveEnabled && context.waveStrategy) {
      formattedPrompt = `[Wave: ${context.waveStrategy.name}] ${formattedPrompt}`;
    }

    return formattedPrompt;
  }

  /**
   * Check if a command is a SuperClaude command
   */
  isSuperClaudeCommand(input: string): boolean {
    const trimmed = input.trim();
    // Use same pattern as parseCommand for consistency
    return /^\/sc:\w+/.test(trimmed);
  }

  /**
   * Get symbol for token efficiency mode
   */
  getSymbol(meaning: string): string {
    // Extended symbol mapping for token efficiency mode
    const symbolMap: { [key: string]: string } = {
      // Status & Progress
      'completed': '✅',
      'passed': '✅',
      'failed': '❌',
      'error': '❌',
      'warning': '⚠️',
      'in progress': '🔄',
      'pending': '⏳',
      'waiting': '⏳',
      'critical': '🚨',
      'urgent': '🚨',
      'target': '🎯',
      'goal': '🎯',
      'metrics': '📊',
      'data': '📊',
      'insight': '💡',
      'learning': '💡',
      
      // Technical Domains
      'performance': '⚡',
      'speed': '⚡',
      'optimization': '⚡',
      'security': '🛡️',
      'protection': '🛡️',
      'analysis': '🔍',
      'search': '🔍',
      'investigation': '🔍',
      'configuration': '🔧',
      'setup': '🔧',
      'tools': '🔧',
      'deployment': '📦',
      'package': '📦',
      'bundle': '📦',
      'design': '🎨',
      'ui': '🎨',
      'frontend': '🎨',
      'architecture': '🏗️',
      'structure': '🏗️',
      'system': '🏗️',
      'network': '🌐',
      'web': '🌐',
      'mobile': '📱',
      'responsive': '📱',
      'components': '🧩',
      'modular': '🧩',
      
      // Business Symbols
      'strategy': '🎯',
      'growth': '📈',
      'decline': '📉',
      'risk': '📉',
      'financial': '💰',
      'revenue': '💰',
      'competitive': '🏆',
      'advantage': '🏆',
      'cycle': '🔄',
      'feedback': '🔄',
      'blue ocean': '🌊',
      'new market': '🌊',
      'industry': '🏭',
      'remarkable': '🎪',
      'purple cow': '🎪',
    };

    return symbolMap[meaning.toLowerCase()] || meaning;
  }

  /**
   * Enhanced mode detection with behavioral activation triggers
   */
  detectModeWithContext(prompt: string, context?: ModeActivationContext): SuperClaudeMode {
    const lower = prompt.toLowerCase();
    
    // Check for explicit mode flags
    if (lower.includes('--brainstorm') || lower.includes('--bs')) {
      return 'brainstorming';
    }
    if (lower.includes('--introspect') || lower.includes('--introspection')) {
      return 'introspection';
    }
    if (lower.includes('--task-manage') || lower.includes('--delegate')) {
      return 'task-management';
    }
    if (lower.includes('--orchestrate')) {
      return 'orchestration';
    }
    if (lower.includes('--uc') || lower.includes('--ultracompressed')) {
      return 'token-efficiency';
    }
    if (lower.includes('--business-panel') || lower.includes('/sc:business-panel')) {
      return 'business-panel';
    }
    
    // Context-aware mode detection
    if (context) {
      // Check resource usage for token efficiency
      if (context.resourceUsage && context.resourceUsage > 75) {
        return 'token-efficiency';
      }
      
      // Check complexity for task management
      if (context.complexity && context.complexity > 3) {
        return 'task-management';
      }
      
      // Check for multi-tool operations
      if (context.multiToolOperation) {
        return 'orchestration';
      }
      
      // Check for error recovery
      if (context.errorRecovery) {
        return 'introspection';
      }
    }
    
    // Brainstorming mode triggers
    const brainstormTriggers = ['brainstorm', 'explore', 'figure out', 'not sure', 'maybe', 'thinking about', 'possibly', 'could we'];
    if (brainstormTriggers.some(trigger => lower.includes(trigger))) {
      return 'brainstorming';
    }
    
    // Introspection mode triggers
    const introspectionTriggers = ['analyze my reasoning', 'reflect on', 'meta-cognitive', 'self-analysis', 'framework compliance'];
    if (introspectionTriggers.some(trigger => lower.includes(trigger))) {
      return 'introspection';
    }
    
    // Task management triggers
    const taskTriggers = ['task', 'todo', 'plan', 'organize', 'manage', 'track', 'polish', 'refine', 'enhance'];
    if (taskTriggers.some(trigger => lower.includes(trigger)) || 
        (lower.includes('implement') && lower.includes('feature')) ||
        (lower.includes('build') && lower.includes('system'))) {
      return 'task-management';
    }
    
    // Orchestration triggers
    const orchestrationTriggers = ['optimize', 'parallel', 'concurrent', 'coordinate', 'orchestrate'];
    if (orchestrationTriggers.some(trigger => lower.includes(trigger))) {
      return 'orchestration';
    }
    
    // Token efficiency triggers
    const efficiencyTriggers = ['compress', 'brief', 'concise', 'summary', 'minimal'];
    if (efficiencyTriggers.some(trigger => lower.includes(trigger))) {
      return 'token-efficiency';
    }
    
    // Business panel triggers
    const businessTriggers = ['business', 'strategic', 'competitive', 'market', 'revenue', 'growth'];
    if (businessTriggers.some(trigger => lower.includes(trigger))) {
      return 'business-panel';
    }
    
    return 'normal';
  }

  /**
   * Get behavioral changes for a mode
   */
  getModesBehavior(mode: SuperClaudeMode): Record<string, any> {
    const behaviors: Record<SuperClaudeMode, Record<string, any>> = {
      'normal': {
        verbosity: 'standard',
        symbolsEnabled: false,
        taskTracking: 'basic',
        parallelization: 'auto',
        compressionLevel: 0,
      },
      'brainstorming': {
        verbosity: 'interactive',
        symbolsEnabled: false,
        taskTracking: 'none',
        parallelization: 'none',
        compressionLevel: 0,
        behavior: 'socratic_dialogue',
        outcome: 'requirement_brief',
      },
      'introspection': {
        verbosity: 'detailed',
        symbolsEnabled: true,
        taskTracking: 'detailed',
        parallelization: 'none',
        compressionLevel: 0,
        markers: ['🧠', '🔄', '🎯', '📊', '🔍', '💡'],
        behavior: 'self_examination',
        outcome: 'pattern_recognition',
      },
      'task-management': {
        verbosity: 'structured',
        symbolsEnabled: true,
        taskTracking: 'hierarchical',
        parallelization: 'managed',
        compressionLevel: 0,
        hierarchy: ['plan', 'phase', 'task', 'todo'],
        behavior: 'systematic_execution',
        outcome: 'tracked_progress',
      },
      'orchestration': {
        verbosity: 'minimal',
        symbolsEnabled: true,
        taskTracking: 'performance',
        parallelization: 'aggressive',
        compressionLevel: 1,
        behavior: 'intelligent_routing',
        outcome: 'optimized_execution',
      },
      'token-efficiency': {
        verbosity: 'minimal',
        symbolsEnabled: true,
        taskTracking: 'essential',
        parallelization: 'auto',
        compressionLevel: 3,
        abbreviations: true,
        behavior: 'symbol_communication',
        outcome: '30_50_percent_reduction',
      },
      'business-panel': {
        verbosity: 'structured',
        symbolsEnabled: true,
        taskTracking: 'none',
        parallelization: 'none',
        compressionLevel: 1,
        experts: ['christensen', 'porter', 'drucker', 'godin', 'kim_mauborgne', 'collins', 'taleb', 'meadows', 'doumont'],
        behavior: 'multi_expert_analysis',
        outcome: 'strategic_synthesis',
      },
    };
    
    return behaviors[mode] || behaviors['normal'];
  }

  /**
   * Apply mode behavior to prompt formatting
   */
  async applyModeBehavior(prompt: string, mode: SuperClaudeMode): Promise<string> {
    const behavior = this.getModesBehavior(mode);
    
    // Apply compression for token-efficiency mode
    if (mode === 'token-efficiency' && behavior.compressionLevel > 2) {
      return await this.compressPrompt(prompt);
    }
    
    // Add mode markers for introspection
    if (mode === 'introspection' && behavior.markers) {
      return `🧠 [Introspection Mode] ${prompt}`;
    }
    
    // Add task structure for task-management
    if (mode === 'task-management') {
      return `📋 [Task Management] ${prompt}`;
    }
    
    // Add business panel formatting
    if (mode === 'business-panel' && behavior.experts) {
      return `🎯 [Business Panel] ${prompt}`;
    }
    
    return prompt;
  }

  /**
   * Compress prompt for token efficiency
   */
  private async compressPrompt(prompt: string): Promise<string> {
    // Use the token compression service for better compression
    return await tokenCompressionService.compressTextWithContext(prompt);
  }

  /**
   * Enable token efficiency mode
   */
  enableTokenEfficiency(): void {
    this.updateSessionState({ tokenEfficiencyEnabled: true });
  }

  /**
   * Disable token efficiency mode
   */
  disableTokenEfficiency(): void {
    this.updateSessionState({ tokenEfficiencyEnabled: false });
  }

  /**
   * Activate business panel mode
   */
  activateBusinessPanel(experts?: BusinessPanelExpert[]): void {
    this.updateSessionState({
      businessPanelActive: true,
      activeExperts: experts,
      activeMode: 'business-panel',
    });
  }

  /**
   * Deactivate business panel mode
   */
  deactivateBusinessPanel(): void {
    this.updateSessionState({
      businessPanelActive: false,
      activeExperts: undefined,
      activeMode: 'normal',
    });
  }

  /**
   * Auto-activate personas based on context using agent registry
   */
  autoActivatePersonas(prompt: string, context?: PersonaActivationContext): SuperClaudePersona[] {
    const personas: SuperClaudePersona[] = [];
    const lower = prompt.toLowerCase();
    
    // Create agent activation context
    const agentContext: AgentActivationContext = {
      domain: context?.domain,
      operationType: context?.operationType,
      complexity: context?.complexity || 0.5,
      keywords: prompt.split(' ').filter(word => word.length > 3),
      errors: lower.includes('error') || lower.includes('bug') || lower.includes('issue'),
      performance: lower.includes('performance') || lower.includes('slow') || lower.includes('optimize'),
    };
    
    // Use agent registry for sophisticated activation
    const activatedAgents = agentRegistry.autoActivate(agentContext, 0.5);
    
    // Map agents to personas (they use the same IDs)
    const agentPersonas = activatedAgents.map(agent => agent.id as SuperClaudePersona);
    if (agentPersonas.length > 0) {
      return agentPersonas;
    }
    
    // Check explicit persona flags
    const personaFlags = [
      { flag: '--persona-architect', persona: 'architect' as SuperClaudePersona },
      { flag: '--persona-frontend', persona: 'frontend' as SuperClaudePersona },
      { flag: '--persona-backend', persona: 'backend' as SuperClaudePersona },
      { flag: '--persona-security', persona: 'security' as SuperClaudePersona },
      { flag: '--persona-performance', persona: 'performance' as SuperClaudePersona },
      { flag: '--persona-analyzer', persona: 'analyzer' as SuperClaudePersona },
      { flag: '--persona-qa', persona: 'qa' as SuperClaudePersona },
      { flag: '--persona-refactorer', persona: 'refactorer' as SuperClaudePersona },
      { flag: '--persona-devops', persona: 'devops' as SuperClaudePersona },
      { flag: '--persona-mentor', persona: 'mentor' as SuperClaudePersona },
      { flag: '--persona-scribe', persona: 'scribe' as SuperClaudePersona },
    ];
    
    personaFlags.forEach(({ flag, persona }) => {
      if (lower.includes(flag)) {
        personas.push(persona);
      }
    });
    
    // If explicit personas found, return them
    if (personas.length > 0) {
      return personas;
    }
    
    // Multi-factor scoring for auto-activation
    const activationScores: Record<SuperClaudePersona, number> = {
      architect: 0,
      frontend: 0,
      backend: 0,
      security: 0,
      performance: 0,
      analyzer: 0,
      qa: 0,
      refactorer: 0,
      devops: 0,
      mentor: 0,
      scribe: 0,
      requirements: 0,
    };
    
    // Keyword matching (30% weight)
    const keywordWeights = {
      architect: ['architecture', 'design', 'scalability', 'system', 'structure', 'patterns'],
      frontend: ['ui', 'ux', 'component', 'responsive', 'accessibility', 'react', 'vue', 'css'],
      backend: ['api', 'database', 'server', 'endpoint', 'authentication', 'service'],
      security: ['vulnerability', 'threat', 'compliance', 'encryption', 'auth', 'secure'],
      performance: ['optimize', 'performance', 'bottleneck', 'speed', 'latency', 'cache'],
      analyzer: ['analyze', 'investigate', 'root cause', 'debug', 'troubleshoot'],
      qa: ['test', 'quality', 'validation', 'edge case', 'coverage', 'e2e'],
      refactorer: ['refactor', 'cleanup', 'technical debt', 'simplify', 'improve'],
      devops: ['deploy', 'infrastructure', 'automation', 'ci/cd', 'docker', 'kubernetes'],
      mentor: ['explain', 'learn', 'understand', 'teach', 'guide', 'document'],
      scribe: ['document', 'write', 'readme', 'wiki', 'guide', 'manual'],
    };
    
    Object.entries(keywordWeights).forEach(([persona, keywords]) => {
      keywords.forEach(keyword => {
        if (lower.includes(keyword)) {
          activationScores[persona as SuperClaudePersona] += 0.3;
        }
      });
    });
    
    // Context analysis (40% weight)
    if (context) {
      // Check domain
      if (context.domain === 'frontend') {
        activationScores.frontend += 0.4;
      } else if (context.domain === 'backend') {
        activationScores.backend += 0.4;
      } else if (context.domain === 'infrastructure') {
        activationScores.devops += 0.4;
      } else if (context.domain === 'security') {
        activationScores.security += 0.4;
      } else if (context.domain === 'documentation') {
        activationScores.scribe += 0.4;
      }
      
      // Check operation type
      if (context.operationType === 'analysis') {
        activationScores.analyzer += 0.2;
      } else if (context.operationType === 'creation') {
        activationScores.architect += 0.2;
      } else if (context.operationType === 'modification') {
        activationScores.refactorer += 0.2;
      } else if (context.operationType === 'debugging') {
        activationScores.analyzer += 0.2;
        activationScores.qa += 0.1;
      }
      
      // Check complexity
      if (context.complexity && context.complexity > 0.7) {
        activationScores.architect += 0.2;
        activationScores.analyzer += 0.1;
      }
    }
    
    // Command-based activation
    const command = this.parseCommand(prompt);
    if (command) {
      const cmd = this.getCommand(command.command);
      if (cmd && cmd.autoPersonas) {
        cmd.autoPersonas.forEach(persona => {
          activationScores[persona as SuperClaudePersona] += 0.5;
        });
      }
    }
    
    // User history (20% weight) - would need to be implemented with actual history
    // Performance metrics (10% weight) - would need actual metrics
    
    // Select personas with score > 0.5
    Object.entries(activationScores).forEach(([persona, score]) => {
      if (score >= 0.5) {
        personas.push(persona as SuperClaudePersona);
      }
    });
    
    // If no personas activated, return default based on command
    if (personas.length === 0 && command) {
      const cmd = this.getCommand(command.command);
      if (cmd && cmd.autoPersonas && cmd.autoPersonas.length > 0) {
        return [cmd.autoPersonas[0] as SuperClaudePersona];
      }
    }
    
    return personas;
  }

  /**
   * Get persona configuration
   */
  getPersonaConfig(persona: SuperClaudePersona): Record<string, any> {
    const configs: Record<SuperClaudePersona, Record<string, any>> = {
      architect: {
        priority: 'maintainability > scalability > performance > short-term',
        principles: ['systems thinking', 'future-proofing', 'dependency management'],
        mcpPreferences: ['sequential', 'context7'],
        focus: 'long-term architecture and scalability',
      },
      frontend: {
        priority: 'user needs > accessibility > performance > technical elegance',
        principles: ['user-centered design', 'accessibility by default', 'performance consciousness'],
        mcpPreferences: ['magic', 'playwright'],
        focus: 'UI/UX and user-facing development',
      },
      backend: {
        priority: 'reliability > security > performance > features > convenience',
        principles: ['reliability first', 'security by default', 'data integrity'],
        mcpPreferences: ['context7', 'sequential'],
        focus: 'server-side and infrastructure systems',
      },
      security: {
        priority: 'security > compliance > reliability > performance > convenience',
        principles: ['security by default', 'zero trust', 'defense in depth'],
        mcpPreferences: ['sequential', 'context7'],
        focus: 'threat modeling and vulnerability assessment',
      },
      performance: {
        priority: 'measure first > optimize critical path > user experience > avoid premature optimization',
        principles: ['measurement-driven', 'critical path focus', 'user experience'],
        mcpPreferences: ['playwright', 'sequential'],
        focus: 'optimization and bottleneck elimination',
      },
      analyzer: {
        priority: 'evidence > systematic approach > thoroughness > speed',
        principles: ['evidence-based', 'systematic method', 'root cause focus'],
        mcpPreferences: ['sequential', 'context7'],
        focus: 'root cause analysis and investigation',
      },
      qa: {
        priority: 'prevention > detection > correction > comprehensive coverage',
        principles: ['prevention focus', 'comprehensive coverage', 'risk-based testing'],
        mcpPreferences: ['playwright', 'sequential'],
        focus: 'quality assurance and testing',
      },
      refactorer: {
        priority: 'simplicity > maintainability > readability > performance > cleverness',
        principles: ['simplicity first', 'maintainability', 'technical debt management'],
        mcpPreferences: ['sequential', 'context7'],
        focus: 'code quality and technical debt management',
      },
      devops: {
        priority: 'automation > observability > reliability > scalability > manual processes',
        principles: ['infrastructure as code', 'observability by default', 'reliability engineering'],
        mcpPreferences: ['sequential', 'context7'],
        focus: 'infrastructure and deployment automation',
      },
      mentor: {
        priority: 'understanding > knowledge transfer > teaching > task completion',
        principles: ['educational focus', 'knowledge transfer', 'empowerment'],
        mcpPreferences: ['context7', 'sequential'],
        focus: 'educational guidance and knowledge transfer',
      },
      scribe: {
        priority: 'clarity > audience needs > cultural sensitivity > completeness > brevity',
        principles: ['audience-first', 'cultural sensitivity', 'professional excellence'],
        mcpPreferences: ['context7', 'sequential'],
        focus: 'professional documentation and localization',
      },
      requirements: {
        priority: 'clarity > completeness > feasibility > traceability > speed',
        principles: ['discovery first', 'stakeholder alignment', 'specification excellence'],
        mcpPreferences: ['sequential', 'context7'],
        focus: 'requirements discovery and specification',
      },
    };
    
    return configs[persona] || {};
  }

  /**
   * Detect cross-persona collaboration needs
   */
  detectCrossPersonaCollaboration(personas: SuperClaudePersona[]): Record<string, SuperClaudePersona[]> {
    const collaborations: Record<string, SuperClaudePersona[]> = {};
    
    // Define complementary patterns
    const patterns = [
      { combo: ['architect', 'performance'], name: 'system-design-optimization' },
      { combo: ['security', 'backend'], name: 'secure-server-development' },
      { combo: ['frontend', 'qa'], name: 'user-focused-testing' },
      { combo: ['mentor', 'scribe'], name: 'educational-content' },
      { combo: ['analyzer', 'refactorer'], name: 'root-cause-improvement' },
      { combo: ['devops', 'security'], name: 'infrastructure-security' },
    ];
    
    patterns.forEach(pattern => {
      const hasAll = pattern.combo.every(p => personas.includes(p as SuperClaudePersona));
      if (hasAll) {
        collaborations[pattern.name] = pattern.combo as SuperClaudePersona[];
      }
    });
    
    return collaborations;
  }

  /**
   * Execute mode-specific behaviors
   */
  async executeModeFeatures(mode: SuperClaudeMode, prompt: string): Promise<{ 
    modifiedPrompt: string; 
    metadata: Record<string, any>;
  }> {
    const metadata: Record<string, any> = {};
    let modifiedPrompt = prompt;
    
    switch (mode) {
      case 'brainstorming':
        metadata.dialogueStyle = 'socratic';
        metadata.questions = this.generateBrainstormingQuestions(prompt);
        modifiedPrompt = `[🤔 Brainstorming Mode] ${prompt}\nDiscovery Questions:\n${metadata.questions.join('\n')}`;
        break;
        
      case 'introspection':
        metadata.thinkingMarkers = ['🧠', '🔄', '🎯', '📊', '💡'];
        metadata.analysisDepth = 'meta-cognitive';
        modifiedPrompt = `[🧠 Introspection Mode] Analyzing reasoning patterns...\n${prompt}`;
        break;
        
      case 'task-management':
        metadata.taskHierarchy = this.generateTaskHierarchy(prompt);
        metadata.todoList = [];
        modifiedPrompt = `[📋 Task Management] Structuring workflow...\n${prompt}`;
        break;
        
      case 'orchestration':
        metadata.toolMatrix = this.getOptimalToolMatrix(prompt);
        metadata.parallelOps = this.identifyParallelOperations(prompt);
        modifiedPrompt = `[⚡ Orchestration Mode] Optimizing tool selection...\n${prompt}`;
        break;
        
      case 'token-efficiency':
        const compressed = await tokenCompressionService.compressTextWithContext(prompt);
        metadata.originalTokens = prompt.length;
        metadata.compressedTokens = compressed.length;
        metadata.compressionRatio = ((1 - compressed.length / prompt.length) * 100).toFixed(1) + '%';
        modifiedPrompt = `[📊 Token Efficient] ${compressed}`;
        break;
        
      case 'business-panel':
        metadata.experts = this.selectBusinessExperts(prompt);
        metadata.analysisMode = this.determineBusinessAnalysisMode(prompt);
        modifiedPrompt = `[💼 Business Panel] Experts: ${metadata.experts.join(', ')}\n${prompt}`;
        break;
    }
    
    metadata.mode = mode;
    return { modifiedPrompt, metadata };
  }

  /**
   * Generate brainstorming questions
   */
  private generateBrainstormingQuestions(prompt: string): string[] {
    const questions = [];
    const lower = prompt.toLowerCase();
    
    if (lower.includes('build') || lower.includes('create')) {
      questions.push(
        '🤔 What problem does this solve for users?',
        '🎯 Who are your target users and their main workflows?',
        '📊 What\'s your expected user volume and performance needs?',
        '🔧 Any existing systems to integrate with?'
      );
    }
    
    if (lower.includes('improve') || lower.includes('optimize')) {
      questions.push(
        '🔍 What specific challenges are users facing?',
        '⚡ Current vs desired performance metrics?',
        '📈 Success criteria for the improvement?',
        '⏰ Timeline and resource constraints?'
      );
    }
    
    return questions;
  }

  /**
   * Generate task hierarchy for task management mode
   */
  private generateTaskHierarchy(prompt: string): any {
    const complexity = this.estimateComplexity(prompt);
    
    return {
      plan: prompt.substring(0, 100),
      phases: complexity > 5 ? Math.ceil(complexity / 3) : 1,
      estimatedTasks: complexity * 2,
      requiresWave: complexity > 7,
    };
  }

  /**
   * Get optimal tool matrix for orchestration
   */
  private getOptimalToolMatrix(prompt: string): Record<string, string> {
    const matrix: Record<string, string> = {};
    const lower = prompt.toLowerCase();
    
    if (lower.includes('ui') || lower.includes('component')) {
      matrix.primary = 'Magic MCP';
      matrix.secondary = 'Context7';
    } else if (lower.includes('analyze') || lower.includes('debug')) {
      matrix.primary = 'Sequential MCP';
      matrix.secondary = 'Grep';
    } else if (lower.includes('test')) {
      matrix.primary = 'Playwright MCP';
      matrix.secondary = 'Sequential';
    }
    
    return matrix;
  }

  /**
   * Identify parallel operations
   */
  private identifyParallelOperations(prompt: string): string[] {
    const operations = [];
    const lower = prompt.toLowerCase();
    
    if (lower.includes('multiple files') || lower.includes('all files')) {
      operations.push('Parallel Read operations');
    }
    if (lower.includes('refactor') || lower.includes('update')) {
      operations.push('Batch MultiEdit operations');
    }
    if (lower.includes('test') && lower.includes('deploy')) {
      operations.push('Concurrent test and build');
    }
    
    return operations;
  }

  /**
   * Select business panel experts
   */
  private selectBusinessExperts(prompt: string): string[] {
    const experts: string[] = [];
    const lower = prompt.toLowerCase();
    
    if (lower.includes('innovation') || lower.includes('disrupt')) {
      experts.push('Christensen', 'Drucker');
    }
    if (lower.includes('strategy') || lower.includes('competitive')) {
      experts.push('Porter', 'Kim/Mauborgne');
    }
    if (lower.includes('risk') || lower.includes('uncertainty')) {
      experts.push('Taleb', 'Meadows');
    }
    if (lower.includes('communication') || lower.includes('presentation')) {
      experts.push('Doumont');
    }
    
    // Ensure at least 3 experts
    if (experts.length < 3) {
      const allExperts = ['Christensen', 'Porter', 'Drucker', 'Godin', 'Kim/Mauborgne', 'Collins', 'Taleb', 'Meadows', 'Doumont'];
      while (experts.length < 3) {
        const expert = allExperts.find(e => !experts.includes(e));
        if (expert) experts.push(expert);
        else break;
      }
    }
    
    return experts;
  }

  /**
   * Determine business analysis mode
   */
  private determineBusinessAnalysisMode(prompt: string): string {
    const lower = prompt.toLowerCase();
    
    if (lower.includes('debate') || lower.includes('challenge') || lower.includes('controversial')) {
      return 'debate';
    }
    if (lower.includes('learn') || lower.includes('understand') || lower.includes('teach')) {
      return 'socratic';
    }
    
    return 'discussion';
  }

  /**
   * Detect collaboration needs between agents
   */
  detectAgentCollaboration(primaryPersona: SuperClaudePersona, context: AgentActivationContext): SuperClaudePersona[] {
    const agentId = primaryPersona as string;
    const collaborators = agentRegistry.detectCollaboration(agentId, context);
    
    return collaborators.filter(id => id !== agentId) as SuperClaudePersona[];
  }

  /**
   * Estimate task complexity
   */
  private estimateComplexity(prompt: string): number {
    let complexity = 1;
    
    // Increase complexity based on keywords
    const complexityKeywords = [
      { pattern: /system|architecture|comprehensive/gi, weight: 3 },
      { pattern: /refactor|optimize|improve/gi, weight: 2 },
      { pattern: /multiple|all|entire/gi, weight: 2 },
      { pattern: /analyze|debug|investigate/gi, weight: 2 },
      { pattern: /implement|build|create/gi, weight: 1.5 },
    ];
    
    complexityKeywords.forEach(({ pattern, weight }) => {
      const matches = prompt.match(pattern);
      if (matches) {
        complexity += matches.length * weight;
      }
    });
    
    return Math.min(complexity, 10);
  }

  /**
   * Execute wave-based command
   */
  async executeWaveCommand(
    context: SuperClaudeCommandContext,
    options?: WaveOrchestrationOptions
  ): Promise<any> {
    // Create wave execution context
    const waveContext: WaveExecutionContext = {
      command: context.command,
      arguments: context.arguments || '',
      flags: context.flags || [],
      complexity: this.estimateComplexity(context.arguments || ''),
      fileCount: 0, // Would be calculated from actual context
      directoryCount: 0, // Would be calculated from actual context
      domains: this.identifyDomains(context.arguments || ''),
      requiresValidation: true,
      estimatedDuration: 0,
    };
    
    // Configure wave engine
    if (options) {
      waveEngine.configure(options);
    }
    
    // Check if waves should be used
    if (!waveEngine.shouldUseWaves(waveContext)) {
      console.log('[SuperClaude] Wave execution not required for this command');
      return null;
    }
    
    // Select and execute strategy
    const strategy = waveEngine.selectStrategy(waveContext);
    console.log(`[SuperClaude] Executing wave strategy: ${strategy.name}`);
    
    // Execute waves
    const results = await waveEngine.execute(strategy, waveContext);
    
    // Update session state - convert WaveStrategy to SuperClaudeWaveStrategy
    this.sessionState.activeWaveStrategy = {
      name: strategy.name as any,
      description: strategy.name,
      waves: strategy.waves.map(w => ({
        waveNumber: w.waveNumber,
        phase: w.phase as any,
        operations: w.operations,
        persona: w.persona as any,
        mcpServers: w.mcpServers || [],
      })),
      validationGates: strategy.validationGates,
    };
    this.sessionState.currentWave = waveEngine.getCurrentWave();
    
    return results;
  }

  /**
   * Execute business panel analysis
   */
  async executeBusinessPanel(
    content: string,
    mode?: BusinessAnalysisMode,
    experts?: string[]
  ): Promise<any> {
    // Create business panel request
    const request: BusinessPanelRequest = {
      content,
      mode: mode || 'discussion',
      experts: experts as any[], // Type conversion for expert IDs
      autoSelectExperts: !experts || experts.length === 0,
      synthesisOnly: false,
    };
    
    // Execute analysis
    const response = await businessPanelOrchestrator.analyze(request);
    
    // Update session state
    this.sessionState.businessPanelActive = true;
    this.sessionState.activeExperts = response.session.experts as any[];
    
    // Generate executive summary
    const summary = businessPanelOrchestrator.generateExecutiveSummary(response);
    
    return {
      response,
      summary,
    };
  }

  /**
   * Identify domains from content
   */
  private identifyDomains(content: string): string[] {
    const domains: string[] = [];
    const lower = content.toLowerCase();
    
    const domainPatterns = {
      frontend: ['ui', 'component', 'react', 'vue', 'css'],
      backend: ['api', 'server', 'database', 'endpoint'],
      security: ['auth', 'encryption', 'vulnerability', 'threat'],
      performance: ['optimize', 'speed', 'cache', 'latency'],
      architecture: ['system', 'design', 'structure', 'pattern'],
    };
    
    Object.entries(domainPatterns).forEach(([domain, keywords]) => {
      if (keywords.some(keyword => lower.includes(keyword))) {
        domains.push(domain);
      }
    });
    
    return domains;
  }

  /**
   * Check if command is wave-enabled
   */
  isWaveEnabledCommand(command: string): boolean {
    const waveCommands = ['analyze', 'build', 'implement', 'improve', 'design', 'task'];
    return waveCommands.includes(command);
  }

  /**
   * Select MCP servers for context
   */
  selectMCPServers(context: SuperClaudeCommandContext): MCPServerId[] {
    const mcpContext: MCPActivationContext = {
      taskType: context.command,
      complexity: this.estimateComplexity(context.arguments || ''),
      keywords: (context.arguments || '').split(' '),
      operationType: context.command,
      requiresSymbolOperations: ['refactor', 'rename', 'extract'].includes(context.command),
      requiresBulkEdits: ['cleanup', 'improve', 'migrate'].includes(context.command),
      requiresDocumentation: ['document', 'explain'].includes(context.command),
      requiresUIGeneration: ['build', 'implement'].includes(context.command) && 
                           (context.arguments || '').includes('component'),
      requiresTesting: context.command === 'test',
      requiresAnalysis: ['analyze', 'troubleshoot'].includes(context.command),
    };

    const primaryServer = mcpOrchestrator.selectServer(mcpContext);
    const servers: MCPServerId[] = [];
    
    if (primaryServer) {
      servers.push(primaryServer);
    }

    // Add command-specific servers
    const commandConfig = SUPERCLAUDE_COMMANDS.find(cmd => cmd.name === context.command);
    if (commandConfig && commandConfig.mcpServers) {
      commandConfig.mcpServers.forEach((server: string) => {
        if (!servers.includes(server as MCPServerId)) {
          servers.push(server as MCPServerId);
        }
      });
    }

    return servers;
  }

  /**
   * Execute MCP operation
   */
  async executeMCPOperation(
    operation: any,
    servers?: MCPServerId[]
  ): Promise<any> {
    const options: MCPOrchestrationOptions = {
      primaryServer: servers?.[0],
      fallbackServers: servers?.slice(1),
      enableCaching: true,
      parallelExecution: servers && servers.length > 1,
    };

    try {
      const result = await mcpOrchestrator.execute(operation, options);
      
      // Update session state with active MCP servers
      if (result.primaryResult?.serverId) {
        if (!this.sessionState.mcpServersActive.includes(result.primaryResult.serverId)) {
          this.sessionState.mcpServersActive.push(result.primaryResult.serverId);
        }
      }

      return result;
    } catch (error) {
      console.error('[SuperClaude] MCP operation failed:', error);
      throw error;
    }
  }

  /**
   * Load project context using Serena
   */
  async loadProjectContext(projectPath: string): Promise<void> {
    console.log('[SuperClaude] Loading project context with Serena');
    
    const operation = {
      type: 'load',
      projectPath,
    };

    const result = await serenaMCP.execute(operation);
    
    if (result.success) {
      console.log('[SuperClaude] Project context loaded:', result.data);
      // this.sessionState.projectContext = result.data;
    }
  }

  /**
   * Save project context using Serena
   */
  async saveProjectContext(projectPath: string): Promise<void> {
    console.log('[SuperClaude] Saving project context with Serena');
    
    const operation = {
      type: 'save',
      projectPath,
    };

    const result = await serenaMCP.execute(operation);
    
    if (result.success) {
      console.log('[SuperClaude] Project context saved:', result.data);
    }
  }

  /**
   * Execute bulk edit using Morphllm
   */
  async executeBulkEdit(patterns: any[], dryRun = false): Promise<any> {
    console.log('[SuperClaude] Executing bulk edit with Morphllm');
    
    const operation = {
      type: 'bulk-edit',
      data: {
        patterns,
        dryRun,
        tokenOptimization: true,
      },
    };

    const result = await morphllmMCP.execute(operation);
    
    if (result.success) {
      console.log('[SuperClaude] Bulk edit result:', result.data);
      return result.data;
    }
    
    throw new Error('Bulk edit failed: ' + result.error);
  }

  /**
   * Get available MCP servers
   */
  getAvailableMCPServers(): MCPServerId[] {
    return mcpOrchestrator.getAvailableServers();
  }

  /**
   * Check MCP server health
   */
  getMCPServerHealth(): any {
    return Array.from(mcpOrchestrator.getServerHealth().entries()).map(([id, health]) => ({
      server: id,
      ...health,
    }));
  }

  /**
   * Get wave execution status
   */
  getWaveStatus(): { 
    active: boolean; 
    currentWave: number; 
    currentPhase: string | null;
    checkpoints: number;
  } {
    return {
      active: this.sessionState.activeWaveStrategy !== undefined,
      currentWave: this.sessionState.currentWave || 0,
      currentPhase: waveEngine.getCurrentPhase(),
      checkpoints: waveEngine.getCheckpoints().length,
    };
  }

  /**
   * Rollback wave execution
   */
  async rollbackWave(toWave: number, reason: string): Promise<boolean> {
    if (!waveEngine.canRollback(toWave)) {
      console.error(`[SuperClaude] Cannot rollback to wave ${toWave}`);
      return false;
    }
    
    const rollback = await waveEngine.rollback(toWave, reason);
    
    if (rollback.success) {
      this.sessionState.currentWave = toWave;
      console.log(`[SuperClaude] Rolled back to wave ${toWave}`);
    }
    
    return rollback.success;
  }

  /**
   * Apply agent behaviors to enhance prompt
   */
  applyAgentBehaviors(personas: SuperClaudePersona[], prompt: string): AgentBehaviorResult {
    const result: AgentBehaviorResult = {
      enhancedPrompt: prompt,
      capabilities: [],
      tools: [],
      instructions: []
    };

    // Get agent instances for each persona
    personas.forEach(personaId => {
      const agent = agentRegistry.get(personaId);
      if (agent) {
        const enhancement = agent.enhancePrompt(prompt);
        
        // Aggregate capabilities
        result.capabilities.push(...enhancement.capabilities);
        
        // Aggregate preferred tools
        result.tools.push(...enhancement.preferredTools);
        
        // Add system instruction
        result.instructions.push(enhancement.systemInstruction);
        
        // Update prompt with agent context (only from first agent to avoid repetition)
        if (personaId === personas[0]) {
          result.enhancedPrompt = enhancement.prompt;
        }
      }
    });

    // Remove duplicates
    result.capabilities = [...new Set(result.capabilities)];
    result.tools = [...new Set(result.tools)];

    console.log('[SuperClaude] Applied agent behaviors:', {
      personas,
      capabilities: result.capabilities.length,
      tools: result.tools.length,
      instructions: result.instructions.length
    });

    return result;
  }
}

// Export singleton instance
export const superClaudeService = new SuperClaudeService();
/**
 * Scribe Agent
 * Professional documentation and localization specialist
 */

import { BaseAgent, AgentActivationContext, AgentResponse, AgentCollaboration } from './types';

export class ScribeAgent extends BaseAgent {
  id = 'scribe';
  name = 'Documentation Scribe';
  identity = 'Professional writer, documentation specialist, localization expert, cultural communication advisor';
  
  priorityHierarchy = [
    'Clarity > complexity',
    'Audience needs > completeness',
    'Cultural sensitivity > direct translation',
    'Accessibility > brevity',
    'Professional quality > speed'
  ];
  
  corePrinciples = [
    'Audience-First: All decisions prioritize reader understanding',
    'Cultural Sensitivity: Adapt content for cultural context',
    'Professional Excellence: Maintain high writing standards',
    'Clarity Above All: Simple, clear communication',
    'Accessibility: Documentation for all skill levels'
  ];
  
  contextEvaluation = {
    documentation: 100,
    writing: 95,
    localization: 90,
    communication: 95,
    culturalAdaptation: 85,
    technicalWriting: 90,
    contentStrategy: 85,
    userGuides: 90
  };
  
  mcpServerPreferences = {
    primary: ['context7', 'sequential'],
    secondary: [],
    avoided: ['magic'],
  };
  
  optimizedCommands = [
    '/document',
    '/explain',
    '/git',
    '/build --docs',
    '/write'
  ];
  
  autoActivationTriggers = [
    'document',
    'write',
    'readme',
    'guide',
    'wiki',
    'manual',
    'changelog',
    'release notes',
    'api docs',
    'tutorial'
  ];
  
  qualityStandards = [
    'Clarity: Crystal clear for target audience',
    'Completeness: 100% coverage of features',
    'Consistency: Uniform style and terminology',
    'Accessibility: Multiple learning styles supported',
    'Cultural Adaptation: Appropriate for target culture',
    'Professional: Publication-ready quality'
  ];
  
  // Language support capabilities - used for localization
  // @ts-ignore - Documentation object
  private languageSupport = {
    primary: ['en', 'es', 'fr', 'de', 'ja', 'zh'],
    secondary: ['pt', 'it', 'ru', 'ko', 'ar', 'hi'],
    capabilities: {
      translation: true,
      localization: true,
      culturalAdaptation: true,
      technicalTerminology: true
    }
  };
  
  // Documentation types and templates
  private documentationTypes = {
    technical: {
      structure: ['Overview', 'Architecture', 'API Reference', 'Examples'],
      audience: 'Developers and technical staff',
      style: 'Precise, technical, code-heavy'
    },
    user: {
      structure: ['Getting Started', 'Features', 'How-To', 'Troubleshooting'],
      audience: 'End users and non-technical staff',
      style: 'Friendly, step-by-step, visual'
    },
    api: {
      structure: ['Endpoints', 'Parameters', 'Responses', 'Examples', 'Errors'],
      audience: 'API consumers and integrators',
      style: 'Structured, comprehensive, example-rich'
    },
    wiki: {
      structure: ['Concepts', 'Procedures', 'Reference', 'FAQ'],
      audience: 'Mixed technical levels',
      style: 'Encyclopedic, cross-referenced, searchable'
    }
  };
  
  // Audience analysis framework
  private audienceFramework = {
    technical: {
      vocabulary: 'Technical terms acceptable',
      depth: 'Deep technical details',
      examples: 'Code snippets and configurations'
    },
    business: {
      vocabulary: 'Business-oriented language',
      depth: 'High-level concepts',
      examples: 'Use cases and ROI'
    },
    endUser: {
      vocabulary: 'Simple, jargon-free',
      depth: 'Task-focused',
      examples: 'Step-by-step screenshots'
    }
  };

  shouldActivate(context: AgentActivationContext): number {
    let score = 0;
    const input = context.keywords.join(' ').toLowerCase();
    
    // High activation for documentation keywords
    const docKeywords = ['document', 'write', 'readme', 'guide', 'manual', 'wiki'];
    if (docKeywords.some(k => input.includes(k))) {
      score += 0.8;
    }
    
    // Activation for content creation
    const contentKeywords = ['changelog', 'release', 'api doc', 'tutorial', 'howto'];
    if (contentKeywords.some(k => input.includes(k))) {
      score += 0.7;
    }
    
    // Activation for localization needs
    if (input.includes('translat') || input.includes('localiz') || input.includes('language')) {
      score += 0.6;
    }
    
    // Check domain match
    if (context.domain === 'documentation' || context.domain === 'writing') {
      score += 0.5;
    }
    
    // Activation for git documentation
    if (input.includes('commit') || input.includes('pr') || input.includes('release')) {
      score += 0.4;
    }
    
    return Math.min(score, 1.0);
  }

  analyze(input: string, context: AgentActivationContext): AgentResponse {
    const audience = this.identifyAudience(input, context);
    const docType = this.selectDocumentationType(input);
    // Language detection for future use
    this.detectLanguageRequirement(input);
    
    const analysis = this.performDocumentationAnalysis(input, context, audience, docType);
    const recommendations = this.generateDocumentationRecommendations(analysis);
    
    return {
      agent: this.id,
      analysis: analysis.summary,
      recommendations,
      confidence: this.shouldActivate(context)
    };
  }
  
  private identifyAudience(
    input: string,
    _context: AgentActivationContext
  ): any {
    if (input.includes('developer') || input.includes('api') || input.includes('technical')) {
      return this.audienceFramework.technical;
    }
    if (input.includes('business') || input.includes('stakeholder') || input.includes('executive')) {
      return this.audienceFramework.business;
    }
    return this.audienceFramework.endUser;
  }
  
  private selectDocumentationType(input: string): string {
    if (input.includes('api')) return 'api';
    if (input.includes('wiki')) return 'wiki';
    if (input.includes('user') || input.includes('guide')) return 'user';
    return 'technical';
  }
  
  private detectLanguageRequirement(input: string): string {
    // Simple language detection
    if (input.includes('spanish') || input.includes('español')) return 'es';
    if (input.includes('french') || input.includes('français')) return 'fr';
    if (input.includes('german') || input.includes('deutsch')) return 'de';
    if (input.includes('japanese') || input.includes('日本語')) return 'ja';
    if (input.includes('chinese') || input.includes('中文')) return 'zh';
    return 'en';
  }
  
  private performDocumentationAnalysis(
    input: string,
    context: AgentActivationContext,
    audience: any,
    docType: string
  ): any {
    const templates = this.documentationTypes as any;
    const template = templates[docType] || this.documentationTypes.technical;
    
    return {
      summary: `Documentation analysis: ${docType} documentation for ${audience.type || 'general'} audience`,
      structure: template.structure,
      contentGaps: this.identifyContentGaps(input, context),
      qualityMetrics: this.assessDocumentationQuality(context),
      localizationNeeds: this.assessLocalizationNeeds(input)
    };
  }
  
  private identifyContentGaps(input: string, _context: AgentActivationContext): string[] {
    const gaps = [];
    
    if (!input.includes('example')) {
      gaps.push('Missing code examples');
    }
    if (!input.includes('error') && !input.includes('troubleshoot')) {
      gaps.push('Missing troubleshooting section');
    }
    if (!input.includes('quick') && !input.includes('start')) {
      gaps.push('Missing quick start guide');
    }
    
    return gaps;
  }
  
  private assessDocumentationQuality(_context: AgentActivationContext): Record<string, number> {
    return {
      clarity: 0.8,
      completeness: 0.7,
      consistency: 0.85,
      accessibility: 0.75,
      professionalQuality: 0.9
    };
  }
  
  private assessLocalizationNeeds(input: string): string[] {
    const needs = [];
    
    if (input.includes('global') || input.includes('international')) {
      needs.push('Multi-language support required');
      needs.push('Cultural adaptation needed');
    }
    if (input.includes('technical')) {
      needs.push('Technical terminology localization');
    }
    
    return needs;
  }
  
  private generateDocumentationRecommendations(analysis: any): string[] {
    const recommendations = [];
    
    // Structure recommendations
    recommendations.push(`Structure documentation with: ${analysis.structure.join(', ')}`);
    
    // Gap recommendations
    for (const gap of analysis.contentGaps) {
      recommendations.push(`Add ${gap} to improve completeness`);
    }
    
    // Quality recommendations
    if (analysis.qualityMetrics.clarity < 0.8) {
      recommendations.push('Simplify language and improve clarity');
    }
    if (analysis.qualityMetrics.completeness < 0.8) {
      recommendations.push('Expand coverage to include all features');
    }
    
    // Localization recommendations
    if (analysis.localizationNeeds.length > 0) {
      recommendations.push('Implement localization strategy');
    }
    
    // Always include best practices
    recommendations.push('Include practical examples and use cases');
    recommendations.push('Maintain consistent terminology throughout');
    recommendations.push('Add visual aids where appropriate');
    
    return recommendations;
  }
  
  collaborate(otherAgents: string[]): AgentCollaboration {
    return {
      primary: this.id,
      consulting: otherAgents.filter(a => 
        ['mentor', 'technical-writer', 'architect'].includes(a)
      ),
      validation: ['ui-ux-reviewer'],
      handoffMechanisms: [
        'Documentation structure handoff',
        'Content review delegation',
        'Localization task distribution'
      ]
    };
  }
}
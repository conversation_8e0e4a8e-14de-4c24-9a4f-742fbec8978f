/**
 * Refactorer Agent
 * Code quality specialist focused on maintainability and technical debt reduction
 */

import { BaseAgent, AgentActivationContext, AgentResponse, AgentCollaboration } from './types';

export class RefactorerAgent extends BaseAgent {
  id = 'refactorer';
  name = 'Refactoring Specialist';
  identity = 'Code quality specialist, technical debt manager, clean code advocate';
  
  priorityHierarchy = [
    'Simplicity',
    'Maintainability',
    'Readability',
    'Performance',
    'Cleverness'
  ];
  
  corePrinciples = [
    'Simplicity First: Choose the simplest solution that works',
    'Maintainability: Code should be easy to understand and modify',
    'Technical Debt Management: Address debt systematically and proactively'
  ];
  
  contextEvaluation = {
    refactoring: 100,
    maintainability: 95,
    readability: 90,
    simplification: 90,
    patterns: 85,
    cleanup: 85,
  };
  
  mcpServerPreferences = {
    primary: ['sequential'], // For systematic analysis
    secondary: ['context7', 'morphllm'], // For patterns and best practices, and bulk transformations
    avoided: ['magic'], // Prefers refactoring over generation
  };
  
  optimizedCommands = [
    '/improve --quality',
    '/cleanup',
    '/refactor',
    '/analyze --quality',
  ];
  
  autoActivationTriggers = [
    'refactor',
    'cleanup',
    'technical debt',
    'simplify',
    'maintainability',
    'readability',
    'code smell',
    'duplicate',
    'complexity',
    'clean code',
  ];
  
  qualityStandards = [
    'Cyclomatic Complexity: <10 per function',
    'Cognitive Complexity: <15 per function',
    'Duplication: <3% total',
    'Test Coverage: ≥80%',
    'Documentation: 100% public APIs',
    'Dependencies: Minimal and well-maintained',
  ];
  
  codeQualityMetrics = {
    'Cyclomatic Complexity': '<10 per function',
    'Cognitive Complexity': '<15 per function',
    'Duplication': '<3% total',
    'Test Coverage': '≥80%',
    'Documentation': '100% public APIs',
    'Dependencies': 'Minimal and well-maintained',
  };

  shouldActivate(context: AgentActivationContext): number {
    let score = 0;
    const keywords = context.keywords.map(k => k.toLowerCase());
    
    // High activation for refactoring keywords
    const refactoringTerms = ['refactor', 'cleanup', 'simplify', 'improve', 'clean'];
    if (keywords.some(k => refactoringTerms.includes(k))) {
      score += 0.8;
    }
    
    // Activate for technical debt
    if (keywords.some(k => k.includes('debt') || k.includes('legacy') || k.includes('smell'))) {
      score += 0.7;
    }
    
    // Activate for quality issues
    if (keywords.some(k => k.includes('complex') || k.includes('duplicate') || k.includes('maintain'))) {
      score += 0.6;
    }
    
    // Context-based activation
    if (context.operationType === 'modification') score += 0.3;
    if (context.complexity > 0.6) score += 0.2;
    
    return Math.min(score, 1.0);
  }
  
  analyze(input: string, context: AgentActivationContext): AgentResponse {
    const analysis = `Refactoring analysis for: ${input}\nComplexity: ${context.complexity}\nFocus: ${context.domain || 'code quality'}`;
    const recommendations = [
      'Simplify complex methods',
      'Remove code duplication',
      'Improve naming conventions',
      'Apply SOLID principles'
    ];
    
    if (context.complexity > 0.7) {
      recommendations.push('Consider architectural refactoring');
    }
    
    return {
      agent: this.id,
      analysis,
      recommendations,
      confidence: this.shouldActivate(context)
    };
  }
  
  collaborate(otherAgents: string[]): AgentCollaboration {
    return {
      primary: this.id,
      consulting: otherAgents.filter(a => ['architect', 'qa', 'performance'].includes(a)),
      validation: ['qa', 'security'],
      handoffMechanisms: ['Code quality metrics', 'Refactoring recommendations']
    };
  }

  getDecisionFramework(context: string): string {
    return `
## Refactoring Framework

**Priority Hierarchy**: Simplicity > maintainability > readability > performance > cleverness

### Refactoring Principles:
1. **Boy Scout Rule**: Leave code better than you found it
2. **Small Steps**: Make incremental, safe changes
3. **Test Coverage**: Ensure tests before refactoring
4. **Preserve Behavior**: Maintain functionality

### Code Quality Metrics:
- Complexity: ${this.codeQualityMetrics['Cyclomatic Complexity']}
- Cognitive Load: ${this.codeQualityMetrics['Cognitive Complexity']}
- Duplication: ${this.codeQualityMetrics['Duplication']}
- Coverage: ${this.codeQualityMetrics['Test Coverage']}
- Documentation: ${this.codeQualityMetrics['Documentation']}

### Refactoring Patterns:
- **Extract Method**: Break down large functions
- **Replace Conditional**: Polymorphism over conditionals
- **Remove Duplication**: DRY principle
- **Simplify Expressions**: Clear over clever
- **Organize Data**: Proper encapsulation

Context: ${context}
`;
  }

  analyzeCode(_code: string): string {
    return `
## Code Quality Analysis

### Code Smells Detected:
- Long methods requiring extraction
- Duplicate code patterns identified
- Complex conditionals needing simplification
- Poor naming conventions found

### Refactoring Opportunities:
1. **Immediate**: Quick wins for readability
2. **Structural**: Design pattern applications
3. **Architectural**: Module reorganization

### Technical Debt Assessment:
- Current debt level and impact
- Priority areas for improvement
- Estimated effort for cleanup
- Risk vs. reward analysis
`;
  }

  getCapabilities(): string[] {
    return [
      'Code quality analysis and improvement',
      'Technical debt identification and reduction',
      'Design pattern implementation',
      'Code simplification and cleanup',
      'Duplicate code elimination',
      'Complexity reduction',
      'Maintainability enhancement',
      'Refactoring strategy planning'
    ];
  }

  getPreferredTools(): string[] {
    return [
      'Sequential for systematic refactoring',
      'Context7 for design patterns',
      'MultiEdit for bulk changes',
      'Grep for code smell detection',
      'TodoWrite for refactoring tracking'
    ];
  }

  getSystemInstruction(): string {
    return `You are operating as the Refactoring Specialist agent with the following focus:
    
Priority: ${this.priorityHierarchy.join(' > ')}

Core Principles:
${this.corePrinciples.map(p => `- ${p}`).join('\n')}

Code Quality Metrics:
${Object.entries(this.codeQualityMetrics).map(([metric, target]) => `- ${metric}: ${target}`).join('\n')}

Quality Standards:
${this.qualityStandards.map(s => `- ${s}`).join('\n')}

When refactoring code:
1. Prioritize simplicity and readability over cleverness
2. Reduce complexity systematically
3. Eliminate duplication through abstraction
4. Apply appropriate design patterns
5. Maintain backward compatibility when possible
6. Write tests before refactoring critical code
7. Document significant architectural changes

Focus on creating clean, maintainable code that other developers can easily understand and modify.`;
  }

  // Properties required by BaseAgent
  specialization = 'Code quality and refactoring';
  focusAreas = ['Refactoring', 'Code Quality', 'Technical Debt', 'Maintainability'];
}
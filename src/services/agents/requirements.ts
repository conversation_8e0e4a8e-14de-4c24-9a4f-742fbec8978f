/**
 * Requirements Agent
 * Requirements discovery and specification specialist
 */

import { BaseAgent, AgentActivationContext, AgentResponse, AgentCollaboration } from './types';

export class RequirementsAgent extends BaseAgent {
  id = 'requirements';
  name = 'Requirements Analyst';
  identity = 'Requirements discovery specialist, specification expert, stakeholder liaison';
  
  priorityHierarchy = [
    'Clarity > assumptions',
    'Completeness > speed',
    'Feasibility > wishful thinking',
    'Traceability > isolation',
    'Validation > speculation'
  ];
  
  corePrinciples = [
    'Discovery First: Uncover hidden requirements through systematic exploration',
    'Stakeholder Alignment: Balance competing needs and priorities',
    'Specification Excellence: Create clear, testable requirements',
    'Traceability: Link requirements to implementation and testing',
    'Continuous Validation: Verify requirements throughout lifecycle'
  ];
  
  contextEvaluation = {
    requirements: 100,
    analysis: 95,
    specification: 90,
    validation: 85,
    discovery: 90,
    prioritization: 85,
    feasibility: 80,
    stakeholderManagement: 85
  };
  
  mcpServerPreferences = {
    primary: ['sequential', 'context7'],
    secondary: [],
    avoided: ['magic', 'playwright'],
  };
  
  optimizedCommands = [
    '/analyze --requirements',
    '/document --spec',
    '/validate',
    '/estimate',
    '/prioritize'
  ];
  
  autoActivationTriggers = [
    'requirement',
    'specification',
    'user story',
    'criteria',
    'scope',
    'feature',
    'epic',
    'acceptance',
    'constraint',
    'assumption'
  ];
  
  qualityStandards = [
    'Completeness: All aspects covered',
    'Clarity: Unambiguous specifications',
    'Consistency: No conflicting requirements',
    'Testability: Measurable acceptance criteria',
    'Feasibility: Technically achievable',
    'Priority: Value-driven ordering'
  ];
  
  // Requirements discovery framework - used for analysis
  // @ts-ignore - Documentation object
  private discoveryFramework = {
    functional: {
      questions: ['What must the system do?', 'What are the user workflows?'],
      artifacts: ['User stories', 'Use cases', 'Feature specifications']
    },
    nonFunctional: {
      questions: ['How well must it perform?', 'What quality attributes?'],
      artifacts: ['Performance criteria', 'Security requirements', 'Scalability needs']
    },
    constraints: {
      questions: ['What limitations exist?', 'What dependencies?'],
      artifacts: ['Technical constraints', 'Business constraints', 'Regulatory requirements']
    },
    assumptions: {
      questions: ['What are we assuming?', 'What needs validation?'],
      artifacts: ['Assumption log', 'Risk register', 'Validation plan']
    }
  };
  
  // Specification templates
  private specificationTemplates = {
    userStory: {
      format: 'As a [role], I want [feature] so that [benefit]',
      components: ['Actor', 'Action', 'Outcome'],
      criteria: ['Independent', 'Negotiable', 'Valuable', 'Estimable', 'Small', 'Testable']
    },
    useCase: {
      format: 'Title, Actor, Preconditions, Steps, Postconditions',
      components: ['Main flow', 'Alternative flows', 'Exception flows'],
      criteria: ['Complete', 'Consistent', 'Correct', 'Unambiguous']
    },
    requirement: {
      format: 'ID, Title, Description, Priority, Acceptance Criteria',
      components: ['Functional', 'Non-functional', 'Constraints'],
      criteria: ['Atomic', 'Traceable', 'Consistent', 'Verifiable']
    }
  };
  
  // Priority matrix
  private priorityMatrix = {
    mustHave: { value: 100, description: 'Critical for launch' },
    shouldHave: { value: 70, description: 'Important but not critical' },
    couldHave: { value: 40, description: 'Desirable if time permits' },
    wontHave: { value: 10, description: 'Future consideration' }
  };

  shouldActivate(context: AgentActivationContext): number {
    let score = 0;
    const input = context.keywords.join(' ').toLowerCase();
    
    // High activation for requirements keywords
    const reqKeywords = ['requirement', 'specification', 'scope', 'feature', 'user story'];
    if (reqKeywords.some(k => input.includes(k))) {
      score += 0.8;
    }
    
    // Activation for discovery and analysis
    if (input.includes('discover') || input.includes('elicit') || input.includes('gather')) {
      score += 0.7;
    }
    
    // Activation for validation and criteria
    const validationKeywords = ['acceptance', 'criteria', 'validate', 'verify'];
    if (validationKeywords.some(k => input.includes(k))) {
      score += 0.6;
    }
    
    // Check domain match
    if (context.domain === 'requirements' || context.domain === 'analysis') {
      score += 0.5;
    }
    
    // Activation for prioritization
    if (input.includes('priorit') || input.includes('must have') || input.includes('should have')) {
      score += 0.4;
    }
    
    return Math.min(score, 1.0);
  }

  analyze(input: string, context: AgentActivationContext): AgentResponse {
    const requirementType = this.identifyRequirementType(input);
    const priority = this.assessPriority(input, context);
    // Template selection for future use
    this.selectTemplate(requirementType);
    
    const analysis = this.performRequirementsAnalysis(input, context, requirementType, priority);
    const recommendations = this.generateRequirementsRecommendations(analysis);
    
    return {
      agent: this.id,
      analysis: analysis.summary,
      recommendations,
      confidence: this.shouldActivate(context)
    };
  }
  
  private identifyRequirementType(input: string): string {
    if (input.includes('user story') || input.includes('as a')) {
      return 'userStory';
    }
    if (input.includes('use case') || input.includes('scenario')) {
      return 'useCase';
    }
    if (input.includes('performance') || input.includes('security') || input.includes('scalability')) {
      return 'nonFunctional';
    }
    return 'functional';
  }
  
  private assessPriority(input: string, _context: AgentActivationContext): any {
    if (input.includes('critical') || input.includes('must have') || input.includes('essential')) {
      return { level: 'mustHave', value: this.priorityMatrix.mustHave.value };
    }
    if (input.includes('important') || input.includes('should have')) {
      return { level: 'shouldHave', value: this.priorityMatrix.shouldHave.value };
    }
    if (input.includes('nice to have') || input.includes('could have')) {
      return { level: 'couldHave', value: this.priorityMatrix.couldHave.value };
    }
    return { level: 'shouldHave', value: this.priorityMatrix.shouldHave.value };
  }
  
  private selectTemplate(requirementType: string): any {
    switch (requirementType) {
      case 'userStory':
        return this.specificationTemplates.userStory;
      case 'useCase':
        return this.specificationTemplates.useCase;
      default:
        return this.specificationTemplates.requirement;
    }
  }
  
  private performRequirementsAnalysis(
    input: string,
    context: AgentActivationContext,
    requirementType: string,
    priority: any
  ): any {
    return {
      summary: `Requirements analysis: ${requirementType} with ${priority.level} priority`,
      completeness: this.assessCompleteness(input, requirementType),
      clarity: this.assessClarity(input),
      feasibility: this.assessFeasibility(input, context),
      risks: this.identifyRisks(input, context),
      dependencies: this.identifyDependencies(input),
      acceptanceCriteria: this.generateAcceptanceCriteria(input, requirementType)
    };
  }
  
  private assessCompleteness(input: string, requirementType: string): number {
    let score = 0.5; // Base score
    
    // Check for key components based on type
    if (requirementType === 'userStory') {
      if (input.includes('as a')) score += 0.15;
      if (input.includes('i want')) score += 0.15;
      if (input.includes('so that')) score += 0.2;
    }
    
    // Check for acceptance criteria
    if (input.includes('accept') || input.includes('criteria')) {
      score += 0.2;
    }
    
    // Check for context
    if (input.includes('given') || input.includes('when') || input.includes('then')) {
      score += 0.15;
    }
    
    return Math.min(score, 1.0);
  }
  
  private assessClarity(input: string): number {
    let score = 0.7; // Base clarity
    
    // Penalize ambiguous terms
    const ambiguousTerms = ['maybe', 'possibly', 'might', 'could', 'sometime'];
    for (const term of ambiguousTerms) {
      if (input.includes(term)) score -= 0.1;
    }
    
    // Reward specific terms
    const specificTerms = ['must', 'shall', 'will', 'exactly', 'specifically'];
    for (const term of specificTerms) {
      if (input.includes(term)) score += 0.05;
    }
    
    return Math.max(0, Math.min(score, 1.0));
  }
  
  private assessFeasibility(input: string, context: AgentActivationContext): string {
    if (context.complexity && context.complexity > 0.8) {
      return 'challenging';
    }
    if (input.includes('complex') || input.includes('difficult')) {
      return 'moderate';
    }
    return 'feasible';
  }
  
  private identifyRisks(input: string, context: AgentActivationContext): string[] {
    const risks = [];
    
    if (input.includes('integration') || input.includes('third-party')) {
      risks.push('External dependency risk');
    }
    if (input.includes('performance') || input.includes('scale')) {
      risks.push('Performance risk');
    }
    if (input.includes('security') || input.includes('authentication')) {
      risks.push('Security risk');
    }
    if (context.complexity && context.complexity > 0.7) {
      risks.push('Complexity risk');
    }
    
    return risks;
  }
  
  private identifyDependencies(input: string): string[] {
    const dependencies = [];
    
    if (input.includes('after') || input.includes('depends on')) {
      dependencies.push('Sequential dependency');
    }
    if (input.includes('api') || input.includes('service')) {
      dependencies.push('External service dependency');
    }
    if (input.includes('database') || input.includes('data')) {
      dependencies.push('Data dependency');
    }
    
    return dependencies;
  }
  
  private generateAcceptanceCriteria(_input: string, requirementType: string): string[] {
    const criteria = [];
    
    // Add type-specific criteria
    if (requirementType === 'userStory') {
      criteria.push('User can successfully complete the described action');
      criteria.push('Expected outcome is achieved');
      criteria.push('Edge cases are handled gracefully');
    } else if (requirementType === 'nonFunctional') {
      criteria.push('Performance metrics are met');
      criteria.push('Quality attributes are satisfied');
      criteria.push('Constraints are respected');
    } else {
      criteria.push('Functionality works as specified');
      criteria.push('All test cases pass');
      criteria.push('No regression in existing features');
    }
    
    return criteria;
  }
  
  private generateRequirementsRecommendations(analysis: any): string[] {
    const recommendations = [];
    
    // Completeness recommendations
    if (analysis.completeness < 0.8) {
      recommendations.push('Add missing requirement components for completeness');
    }
    
    // Clarity recommendations
    if (analysis.clarity < 0.8) {
      recommendations.push('Remove ambiguous language and be more specific');
    }
    
    // Feasibility recommendations
    if (analysis.feasibility === 'challenging') {
      recommendations.push('Consider breaking into smaller, manageable requirements');
    }
    
    // Risk recommendations
    for (const risk of analysis.risks) {
      recommendations.push(`Mitigate ${risk} through planning and validation`);
    }
    
    // Always include best practices
    recommendations.push('Define clear acceptance criteria');
    recommendations.push('Ensure traceability to implementation');
    recommendations.push('Validate with stakeholders');
    
    return recommendations;
  }
  
  collaborate(otherAgents: string[]): AgentCollaboration {
    return {
      primary: this.id,
      consulting: otherAgents.filter(a => 
        ['architect', 'analyzer', 'qa'].includes(a)
      ),
      validation: ['qa', 'frontend', 'backend'],
      handoffMechanisms: [
        'Requirements specification handoff',
        'Acceptance criteria delegation',
        'Priority matrix distribution'
      ]
    };
  }

  getCapabilities(): string[] {
    return [
      'Requirements discovery and elicitation',
      'Specification writing and documentation',
      'Stakeholder analysis and management',
      'User story creation and refinement',
      'Acceptance criteria definition',
      'Requirements prioritization and scoping',
      'Feasibility assessment and validation',
      'Traceability matrix management'
    ];
  }

  getPreferredTools(): string[] {
    return [
      'Sequential for requirements analysis',
      'Context7 for requirement patterns',
      'Write for specification documents',
      'TodoWrite for requirement tracking',
      'Read for document review'
    ];
  }

  getSystemInstruction(): string {
    return `You are operating as the Requirements Analyst agent with the following focus:
    
Priority: ${this.priorityHierarchy.join(' > ')}

Core Principles:
${this.corePrinciples.map(p => `- ${p}`).join('\n')}

Discovery Framework:
- Functional: What the system must do
- Non-functional: Quality attributes and performance
- Constraints: Technical and business limitations
- Assumptions: What needs validation

Quality Standards:
${this.qualityStandards.map(s => `- ${s}`).join('\n')}

When analyzing requirements:
1. Discover hidden requirements through systematic questioning
2. Balance stakeholder needs and priorities
3. Create clear, testable specifications
4. Define measurable acceptance criteria
5. Assess technical feasibility and risks
6. Maintain traceability throughout lifecycle
7. Validate requirements with stakeholders

Focus on creating complete, clear, and feasible requirements that drive successful implementation.`;
  }

  // Properties required by BaseAgent
  specialization = 'Requirements discovery and specification';
  focusAreas = ['Requirements', 'Analysis', 'Specification', 'Validation'];
}
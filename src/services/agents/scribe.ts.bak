/**
 * Scribe Agent
 * Professional documentation specialist with localization expertise
 */

import { BaseAgent, AgentActivationContext, AgentResponse, AgentCollaboration } from './types';

export class ScribeAgent extends BaseAgent {
  constructor() {
    super(
      'scribe',
      'Documentation Scribe',
      'Professional writer, documentation specialist, localization expert',
      ['clarity-first', 'audience-awareness', 'cultural-sensitivity', 'professional-excellence']
    );
  }

  get contextEvaluation() {
    return {
      documentation: 100,
      writing: 95,
      localization: 90,
      communication: 90,
      organization: 85,
      translation: 80,
    };
  }

  get mcpPreferences() {
    return {
      primary: ['context7'], // For documentation patterns
      secondary: ['sequential'], // For structured writing
      avoided: ['magic'], // Prefers writing over generation
    };
  }

  get optimizedCommands() {
    return [
      '/document',
      '/explain',
      '/git',
      '/build --docs',
    ];
  }

  get autoActivationTriggers() {
    return [
      'document',
      'write',
      'readme',
      'guide',
      'wiki',
      'changelog',
      'release notes',
      'api docs',
      'localize',
      'translate',
      'commit message',
    ];
  }

  get qualityStandards() {
    return {
      'Clarity': 'Crystal clear for target audience',
      'Completeness': '100% coverage of features',
      'Consistency': 'Uniform style and terminology',
      'Accessibility': 'Multiple learning styles supported',
      'Localization': 'Culturally appropriate content',
      'Maintenance': 'Easy to update and extend',
    };
  }

  get supportedLanguages() {
    return ['en', 'es', 'fr', 'de', 'ja', 'zh', 'pt', 'it', 'ru', 'ko'];
  }

  shouldActivate(context: AgentActivationContext): number {
    let score = 0;
    const keywords = context.keywords.map(k => k.toLowerCase());
    
    // High activation for documentation keywords
    const docTerms = ['document', 'write', 'readme', 'guide', 'wiki'];
    if (keywords.some(k => docTerms.includes(k))) {
      score += 0.8;
    }
    
    // Activate for commit messages and releases
    if (keywords.some(k => k.includes('commit') || k.includes('release') || k.includes('changelog'))) {
      score += 0.7;
    }
    
    // Activate for localization
    if (keywords.some(k => k.includes('localiz') || k.includes('translat') || k.includes('i18n'))) {
      score += 0.6;
    }
    
    // Context-based activation
    if (context.domain === 'documentation') score += 0.5;
    if (context.operationType === 'creation') score += 0.2;
    
    return Math.min(score, 1.0);
  }

  getDecisionFramework(context: string): string {
    return `
## Documentation Framework

**Priority Hierarchy**: Clarity > audience needs > cultural sensitivity > completeness > brevity

### Writing Principles:
1. **Audience-First**: Tailor to reader's expertise level
2. **Progressive Disclosure**: Layer information appropriately
3. **Cultural Adaptation**: Respect local conventions
4. **Visual Support**: Include diagrams and examples

### Documentation Standards:
- Clarity: ${this.qualityStandards['Clarity']}
- Coverage: ${this.qualityStandards['Completeness']}
- Style: ${this.qualityStandards['Consistency']}
- Access: ${this.qualityStandards['Accessibility']}
- i18n: ${this.qualityStandards['Localization']}

### Content Types:
- **Technical Docs**: API references, architecture guides
- **User Guides**: Tutorials, how-tos, FAQs
- **Development**: README, CONTRIBUTING, CODE_OF_CONDUCT
- **Release**: Changelogs, release notes, migration guides
- **Localization**: Multi-language support

Supported Languages: ${this.supportedLanguages.join(', ')}

Context: ${context}
`;
  }

  analyzeCode(code: string): string {
    return `
## Documentation Analysis

### Documentation Gaps:
- Missing API documentation
- Undocumented complex logic
- Absent usage examples
- Incomplete README sections

### Content Structure:
1. **Overview**: System purpose and architecture
2. **Getting Started**: Quick setup guide
3. **API Reference**: Complete interface docs
4. **Examples**: Common use cases
5. **Troubleshooting**: Common issues

### Localization Needs:
- User-facing strings for i18n
- Cultural adaptations required
- Translation priorities
- Regional variations
`;
  }
}
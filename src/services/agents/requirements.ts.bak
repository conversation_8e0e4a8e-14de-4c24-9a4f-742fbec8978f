/**
 * Requirements Analyst Agent
 * Specialist in requirements discovery and specification
 */

import { BaseAgent, AgentActivationContext, AgentResponse, AgentCollaboration } from './types';

export class RequirementsAgent extends BaseAgent {
  constructor() {
    super(
      'requirements',
      'Requirements Analyst',
      'Requirements discovery specialist, specification expert, stakeholder communicator',
      ['clarity-seeking', 'completeness-focus', 'stakeholder-alignment', 'traceability']
    );
  }

  get contextEvaluation() {
    return {
      requirements: 100,
      analysis: 95,
      specification: 90,
      validation: 85,
      stakeholder_management: 85,
      documentation: 80,
    };
  }

  get mcpPreferences() {
    return {
      primary: ['sequential'], // For systematic analysis
      secondary: ['context7'], // For requirement patterns
      avoided: ['magic', 'playwright'], // Focus on analysis over implementation
    };
  }

  get optimizedCommands() {
    return [
      '/analyze --requirements',
      '/document --spec',
      '/validate --requirements',
      '/estimate',
    ];
  }

  get autoActivationTriggers() {
    return [
      'requirement',
      'specification',
      'user story',
      'acceptance criteria',
      'use case',
      'functional',
      'non-functional',
      'constraint',
      'assumption',
      'dependency',
      'scope',
    ];
  }

  get qualityStandards() {
    return {
      'Completeness': 'All aspects covered',
      'Clarity': 'Unambiguous specifications',
      'Consistency': 'No conflicting requirements',
      'Testability': 'Measurable acceptance criteria',
      'Traceability': 'Requirements to implementation mapping',
      'Feasibility': 'Technically and economically viable',
    };
  }

  shouldActivate(context: AgentActivationContext): number {
    let score = 0;
    const keywords = context.keywords.map(k => k.toLowerCase());
    
    // High activation for requirements keywords
    const reqTerms = ['requirement', 'specification', 'story', 'criteria', 'scope'];
    if (keywords.some(k => reqTerms.includes(k))) {
      score += 0.8;
    }
    
    // Activate for analysis and validation
    if (keywords.some(k => k.includes('validat') || k.includes('analyz') || k.includes('discover'))) {
      score += 0.6;
    }
    
    // Activate for constraints and dependencies
    if (keywords.some(k => k.includes('constraint') || k.includes('depend') || k.includes('assumption'))) {
      score += 0.5;
    }
    
    // Context-based activation
    if (context.operationType === 'analysis') score += 0.3;
    if (context.complexity > 0.7) score += 0.2;
    
    return Math.min(score, 1.0);
  }

  getDecisionFramework(context: string): string {
    return `
## Requirements Analysis Framework

**Priority Hierarchy**: Clarity > completeness > feasibility > traceability

### Requirements Discovery Process:
1. **Stakeholder Identification**: Who needs what and why
2. **Elicitation**: Gather needs through various techniques
3. **Analysis**: Decompose and prioritize requirements
4. **Specification**: Document in clear, testable format
5. **Validation**: Ensure correctness and completeness

### Quality Criteria:
- Completeness: ${this.qualityStandards['Completeness']}
- Clarity: ${this.qualityStandards['Clarity']}
- Consistency: ${this.qualityStandards['Consistency']}
- Testability: ${this.qualityStandards['Testability']}
- Traceability: ${this.qualityStandards['Traceability']}

### Requirement Types:
- **Functional**: What the system must do
- **Non-Functional**: How well it must do it
- **Constraints**: Limitations and boundaries
- **Assumptions**: Foundational beliefs
- **Dependencies**: External requirements

Context: ${context}
`;
  }

  analyzeCode(code: string): string {
    return `
## Requirements Analysis

### Identified Requirements:
- Functional requirements extracted
- Non-functional requirements detected
- Implicit requirements discovered
- Missing requirements highlighted

### Validation Results:
1. **Coverage**: Requirements to implementation mapping
2. **Gaps**: Unimplemented requirements
3. **Conflicts**: Contradictory specifications
4. **Risks**: High-complexity requirements

### Recommendations:
- Priority clarifications needed
- Ambiguous specifications to resolve
- Missing acceptance criteria
- Stakeholder validation required
`;
  }
}
/**
 * Backend Architect Agent
 * Reliability engineer, API specialist, data integrity focus
 */

import { BaseAgent, AgentActivationContext, AgentResponse, AgentCollaboration } from './types';

export class BackendAgent extends BaseAgent {
  id = 'backend';
  name = 'Backend Architect';
  identity = 'Reliability engineer, API specialist, data integrity focus';
  
  priorityHierarchy = [
    'Reliability',
    'Security',
    'Performance',
    'Features',
    'Convenience'
  ];
  
  corePrinciples = [
    'Reliability First: Systems must be fault-tolerant and recoverable',
    'Security by Default: Implement defense in depth and zero trust',
    'Data Integrity: Ensure consistency and accuracy across all operations'
  ];
  
  contextEvaluation = {
    'backend': 100,
    'api': 95,
    'database': 90,
    'server': 95,
    'service': 90,
    'reliability': 100,
    'infrastructure': 85
  };
  
  reliabilityBudgets = {
    uptime: '99.9% (8.7h/year downtime)',
    errorRate: '<0.1% for critical operations',
    responseTime: '<200ms for API calls',
    recoveryTime: '<5 minutes for critical services'
  };
  
  mcpServerPreferences = {
    primary: ['context7', 'serena'],
    secondary: ['sequential', 'morphllm'],
    avoided: ['magic']
  };
  
  optimizedCommands = [
    '/build --api',
    '/git',
    '/implement --type api',
    '/analyze --focus backend',
    '/improve --reliability'
  ];
  
  autoActivationTriggers = [
    'api',
    'database',
    'service',
    'reliability',
    'backend',
    'server',
    'endpoint',
    'microservice',
    'data integrity',
    'authentication',
    'authorization'
  ];
  
  qualityStandards = [
    'Reliability: 99.9% uptime with graceful degradation',
    'Security: Defense in depth with zero trust architecture',
    'Data Integrity: ACID compliance and consistency guarantees'
  ];

  analyze(input: string, _context: AgentActivationContext): AgentResponse {
    const response: AgentResponse = {
      agent: this.id,
      analysis: '',
      recommendations: [],
      confidence: 0.90
    };

    // API design analysis
    if (input.includes('api') || input.includes('endpoint')) {
      response.analysis = 'API design analysis reveals RESTful optimization opportunities';
      response.recommendations.push(
        'Implement proper HTTP status codes',
        'Add comprehensive error handling',
        'Use versioning strategy',
        'Implement rate limiting'
      );
    }

    // Database optimization
    if (input.includes('database') || input.includes('query')) {
      response.analysis += '\nDatabase performance analysis indicates optimization needs';
      response.recommendations.push(
        'Optimize query performance',
        'Implement proper indexing',
        'Add connection pooling',
        'Consider caching strategy'
      );
      response.collaborationNeeded = ['performance'];
    }

    // Reliability assessment
    if (input.includes('reliability') || input.includes('fault')) {
      response.analysis += '\nReliability assessment shows resilience improvements needed';
      response.recommendations.push(
        'Implement circuit breakers',
        'Add retry mechanisms',
        'Use message queues for async operations',
        'Implement health checks'
      );
    }

    // Security analysis
    if (input.includes('security') || input.includes('auth')) {
      response.analysis += '\nSecurity analysis reveals authentication/authorization needs';
      response.recommendations.push(
        'Implement JWT authentication',
        'Add role-based access control',
        'Use environment variables for secrets',
        'Implement audit logging'
      );
      response.collaborationNeeded = ['security'];
    }

    return response;
  }

  collaborate(otherAgents: string[]): AgentCollaboration {
    return {
      primary: this.id,
      consulting: otherAgents.filter(agent => 
        ['architect', 'security', 'performance', 'devops'].includes(agent)
      ),
      validation: ['qa', 'security'],
      handoffMechanisms: [
        'API documentation and contracts',
        'Database schema specifications',
        'Service dependencies mapping',
        'Security requirements'
      ]
    };
  }
}
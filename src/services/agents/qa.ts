/**
 * QA Agent
 * Quality assurance specialist focused on testing, validation, and edge cases
 */

import { BaseAgent, AgentActivationContext, AgentResponse, AgentCollaboration } from './types';

export class QAAgent extends BaseAgent {
  id = 'qa';
  name = 'Quality Assurance Engineer';
  identity = 'Quality advocate, testing specialist, edge case detective';
  
  priorityHierarchy = [
    'Prevention',
    'Detection',
    'Correction',
    'Comprehensive coverage'
  ];
  
  corePrinciples = [
    'Prevention Focus: Build quality in rather than testing it in',
    'Comprehensive Coverage: Test all scenarios including edge cases',
    'Risk-Based Testing: Prioritize testing based on risk and impact'
  ];
  
  contextEvaluation = {
    testing: 100,
    validation: 95,
    quality: 90,
    coverage: 90,
    automation: 85,
    debugging: 80,
  };
  
  mcpServerPreferences = {
    primary: ['playwright'], // For E2E testing
    secondary: ['sequential'], // For test planning
    avoided: ['magic'], // Prefers testing over generation
  };
  
  optimizedCommands = [
    '/test',
    '/troubleshoot',
    '/analyze --focus quality',
    '/validate',
  ];
  
  autoActivationTriggers = [
    'test',
    'quality',
    'validation',
    'edge case',
    'coverage',
    'regression',
    'e2e',
    'unit test',
    'integration',
    'bug',
    'defect',
  ];
  
  qualityStandards = [
    'Unit Test Coverage: ≥80%',
    'Integration Coverage: ≥70%',
    'E2E Coverage: Critical paths 100%',
    'Code Quality: Zero critical issues',
    'Performance: No regressions >10%',
    'Accessibility: WCAG 2.1 AA compliance',
  ];
  
  testingStandards = {
    'Unit Test Coverage': '≥80%',
    'Integration Coverage': '≥70%',
    'E2E Coverage': 'Critical paths 100%',
    'Code Quality': 'Zero critical issues',
    'Performance': 'No regressions >10%',
    'Accessibility': 'WCAG 2.1 AA compliance',
  };

  shouldActivate(context: AgentActivationContext): number {
    let score = 0;
    const keywords = context.keywords.map(k => k.toLowerCase());
    
    // High activation for testing keywords
    const testingTerms = ['test', 'quality', 'qa', 'validation', 'coverage'];
    if (keywords.some(k => testingTerms.includes(k))) {
      score += 0.8;
    }
    
    // Activate for bug-related work
    if (keywords.some(k => k.includes('bug') || k.includes('defect') || k.includes('issue'))) {
      score += 0.7;
    }
    
    // Activate for edge cases
    if (keywords.some(k => k.includes('edge') || k.includes('corner') || k.includes('boundary'))) {
      score += 0.6;
    }
    
    // Context-based activation
    if (context.errors) score += 0.4;
    if (context.complexity > 0.6) score += 0.2;
    
    return Math.min(score, 1.0);
  }

  analyze(input: string, context: AgentActivationContext): AgentResponse {
    const analysis = `QA analysis for: ${input}\nFocus: ${context.domain || 'general testing'}`;
    const recommendations = [
      'Implement comprehensive test coverage',
      'Focus on critical user paths',
      'Add edge case testing'
    ];
    
    return {
      agent: this.id,
      analysis,
      recommendations,
      confidence: this.shouldActivate(context)
    };
  }
  
  collaborate(otherAgents: string[]): AgentCollaboration {
    return {
      primary: this.id,
      consulting: otherAgents.filter(a => ['frontend', 'backend', 'performance'].includes(a)),
      validation: ['security'],
      handoffMechanisms: ['Test results handoff', 'Quality metrics sharing']
    };
  }
  
  getDecisionFramework(context: string): string {
    return `
## Quality Assurance Framework

**Priority Hierarchy**: Prevention > detection > correction > comprehensive coverage

### Testing Strategy:
1. **Risk Assessment**: Identify critical paths and high-risk areas
2. **Test Planning**: Design comprehensive test scenarios
3. **Edge Case Analysis**: Identify boundary conditions
4. **Automation Priority**: Automate repetitive and critical tests

### Quality Standards:
- Unit Coverage: ${this.testingStandards['Unit Test Coverage']}
- Integration: ${this.testingStandards['Integration Coverage']}
- E2E: ${this.testingStandards['E2E Coverage']}
- Code Quality: ${this.testingStandards['Code Quality']}
- Performance: ${this.testingStandards['Performance']}
- Accessibility: ${this.testingStandards['Accessibility']}

### Testing Pyramid:
- **Unit Tests**: Fast, isolated, high coverage
- **Integration Tests**: Component interactions
- **E2E Tests**: Critical user journeys
- **Manual Testing**: Exploratory and usability

Context: ${context}
`;
  }

  analyzeCode(_code: string): string {
    return `
## Quality Analysis

### Test Coverage Assessment:
- Current coverage levels identified
- Uncovered critical paths detected
- Edge cases requiring attention

### Risk Areas:
1. **High Risk**: Critical business logic
2. **Medium Risk**: User-facing features
3. **Low Risk**: Utility functions

### Testing Recommendations:
- Priority test scenarios
- Automation opportunities
- Performance test requirements
- Security test considerations
`;
  }
}
/**
 * Python Expert Agent
 * Specialist in Python development with focus on best practices and performance
 */

import { BaseAgent, AgentActivationContext, AgentResponse, AgentCollaboration } from './types';

export class PythonExpertAgent extends BaseAgent {
  constructor() {
    super(
      'python-expert',
      'Python Expert',
      'Python specialist, async expert, data processing guru, ML/AI practitioner',
      ['pythonic-code', 'performance-conscious', 'type-safety', 'testing-first']
    );
  }

  get contextEvaluation() {
    return {
      python: 100,
      async_programming: 95,
      data_processing: 90,
      testing: 90,
      ml_ai: 85,
      web_frameworks: 85,
    };
  }

  get mcpPreferences() {
    return {
      primary: ['context7'], // For Python patterns
      secondary: ['sequential'], // For complex Python logic
      avoided: ['magic'], // UI generation not relevant
    };
  }

  get optimizedCommands() {
    return [
      '/build --python',
      '/analyze --python',
      '/improve --pythonic',
      '/test --pytest',
    ];
  }

  get autoActivationTriggers() {
    return [
      'python',
      'django',
      'flask',
      'fastapi',
      'pandas',
      'numpy',
      'asyncio',
      'pytest',
      'mypy',
      'poetry',
      'pip',
      'virtualenv',
    ];
  }

  get qualityStandards() {
    return {
      'PEP 8': 'Full compliance',
      'Type Hints': 'Complete coverage',
      'Test Coverage': '≥90%',
      'Async Safety': 'No blocking in async',
      'Performance': 'Optimized algorithms',
      'Security': 'OWASP compliance',
    };
  }

  shouldActivate(context: AgentActivationContext): number {
    let score = 0;
    const keywords = context.keywords.map(k => k.toLowerCase());
    
    // High activation for Python keywords
    const pythonTerms = ['python', 'py', 'django', 'flask', 'fastapi'];
    if (keywords.some(k => pythonTerms.includes(k))) {
      score += 0.9;
    }
    
    // Activate for Python libraries
    if (keywords.some(k => ['pandas', 'numpy', 'scipy', 'sklearn'].includes(k))) {
      score += 0.7;
    }
    
    // Activate for Python tools
    if (keywords.some(k => ['pytest', 'mypy', 'poetry', 'pip'].includes(k))) {
      score += 0.6;
    }
    
    // Context-based activation
    if (context.domain === 'backend' && keywords.some(k => k.includes('.py'))) score += 0.5;
    
    return Math.min(score, 1.0);
  }

  getDecisionFramework(context: string): string {
    return `
## Python Development Framework

**Priority Hierarchy**: Pythonic code > readability > performance > cleverness

### Python Principles:
1. **Zen of Python**: Beautiful, explicit, simple
2. **Type Safety**: Use type hints everywhere
3. **Async First**: For I/O bound operations
4. **Testing**: Comprehensive pytest coverage
5. **Documentation**: Docstrings for all public APIs

### Quality Standards:
- Style: ${this.qualityStandards['PEP 8']}
- Types: ${this.qualityStandards['Type Hints']}
- Testing: ${this.qualityStandards['Test Coverage']}
- Async: ${this.qualityStandards['Async Safety']}
- Performance: ${this.qualityStandards['Performance']}

### Python Patterns:
- **Context Managers**: Resource management
- **Decorators**: Clean abstractions
- **Generators**: Memory efficiency
- **Async/Await**: Concurrent operations
- **Dataclasses**: Structured data

Context: ${context}
`;
  }

  analyzeCode(code: string): string {
    return `
## Python Code Analysis

### Code Quality:
- PEP 8 compliance check
- Type hint coverage
- Complexity analysis
- Security vulnerabilities

### Performance Review:
1. **Algorithm Efficiency**: O(n) analysis
2. **Memory Usage**: Generator opportunities
3. **Async Opportunities**: I/O operations
4. **Caching**: Memoization candidates

### Best Practices:
- Pythonic idioms usage
- Error handling quality
- Resource management
- Testing coverage

### Recommendations:
- Refactoring opportunities
- Performance optimizations
- Security improvements
- Testing additions
`;
  }
}
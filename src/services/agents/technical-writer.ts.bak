/**
 * Technical Writer Agent
 * Specialist in creating clear, comprehensive technical documentation
 */

import { BaseAgent, AgentActivationContext, AgentResponse, AgentCollaboration } from './types';

export class TechnicalWriterAgent extends BaseAgent {
  constructor() {
    super(
      'technical-writer',
      'Technical Writer',
      'Technical documentation specialist, API documenter, tutorial creator',
      ['clarity-excellence', 'user-focused', 'comprehensive-coverage', 'maintainability']
    );
  }

  get contextEvaluation() {
    return {
      technical_writing: 100,
      api_documentation: 95,
      tutorials: 90,
      reference_docs: 90,
      diagrams: 85,
      style_guides: 85,
    };
  }

  get mcpPreferences() {
    return {
      primary: ['context7'], // For documentation standards
      secondary: ['sequential'], // For structured content
      avoided: ['magic', 'playwright'], // Focus on writing
    };
  }

  get optimizedCommands() {
    return [
      '/document --technical',
      '/document --api',
      '/document --tutorial',
      '/explain --technical',
    ];
  }

  get autoActivationTriggers() {
    return [
      'api documentation',
      'technical docs',
      'reference guide',
      'developer docs',
      'integration guide',
      'sdk documentation',
      'openapi',
      'swagger',
      'tutorial',
      'cookbook',
      'architecture docs',
    ];
  }

  get qualityStandards() {
    return {
      'Accuracy': '100% technical correctness',
      'Completeness': 'All APIs and features documented',
      'Clarity': 'Clear to target technical audience',
      'Examples': 'Working code samples for all features',
      'Versioning': 'Clear version compatibility info',
      'Searchability': 'Well-indexed and structured',
    };
  }

  shouldActivate(context: AgentActivationContext): number {
    let score = 0;
    const keywords = context.keywords.map(k => k.toLowerCase());
    
    // High activation for technical documentation
    const techDocTerms = ['api', 'technical', 'reference', 'sdk', 'developer'];
    if (keywords.some(k => techDocTerms.includes(k))) {
      score += 0.8;
    }
    
    // Activate for API documentation
    if (keywords.some(k => k.includes('openapi') || k.includes('swagger') || k.includes('endpoint'))) {
      score += 0.7;
    }
    
    // Activate for tutorials and guides
    if (keywords.some(k => k.includes('tutorial') || k.includes('cookbook') || k.includes('integration'))) {
      score += 0.6;
    }
    
    // Context-based activation
    if (context.domain === 'documentation') score += 0.4;
    if (keywords.some(k => k.includes('document'))) score += 0.3;
    
    return Math.min(score, 1.0);
  }

  getDecisionFramework(context: string): string {
    return `
## Technical Writing Framework

**Priority Hierarchy**: Accuracy > clarity > completeness > examples > maintainability

### Documentation Strategy:
1. **Audience Analysis**: Identify technical level
2. **Information Architecture**: Structure for discoverability
3. **Content Development**: Write with precision
4. **Code Examples**: Provide working samples
5. **Review Process**: Technical and editorial review

### Quality Standards:
- Accuracy: ${this.qualityStandards['Accuracy']}
- Coverage: ${this.qualityStandards['Completeness']}
- Clarity: ${this.qualityStandards['Clarity']}
- Examples: ${this.qualityStandards['Examples']}
- Versioning: ${this.qualityStandards['Versioning']}

### Documentation Types:
- **API Reference**: Endpoint documentation, schemas
- **SDK Guides**: Language-specific integration
- **Architecture**: System design and patterns
- **Tutorials**: Step-by-step learning paths
- **Troubleshooting**: Common issues and solutions

Context: ${context}
`;
  }

  analyzeCode(code: string): string {
    return `
## Technical Documentation Analysis

### Documentation Coverage:
- API endpoints documented: X/Y
- Public methods documented: X/Y
- Complex algorithms explained: X/Y
- Integration points covered: X/Y

### Content Quality:
1. **Accuracy**: Technical correctness verified
2. **Examples**: Code samples availability
3. **Clarity**: Explanation quality assessment
4. **Structure**: Information architecture review

### Improvement Areas:
- Missing API documentation
- Outdated examples
- Unclear technical concepts
- Version compatibility gaps
`;
  }
}
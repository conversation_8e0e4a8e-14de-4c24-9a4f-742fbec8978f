/**
 * System Architect Agent
 * Systems architecture specialist with long-term thinking focus
 */

import { BaseAgent, AgentActivationContext, AgentResponse, AgentCollaboration } from './types';

export class ArchitectAgent extends BaseAgent {
  id = 'architect';
  name = 'System Architect';
  identity = 'Systems architecture specialist, long-term thinking focus, scalability expert';
  
  priorityHierarchy = [
    'Long-term maintainability',
    'Scalability',
    'Performance',
    'Short-term gains'
  ];
  
  corePrinciples = [
    'Systems Thinking: Analyze impacts across entire system',
    'Future-Proofing: Design decisions that accommodate growth',
    'Dependency Management: Minimize coupling, maximize cohesion'
  ];
  
  contextEvaluation = {
    'architecture': 100,
    'implementation': 70,
    'maintenance': 90,
    'design': 95,
    'analysis': 85,
    'infrastructure': 90
  };
  
  mcpServerPreferences = {
    primary: ['sequential', 'serena'],
    secondary: ['context7', 'morphllm'],
    avoided: ['magic']
  };
  
  optimizedCommands = [
    '/analyze',
    '/estimate',
    '/improve --arch',
    '/design',
    '/workflow'
  ];
  
  autoActivationTriggers = [
    'architecture',
    'design',
    'scalability',
    'system',
    'structure',
    'dependencies',
    'modules',
    'components',
    'patterns',
    'framework'
  ];
  
  qualityStandards = [
    'Maintainability: Solutions must be understandable and modifiable',
    'Scalability: Designs accommodate growth and increased load',
    'Modularity: Components should be loosely coupled and highly cohesive'
  ];

  analyze(input: string, _context: AgentActivationContext): AgentResponse {
    const response: AgentResponse = {
      agent: this.id,
      analysis: '',
      recommendations: [],
      confidence: 0.85
    };

    // Analyze system architecture aspects
    if (input.includes('architecture') || input.includes('design')) {
      response.analysis = 'System architecture analysis indicates potential for modular design patterns';
      response.recommendations.push(
        'Consider implementing a layered architecture',
        'Evaluate microservices vs monolithic approach',
        'Implement proper dependency injection'
      );
    }

    // Check for scalability concerns
    if (input.includes('scale') || input.includes('performance')) {
      response.analysis += '\nScalability assessment reveals optimization opportunities';
      response.recommendations.push(
        'Implement caching strategies',
        'Consider horizontal scaling patterns',
        'Optimize database queries and indexing'
      );
      response.collaborationNeeded = ['performance'];
    }

    // Dependency analysis
    if (input.includes('dependency') || input.includes('coupling')) {
      response.analysis += '\nDependency analysis shows coupling concerns';
      response.recommendations.push(
        'Reduce tight coupling between modules',
        'Implement interface-based design',
        'Use dependency inversion principle'
      );
    }

    return response;
  }

  collaborate(otherAgents: string[]): AgentCollaboration {
    return {
      primary: this.id,
      consulting: otherAgents.filter(agent => 
        ['performance', 'backend', 'frontend', 'security'].includes(agent)
      ),
      validation: ['qa', 'refactorer'],
      handoffMechanisms: [
        'Architecture documentation handoff',
        'Design pattern recommendations',
        'System constraints specification'
      ]
    };
  }

  getCapabilities(): string[] {
    return [
      'System-wide architectural analysis',
      'Dependency graph visualization',
      'Scalability pattern identification',
      'Modular design optimization',
      'Long-term maintainability assessment',
      'Technical debt evaluation',
      'Architecture documentation generation'
    ];
  }

  getPreferredTools(): string[] {
    return [
      'Sequential-thinking for deep analysis',
      'Context7 for architectural patterns',
      'Grep for dependency tracking',
      'Task for complex multi-step operations',
      'TodoWrite for structured planning'
    ];
  }

  getSystemInstruction(): string {
    return `You are operating as the System Architect agent with the following focus:
    
Priority: ${this.priorityHierarchy.join(' > ')}

Core Principles:
${this.corePrinciples.map(p => `- ${p}`).join('\n')}

Quality Standards:
${this.qualityStandards.map(s => `- ${s}`).join('\n')}

When analyzing and designing systems:
1. Always consider long-term maintainability and scalability
2. Evaluate system-wide impacts of decisions
3. Design for loose coupling and high cohesion
4. Create clear architectural boundaries
5. Document architectural decisions and rationale
6. Consider both technical and business constraints
7. Plan for future growth and evolution

Focus on creating robust, scalable architectures that will stand the test of time.`;
  }

  // Properties required by BaseAgent that aren't defined
  specialization = 'Systems architecture and long-term design';
  focusAreas = ['Architecture', 'Scalability', 'Maintainability', 'System Design'];
}
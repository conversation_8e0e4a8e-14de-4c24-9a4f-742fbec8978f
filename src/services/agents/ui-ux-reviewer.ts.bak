/**
 * UI/UX Reviewer Agent
 * Specialist in React/TypeScript frontend code review and UX assessment
 */

import { BaseAgent, AgentActivationContext, AgentResponse, AgentCollaboration } from './types';

export class UIUXReviewerAgent extends BaseAgent {
  constructor() {
    super(
      'ui-ux-reviewer',
      'UI/UX Code Reviewer',
      'React/TypeScript specialist, UX implementation expert, accessibility advocate',
      ['user-experience-first', 'accessibility-compliance', 'performance-optimization', 'best-practices']
    );
  }

  get contextEvaluation() {
    return {
      ui_review: 100,
      react_patterns: 95,
      accessibility: 90,
      performance: 85,
      typescript: 85,
      design_systems: 85,
    };
  }

  get mcpPreferences() {
    return {
      primary: ['magic'], // For UI patterns
      secondary: ['context7', 'playwright'], // For best practices and testing
      avoided: [], // Uses all tools as needed
    };
  }

  get optimizedCommands() {
    return [
      '/review --ui',
      '/analyze --focus frontend',
      '/improve --accessibility',
      '/test --visual',
    ];
  }

  get autoActivationTriggers() {
    return [
      'react component',
      'ui review',
      'ux implementation',
      'accessibility',
      'shadcn',
      'radix ui',
      'frontend review',
      'component review',
      'responsive design',
      'user interface',
      'typescript component',
    ];
  }

  get qualityStandards() {
    return {
      'Accessibility': 'WCAG 2.1 AA compliance',
      'Performance': 'Core Web Vitals passing',
      'TypeScript': 'Strict type safety',
      'React Patterns': 'Modern hooks and patterns',
      'Responsive': 'Mobile-first design',
      'Code Quality': 'Clean, maintainable components',
    };
  }

  shouldActivate(context: AgentActivationContext): number {
    let score = 0;
    const keywords = context.keywords.map(k => k.toLowerCase());
    
    // High activation for UI/UX review
    const uiTerms = ['ui', 'ux', 'component', 'react', 'frontend'];
    if (keywords.some(k => uiTerms.includes(k))) {
      score += 0.8;
    }
    
    // Activate for accessibility
    if (keywords.some(k => k.includes('accessib') || k.includes('a11y') || k.includes('wcag'))) {
      score += 0.7;
    }
    
    // Activate for design systems
    if (keywords.some(k => k.includes('shadcn') || k.includes('radix') || k.includes('design'))) {
      score += 0.6;
    }
    
    // Context-based activation
    if (context.domain === 'frontend') score += 0.5;
    if (context.operationType === 'analysis' || context.operationType === 'debugging') score += 0.2;
    
    return Math.min(score, 1.0);
  }

  getDecisionFramework(context: string): string {
    return `
## UI/UX Review Framework

**Priority Hierarchy**: User experience > accessibility > performance > code quality

### Review Criteria:
1. **Component Architecture**: Proper composition and reusability
2. **State Management**: Efficient and predictable state
3. **Performance**: Optimization and lazy loading
4. **Accessibility**: Keyboard navigation, screen readers
5. **TypeScript**: Type safety and inference

### Quality Standards:
- Accessibility: ${this.qualityStandards['Accessibility']}
- Performance: ${this.qualityStandards['Performance']}
- TypeScript: ${this.qualityStandards['TypeScript']}
- Patterns: ${this.qualityStandards['React Patterns']}
- Responsive: ${this.qualityStandards['Responsive']}

### Review Areas:
- **React Patterns**: Hooks, composition, performance
- **TypeScript Usage**: Types, generics, inference
- **Accessibility**: ARIA, keyboard, screen readers
- **Performance**: Bundle size, rendering, memoization
- **UX Implementation**: Interactions, feedback, errors

Context: ${context}
`;
  }

  analyzeCode(code: string): string {
    return `
## UI/UX Code Review

### Component Analysis:
- React pattern compliance
- TypeScript type safety
- Prop validation and defaults
- State management approach

### Accessibility Review:
1. **ARIA**: Proper attributes and roles
2. **Keyboard**: Navigation support
3. **Screen Reader**: Announcement quality
4. **Color Contrast**: WCAG compliance

### Performance Assessment:
- Bundle impact analysis
- Render optimization opportunities
- Memoization recommendations
- Code splitting suggestions

### UX Implementation:
- Loading states
- Error handling
- User feedback
- Responsive behavior
`;
  }
}
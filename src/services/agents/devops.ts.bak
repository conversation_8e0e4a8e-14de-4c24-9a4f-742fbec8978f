/**
 * DevOps Agent
 * Infrastructure and deployment specialist focused on automation and reliability
 */

import { BaseAgent, AgentActivationContext, AgentResponse, AgentCollaboration } from './types';

export class DevOpsAgent extends BaseAgent {
  constructor() {
    super(
      'devops',
      'DevOps Engineer',
      'Infrastructure specialist, deployment expert, reliability engineer',
      ['automation-first', 'observability-default', 'reliability-engineering']
    );
  }

  get contextEvaluation() {
    return {
      infrastructure: 100,
      deployment: 95,
      automation: 95,
      monitoring: 90,
      cicd: 90,
      containerization: 85,
      scaling: 85,
    };
  }

  get mcpPreferences() {
    return {
      primary: ['sequential'], // For infrastructure analysis
      secondary: ['context7'], // For deployment patterns
      avoided: ['magic'], // UI generation not relevant
    };
  }

  get optimizedCommands() {
    return [
      '/git',
      '/deploy',
      '/analyze --focus infrastructure',
      '/automate',
    ];
  }

  get autoActivationTriggers() {
    return [
      'deploy',
      'infrastructure',
      'ci/cd',
      'pipeline',
      'docker',
      'kubernetes',
      'automation',
      'monitoring',
      'scaling',
      'devops',
      'release',
    ];
  }

  get qualityStandards() {
    return {
      'Deployment Frequency': 'Multiple per day',
      'Lead Time': '<1 hour',
      'MTTR': '<30 minutes',
      'Change Failure Rate': '<5%',
      'Availability': '99.9% uptime',
      'Automation Coverage': '>90%',
    };
  }

  shouldActivate(context: AgentActivationContext): number {
    let score = 0;
    const keywords = context.keywords.map(k => k.toLowerCase());
    
    // High activation for infrastructure keywords
    const infraTerms = ['deploy', 'infrastructure', 'devops', 'pipeline', 'release'];
    if (keywords.some(k => infraTerms.includes(k))) {
      score += 0.8;
    }
    
    // Activate for containerization
    if (keywords.some(k => k.includes('docker') || k.includes('kubernetes') || k.includes('container'))) {
      score += 0.7;
    }
    
    // Activate for automation
    if (keywords.some(k => k.includes('automat') || k.includes('ci') || k.includes('cd'))) {
      score += 0.6;
    }
    
    // Context-based activation
    if (context.domain === 'infrastructure') score += 0.5;
    if (context.complexity > 0.6) score += 0.2;
    
    return Math.min(score, 1.0);
  }

  getDecisionFramework(context: string): string {
    return `
## DevOps Framework

**Priority Hierarchy**: Automation > observability > reliability > scalability > manual processes

### Infrastructure Principles:
1. **Infrastructure as Code**: Version-controlled, automated
2. **Immutable Infrastructure**: Replace, don't modify
3. **Observability First**: Monitor everything
4. **Fail Fast**: Early detection and recovery

### Quality Metrics:
- Deployment: ${this.qualityStandards['Deployment Frequency']}
- Lead Time: ${this.qualityStandards['Lead Time']}
- MTTR: ${this.qualityStandards['MTTR']}
- Failure Rate: ${this.qualityStandards['Change Failure Rate']}
- Availability: ${this.qualityStandards['Availability']}
- Automation: ${this.qualityStandards['Automation Coverage']}

### Automation Strategy:
- **CI/CD Pipeline**: Automated build, test, deploy
- **Configuration Management**: Ansible, Terraform
- **Container Orchestration**: Docker, Kubernetes
- **Monitoring & Alerting**: Prometheus, Grafana
- **Incident Response**: Automated remediation

Context: ${context}
`;
  }

  analyzeCode(code: string): string {
    return `
## Infrastructure Analysis

### Deployment Assessment:
- Current deployment process evaluation
- Automation opportunities identified
- Infrastructure bottlenecks detected
- Security considerations noted

### Recommendations:
1. **Immediate**: Quick automation wins
2. **Short-term**: Pipeline improvements
3. **Long-term**: Architecture evolution

### Monitoring Gaps:
- Missing metrics identified
- Alert coverage assessment
- Observability improvements
- SLO/SLI recommendations
`;
  }
}
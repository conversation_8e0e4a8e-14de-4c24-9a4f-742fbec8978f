/**
 * Agent System Types and Interfaces
 * Core definitions for SuperClaude agent personas
 */

export interface AgentPersona {
  id: string;
  name: string;
  identity: string;
  priorityHierarchy: string[];
  corePrinciples: string[];
  contextEvaluation: Record<string, number>;
  mcpServerPreferences: {
    primary: string[];
    secondary: string[];
    avoided: string[];
  };
  optimizedCommands: string[];
  autoActivationTriggers: string[];
  qualityStandards: string[];
  performanceBudgets?: Record<string, any>;
  investigationMethodology?: string[];
  learningPathwayOptimization?: Record<string, any>;
  codeQualityMetrics?: Record<string, any>;
  infrastructureAutomationStrategy?: Record<string, any>;
  audienceAnalysisFramework?: Record<string, any>;
}

export interface AgentActivationContext {
  domain?: string;
  operationType?: string;
  complexity: number;
  keywords: string[];
  files?: string[];
  errors?: boolean;
  performance?: boolean;
}

export interface AgentCollaboration {
  primary: string;
  consulting: string[];
  validation: string[];
  handoffMechanisms: string[];
}

export interface AgentResponse {
  agent: string;
  analysis: string;
  recommendations: string[];
  collaborationNeeded?: string[];
  confidence: number;
}

/**
 * Agent enhancement for prompts
 */
export interface AgentEnhancement {
  prompt: string;
  capabilities: string[];
  preferredTools: string[];
  systemInstruction: string;
}

/**
 * Agent behavior result
 */
export interface AgentBehaviorResult {
  enhancedPrompt: string;
  capabilities: string[];
  tools: string[];
  instructions: string[];
}

export abstract class BaseAgent implements AgentPersona {
  abstract id: string;
  abstract name: string;
  abstract identity: string;
  abstract priorityHierarchy: string[];
  abstract corePrinciples: string[];
  abstract contextEvaluation: Record<string, number>;
  abstract mcpServerPreferences: {
    primary: string[];
    secondary: string[];
    avoided: string[];
  };
  abstract optimizedCommands: string[];
  abstract autoActivationTriggers: string[];
  abstract qualityStandards: string[];
  
  // Additional properties for agent functionality
  abstract specialization: string;
  abstract focusAreas: string[];

  /**
   * Evaluate if this agent should activate based on context
   */
  shouldActivate(context: AgentActivationContext): number {
    let score = 0;
    
    // Check keyword matches
    const keywordMatches = context.keywords.filter(keyword => 
      this.autoActivationTriggers.some(trigger => 
        keyword.toLowerCase().includes(trigger.toLowerCase())
      )
    );
    score += keywordMatches.length * 0.3;
    
    // Check domain match
    if (context.domain && this.contextEvaluation[context.domain]) {
      score += this.contextEvaluation[context.domain] / 100;
    }
    
    // Check operation type
    if (context.operationType && this.contextEvaluation[context.operationType]) {
      score += this.contextEvaluation[context.operationType] / 100 * 0.5;
    }
    
    // Complexity factor
    score += context.complexity * 0.2;
    
    return Math.min(score, 1.0);
  }

  /**
   * Generate analysis based on agent's expertise
   */
  abstract analyze(input: string, context: AgentActivationContext): AgentResponse;

  /**
   * Collaborate with other agents
   */
  abstract collaborate(otherAgents: string[]): AgentCollaboration;
  
  /**
   * Enhance prompt with agent-specific context
   */
  enhancePrompt(prompt: string): AgentEnhancement {
    return {
      prompt: this.addAgentContext(prompt),
      capabilities: this.getCapabilities(),
      preferredTools: this.getPreferredTools(),
      systemInstruction: this.getSystemInstruction()
    };
  }
  
  /**
   * Add agent-specific context to prompt
   */
  protected addAgentContext(prompt: string): string {
    return `[Agent: ${this.name}] ${prompt}\n\n` +
           `Specialized Context: ${this.specialization}\n` +
           `Focus Areas: ${this.focusAreas.join(', ')}`;
  }
  
  /**
   * Get agent capabilities
   */
  abstract getCapabilities(): string[];
  
  /**
   * Get preferred tools for this agent
   */
  abstract getPreferredTools(): string[];
  
  /**
   * Get system instruction for Claude
   */
  abstract getSystemInstruction(): string;
}
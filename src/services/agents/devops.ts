/**
 * DevOps Agent
 * Infrastructure automation and deployment specialist
 */

import { BaseAgent, AgentActivationContext, AgentResponse, AgentCollaboration } from './types';

export class DevOpsAgent extends BaseAgent {
  id = 'devops';
  name = '<PERSON>Ops Engineer';
  identity = 'Infrastructure specialist, deployment expert, reliability engineer';
  
  priorityHierarchy = [
    'Automation > manual processes',
    'Observability > blind spots',
    'Reliability > features',
    'Scalability > quick fixes',
    'Security > convenience'
  ];
  
  corePrinciples = [
    'Infrastructure as Code: Version control everything',
    'Observability by Default: Monitor, log, alert from start',
    'Reliability Engineering: Design for failure and recovery',
    'Continuous Improvement: Automate repetitive tasks',
    'Security Integration: DevSecOps approach'
  ];
  
  contextEvaluation = {
    infrastructure: 100,
    deployment: 95,
    automation: 95,
    monitoring: 90,
    cicd: 90,
    containerization: 85,
    configuration: 85,
    scaling: 80
  };
  
  mcpServerPreferences = {
    primary: ['sequential', 'context7'],
    secondary: ['playwright'],
    avoided: ['magic'],
  };
  
  optimizedCommands = [
    '/git',
    '/deploy',
    '/analyze --infrastructure',
    '/implement --pipeline',
    '/improve --automation'
  ];
  
  autoActivationTriggers = [
    'deploy',
    'infrastructure',
    'devops',
    'pipeline',
    'docker',
    'kubernetes',
    'ci/cd',
    'automation',
    'monitoring',
    'terraform',
    'ansible',
    'jenkins'
  ];
  
  qualityStandards = [
    'Deployment Frequency: Multiple per day',
    'Lead Time: <1 hour from commit to production',
    'MTTR: <30 minutes mean time to recovery',
    'Availability: 99.9% uptime minimum',
    'Change Failure Rate: <5%',
    'Automation Coverage: >80% of processes'
  ];
  
  // Infrastructure automation strategies - used for documentation
  // @ts-ignore - Documentation object
  private automationStrategies = {
    deployment: {
      strategy: 'Zero-downtime deployments',
      tools: ['Blue-green', 'Canary', 'Rolling updates'],
      validation: 'Automated smoke tests and rollback'
    },
    configuration: {
      strategy: 'Infrastructure as Code',
      tools: ['Terraform', 'Ansible', 'CloudFormation'],
      validation: 'Policy as code validation'
    },
    monitoring: {
      strategy: 'Observability pipeline',
      tools: ['Prometheus', 'Grafana', 'ELK Stack'],
      validation: 'Alert accuracy and coverage'
    },
    scaling: {
      strategy: 'Auto-scaling policies',
      tools: ['Kubernetes HPA', 'AWS Auto Scaling'],
      validation: 'Load testing and performance metrics'
    }
  };

  shouldActivate(context: AgentActivationContext): number {
    let score = 0;
    const input = context.keywords.join(' ').toLowerCase();
    
    // High activation for infrastructure keywords
    const infraKeywords = ['deploy', 'infrastructure', 'pipeline', 'docker', 'kubernetes'];
    if (infraKeywords.some(k => input.includes(k))) {
      score += 0.7;
    }
    
    // Activation for automation contexts
    const automationKeywords = ['automate', 'ci/cd', 'workflow', 'jenkins', 'github actions'];
    if (automationKeywords.some(k => input.includes(k))) {
      score += 0.6;
    }
    
    // Activation for monitoring/observability
    const monitoringKeywords = ['monitor', 'logging', 'metrics', 'alert', 'observability'];
    if (monitoringKeywords.some(k => input.includes(k))) {
      score += 0.5;
    }
    
    // Check domain match
    if (context.domain === 'infrastructure' || context.domain === 'deployment') {
      score += 0.4;
    }
    
    // Check for configuration management
    if (input.includes('config') || input.includes('terraform') || input.includes('ansible')) {
      score += 0.3;
    }
    
    return Math.min(score, 1.0);
  }

  analyze(input: string, context: AgentActivationContext): AgentResponse {
    const analysis = this.performInfrastructureAnalysis(input, context);
    const recommendations = this.generateRecommendations(analysis);
    
    return {
      agent: this.id,
      analysis: analysis.summary,
      recommendations,
      confidence: this.shouldActivate(context)
    };
  }
  
  private performInfrastructureAnalysis(
    input: string,
    context: AgentActivationContext
  ): any {
    return {
      summary: `Infrastructure analysis: Evaluating deployment pipeline, automation coverage, and reliability metrics`,
      automationLevel: this.assessAutomationLevel(input),
      reliabilityScore: this.calculateReliabilityScore(context),
      scalabilityAssessment: this.assessScalability(input),
      securityPosture: this.evaluateSecurityIntegration(context)
    };
  }
  
  private generateRecommendations(analysis: any): string[] {
    const recommendations = [];
    
    if (analysis.automationLevel < 0.8) {
      recommendations.push('Increase automation coverage for repetitive tasks');
    }
    
    if (analysis.reliabilityScore < 0.9) {
      recommendations.push('Implement circuit breakers and retry mechanisms');
    }
    
    if (analysis.scalabilityAssessment === 'limited') {
      recommendations.push('Design for horizontal scaling and stateless services');
    }
    
    if (analysis.securityPosture === 'basic') {
      recommendations.push('Integrate security scanning into CI/CD pipeline');
    }
    
    // Always include proactive recommendations
    recommendations.push('Implement GitOps for declarative infrastructure');
    recommendations.push('Add comprehensive monitoring and alerting');
    
    return recommendations;
  }
  
  private assessAutomationLevel(input: string): number {
    // Simplified assessment based on keywords
    const automationIndicators = ['automated', 'pipeline', 'ci/cd', 'gitops'];
    const matches = automationIndicators.filter(i => input.toLowerCase().includes(i));
    return Math.min(0.2 + (matches.length * 0.2), 1.0);
  }
  
  private calculateReliabilityScore(context: AgentActivationContext): number {
    // Base reliability score
    let score = 0.7;
    
    if (context.complexity && context.complexity < 0.5) {
      score += 0.2;
    }
    
    if (context.complexity && context.complexity < 0.3) {
      score += 0.1;
    }
    
    return Math.min(score, 1.0);
  }
  
  private assessScalability(input: string): string {
    if (input.includes('kubernetes') || input.includes('k8s')) {
      return 'excellent';
    }
    if (input.includes('docker') || input.includes('container')) {
      return 'good';
    }
    if (input.includes('serverless') || input.includes('lambda')) {
      return 'elastic';
    }
    return 'limited';
  }
  
  private evaluateSecurityIntegration(context: AgentActivationContext): string {
    const keywords = context.keywords.join(' ').toLowerCase();
    if (keywords.includes('devsecops') || keywords.includes('security scan')) {
      return 'integrated';
    }
    if (keywords.includes('vault') || keywords.includes('secrets')) {
      return 'managed';
    }
    return 'basic';
  }
  
  collaborate(otherAgents: string[]): AgentCollaboration {
    return {
      primary: this.id,
      consulting: otherAgents.filter(a => 
        ['backend', 'security', 'architect'].includes(a)
      ),
      validation: ['qa', 'performance'],
      handoffMechanisms: [
        'Infrastructure configuration handoff',
        'Deployment pipeline transfer',
        'Monitoring setup delegation'
      ]
    };
  }
}
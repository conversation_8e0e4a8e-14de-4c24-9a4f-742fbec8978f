/**
 * Mentor Agent
 * Educational specialist focused on knowledge transfer and learning
 */

import { BaseAgent, AgentActivationContext, AgentResponse, AgentCollaboration } from './types';

export class MentorAgent extends BaseAgent {
  constructor() {
    super(
      'mentor',
      'Technical Mentor',
      'Knowledge transfer specialist, educator, documentation advocate',
      ['educational-focus', 'knowledge-transfer', 'empowerment']
    );
  }

  get contextEvaluation() {
    return {
      education: 100,
      documentation: 95,
      explanation: 95,
      guidance: 90,
      best_practices: 85,
      learning_paths: 85,
    };
  }

  get mcpPreferences() {
    return {
      primary: ['context7'], // For educational resources
      secondary: ['sequential'], // For structured explanations
      avoided: ['magic'], // Prefers teaching over generation
    };
  }

  get optimizedCommands() {
    return [
      '/explain',
      '/document',
      '/index',
      '/guide',
    ];
  }

  get autoActivationTriggers() {
    return [
      'explain',
      'learn',
      'understand',
      'teach',
      'guide',
      'mentor',
      'document',
      'how does',
      'why does',
      'what is',
      'tutorial',
    ];
  }

  get qualityStandards() {
    return {
      'Clarity': 'Accessible to target audience',
      'Completeness': 'All concepts covered',
      'Examples': 'Practical, runnable examples',
      'Progressive': 'Build from simple to complex',
      'Engagement': 'Interactive and interesting',
      'Retention': 'Reinforcement through practice',
    };
  }

  shouldActivate(context: AgentActivationContext): number {
    let score = 0;
    const keywords = context.keywords.map(k => k.toLowerCase());
    
    // High activation for educational keywords
    const educationTerms = ['explain', 'learn', 'understand', 'teach', 'guide'];
    if (keywords.some(k => educationTerms.includes(k))) {
      score += 0.8;
    }
    
    // Activate for documentation
    if (keywords.some(k => k.includes('document') || k.includes('readme') || k.includes('guide'))) {
      score += 0.7;
    }
    
    // Activate for questions
    if (keywords.some(k => k.includes('how') || k.includes('why') || k.includes('what'))) {
      score += 0.6;
    }
    
    // Context-based activation
    if (context.domain === 'documentation') score += 0.4;
    if (context.complexity > 0.5) score += 0.2;
    
    return Math.min(score, 1.0);
  }

  getDecisionFramework(context: string): string {
    return `
## Educational Framework

**Priority Hierarchy**: Understanding > knowledge transfer > teaching > task completion

### Teaching Principles:
1. **Progressive Disclosure**: Start simple, add complexity
2. **Active Learning**: Engage through examples
3. **Scaffold Knowledge**: Build on existing understanding
4. **Practical Application**: Real-world examples

### Learning Objectives:
- Clarity: ${this.qualityStandards['Clarity']}
- Coverage: ${this.qualityStandards['Completeness']}
- Examples: ${this.qualityStandards['Examples']}
- Structure: ${this.qualityStandards['Progressive']}
- Engagement: ${this.qualityStandards['Engagement']}

### Knowledge Transfer Methods:
- **Conceptual**: Explain theory and principles
- **Practical**: Provide working examples
- **Visual**: Use diagrams and illustrations
- **Interactive**: Encourage experimentation
- **Reinforcement**: Review and practice

Context: ${context}
`;
  }

  analyzeCode(code: string): string {
    return `
## Educational Analysis

### Learning Opportunities:
- Key concepts to explain
- Patterns worth teaching
- Common pitfalls to highlight
- Best practices to demonstrate

### Documentation Needs:
1. **API Documentation**: Public interfaces
2. **Usage Examples**: Common scenarios
3. **Architecture Guide**: System overview
4. **Tutorial**: Step-by-step learning

### Knowledge Gaps:
- Missing explanations
- Unclear implementations
- Complex areas needing clarification
- Prerequisites to document
`;
  }
}
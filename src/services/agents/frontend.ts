/**
 * Frontend Architect Agent
 * UX specialist, accessibility advocate, performance-conscious developer
 */

import { BaseAgent, AgentActivationContext, AgentResponse, AgentCollaboration } from './types';

export class FrontendAgent extends BaseAgent {
  id = 'frontend';
  name = 'Frontend Architect';
  identity = 'UX specialist, accessibility advocate, performance-conscious developer';
  
  priorityHierarchy = [
    'User needs',
    'Accessibility',
    'Performance',
    'Technical elegance'
  ];
  
  corePrinciples = [
    'User-Centered Design: All decisions prioritize user experience and usability',
    'Accessibility by Default: Implement WCAG compliance and inclusive design',
    'Performance Consciousness: Optimize for real-world device and network conditions'
  ];
  
  contextEvaluation = {
    'ui': 100,
    'ux': 100,
    'component': 95,
    'frontend': 100,
    'design': 90,
    'accessibility': 95,
    'responsive': 90
  };
  
  performanceBudgets = {
    loadTime: '<3s on 3G, <1s on WiFi',
    bundleSize: '<500KB initial, <2MB total',
    accessibility: 'WCAG 2.1 AA minimum (90%+)',
    coreWebVitals: {
      lcp: '<2.5s',
      fid: '<100ms',
      cls: '<0.1'
    }
  };
  
  mcpServerPreferences = {
    primary: ['magic'],
    secondary: ['playwright', 'morphllm'],
    avoided: ['serena']
  };
  
  optimizedCommands = [
    '/build',
    '/improve --perf',
    '/test e2e',
    '/design',
    '/implement --type component'
  ];
  
  autoActivationTriggers = [
    'component',
    'responsive',
    'accessibility',
    'ui',
    'ux',
    'frontend',
    'react',
    'vue',
    'angular',
    'css',
    'design system',
    'user interface',
    'user experience'
  ];
  
  qualityStandards = [
    'Usability: Interfaces must be intuitive and user-friendly',
    'Accessibility: WCAG 2.1 AA compliance minimum',
    'Performance: Sub-3-second load times on 3G networks'
  ];

  analyze(input: string, _context: AgentActivationContext): AgentResponse {
    const response: AgentResponse = {
      agent: this.id,
      analysis: '',
      recommendations: [],
      confidence: 0.88
    };

    // UI/UX analysis
    if (input.includes('ui') || input.includes('component') || input.includes('design')) {
      response.analysis = 'Frontend analysis indicates opportunities for component optimization';
      response.recommendations.push(
        'Implement component-based architecture',
        'Use design system for consistency',
        'Apply atomic design principles'
      );
    }

    // Accessibility check
    if (input.includes('accessibility') || input.includes('a11y')) {
      response.analysis += '\nAccessibility audit required for WCAG compliance';
      response.recommendations.push(
        'Add ARIA labels and roles',
        'Ensure keyboard navigation',
        'Implement proper color contrast',
        'Add screen reader support'
      );
    }

    // Performance optimization
    if (input.includes('performance') || input.includes('optimize')) {
      response.analysis += '\nPerformance optimization opportunities identified';
      response.recommendations.push(
        'Implement code splitting',
        'Optimize bundle size',
        'Add lazy loading',
        'Use performance monitoring'
      );
      response.collaborationNeeded = ['performance'];
    }

    // Responsive design
    if (input.includes('responsive') || input.includes('mobile')) {
      response.analysis += '\nResponsive design improvements needed';
      response.recommendations.push(
        'Implement mobile-first approach',
        'Use CSS Grid/Flexbox',
        'Add viewport meta tags',
        'Test on multiple devices'
      );
    }

    return response;
  }

  collaborate(otherAgents: string[]): AgentCollaboration {
    return {
      primary: this.id,
      consulting: otherAgents.filter(agent => 
        ['architect', 'backend', 'performance', 'qa'].includes(agent)
      ),
      validation: ['qa', 'security'],
      handoffMechanisms: [
        'Component API documentation',
        'Design system specifications',
        'Accessibility requirements',
        'Performance budgets'
      ]
    };
  }
}
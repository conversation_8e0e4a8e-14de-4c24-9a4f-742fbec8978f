/**
 * Security Engineer Agent
 * Threat modeler, compliance expert, vulnerability specialist
 */

import { BaseAgent, AgentActivationContext, AgentResponse, AgentCollaboration } from './types';

export class SecurityAgent extends BaseAgent {
  id = 'security';
  name = 'Security Engineer';
  identity = 'Threat modeler, compliance expert, vulnerability specialist';
  
  priorityHierarchy = [
    'Security',
    'Compliance',
    'Reliability',
    'Performance',
    'Convenience'
  ];
  
  corePrinciples = [
    'Security by Default: Implement secure defaults and fail-safe mechanisms',
    'Zero Trust Architecture: Verify everything, trust nothing',
    'Defense in Depth: Multiple layers of security controls'
  ];
  
  contextEvaluation = {
    'security': 100,
    'vulnerability': 100,
    'threat': 95,
    'compliance': 90,
    'authentication': 95,
    'authorization': 95,
    'encryption': 90
  };
  
  threatAssessmentMatrix = {
    threatLevel: {
      critical: 'immediate action',
      high: '24h',
      medium: '7d',
      low: '30d'
    },
    attackSurface: {
      external: 100,
      internal: 70,
      isolated: 40
    },
    dataSensitivity: {
      'PII/Financial': 100,
      business: 80,
      public: 30
    }
  };
  
  mcpServerPreferences = {
    primary: ['sequential'],
    secondary: ['context7', 'serena'],
    avoided: ['magic', 'morphllm']
  };
  
  optimizedCommands = [
    '/analyze --focus security',
    '/improve --security',
    '/test security',
    '/review --focus security'
  ];
  
  autoActivationTriggers = [
    'vulnerability',
    'threat',
    'compliance',
    'security',
    'authentication',
    'authorization',
    'encryption',
    'audit',
    'penetration',
    'owasp',
    'cve'
  ];
  
  qualityStandards = [
    'Security First: No compromise on security fundamentals',
    'Compliance: Meet or exceed industry security standards',
    'Transparency: Clear documentation of security measures'
  ];

  analyze(input: string, _context: AgentActivationContext): AgentResponse {
    const response: AgentResponse = {
      agent: this.id,
      analysis: '',
      recommendations: [],
      confidence: 0.92
    };

    // Vulnerability assessment
    if (input.includes('vulnerability') || input.includes('cve')) {
      response.analysis = 'Security vulnerability assessment reveals potential risks';
      response.recommendations.push(
        'Scan for known vulnerabilities',
        'Update dependencies to latest secure versions',
        'Implement security headers',
        'Add input validation and sanitization'
      );
    }

    // Authentication/Authorization
    if (input.includes('auth') || input.includes('authentication') || input.includes('authorization')) {
      response.analysis += '\nAuthentication/Authorization security review needed';
      response.recommendations.push(
        'Implement multi-factor authentication',
        'Use secure session management',
        'Apply principle of least privilege',
        'Add OAuth2/OIDC support'
      );
    }

    // Compliance check
    if (input.includes('compliance') || input.includes('gdpr') || input.includes('hipaa')) {
      response.analysis += '\nCompliance assessment for regulatory requirements';
      response.recommendations.push(
        'Implement data encryption at rest and in transit',
        'Add audit logging for all sensitive operations',
        'Ensure data privacy controls',
        'Implement data retention policies'
      );
    }

    // Threat modeling
    if (input.includes('threat') || input.includes('attack')) {
      response.analysis += '\nThreat modeling reveals attack vectors';
      response.recommendations.push(
        'Perform STRIDE threat modeling',
        'Implement rate limiting',
        'Add intrusion detection',
        'Use Web Application Firewall'
      );
      response.collaborationNeeded = ['backend', 'devops'];
    }

    return response;
  }

  collaborate(otherAgents: string[]): AgentCollaboration {
    return {
      primary: this.id,
      consulting: otherAgents.filter(agent => 
        ['backend', 'devops', 'architect'].includes(agent)
      ),
      validation: ['qa'],
      handoffMechanisms: [
        'Security requirements documentation',
        'Threat model specifications',
        'Compliance checklist',
        'Security test cases'
      ]
    };
  }
}
/**
 * Agent System Index
 * Export all agent personas and utilities
 */

export * from './types';
export { ArchitectAgent } from './architect';
export { FrontendAgent } from './frontend';
export { BackendAgent } from './backend';
export { SecurityAgent } from './security';
export { AnalyzerAgent } from './analyzer';
export { PerformanceAgent } from './performance';
export { QAAgent } from './qa';
export { RefactorerAgent } from './refactorer';
export { DevOpsAgent } from './devops';
export { MentorAgent } from './mentor';
export { ScribeAgent } from './scribe';
export { RequirementsAgent } from './requirements';
export { TechnicalWriterAgent } from './technical-writer';
export { UIUXReviewerAgent } from './ui-ux-reviewer';
export { PythonExpertAgent } from './python-expert';

import { BaseAgent, AgentActivationContext } from './types';
import { ArchitectAgent } from './architect';
import { FrontendAgent } from './frontend';
import { BackendAgent } from './backend';
import { SecurityAgent } from './security';
import { AnalyzerAgent } from './analyzer';
import { PerformanceAgent } from './performance';
import { QAAgent } from './qa';
import { RefactorerAgent } from './refactorer';
import { DevOpsAgent } from './devops';
import { MentorAgent } from './mentor';
import { ScribeAgent } from './scribe';
import { RequirementsAgent } from './requirements';
import { TechnicalWriterAgent } from './technical-writer';
import { UIUXReviewerAgent } from './ui-ux-reviewer';
import { PythonExpertAgent } from './python-expert';

/**
 * Agent Registry
 * Central registry for all available agents
 */
export class AgentRegistry {
  private agents: Map<string, BaseAgent> = new Map();

  constructor() {
    this.registerDefaultAgents();
  }

  private registerDefaultAgents() {
    // Core technical agents
    this.register(new ArchitectAgent());
    this.register(new FrontendAgent());
    this.register(new BackendAgent());
    this.register(new SecurityAgent());
    this.register(new AnalyzerAgent());
    
    // Quality and process agents
    this.register(new PerformanceAgent());
    this.register(new QAAgent());
    this.register(new RefactorerAgent());
    this.register(new DevOpsAgent());
    
    // Knowledge and documentation agents
    this.register(new MentorAgent());
    this.register(new ScribeAgent());
    this.register(new RequirementsAgent());
    this.register(new TechnicalWriterAgent());
    
    // Specialized agents
    this.register(new UIUXReviewerAgent());
    this.register(new PythonExpertAgent());
  }

  register(agent: BaseAgent) {
    this.agents.set(agent.id, agent);
  }

  get(id: string): BaseAgent | undefined {
    return this.agents.get(id);
  }

  getAll(): BaseAgent[] {
    return Array.from(this.agents.values());
  }

  /**
   * Auto-activate agents based on context
   * Returns sorted list of agents by activation score
   */
  autoActivate(context: AgentActivationContext, threshold: number = 0.5): BaseAgent[] {
    const activations = Array.from(this.agents.values())
      .map(agent => ({
        agent,
        score: agent.shouldActivate(context)
      }))
      .filter(({ score }) => score >= threshold)
      .sort((a, b) => b.score - a.score);

    return activations.map(({ agent }) => agent);
  }

  /**
   * Get agents by domain
   */
  getByDomain(domain: string): BaseAgent[] {
    return Array.from(this.agents.values())
      .filter(agent => agent.contextEvaluation[domain] && agent.contextEvaluation[domain] > 50);
  }

  /**
   * Get agents optimized for specific commands
   */
  getByCommand(command: string): BaseAgent[] {
    return Array.from(this.agents.values())
      .filter(agent => agent.optimizedCommands.some(cmd => cmd.includes(command)));
  }

  /**
   * Detect cross-persona collaboration needs
   */
  detectCollaboration(primaryAgent: string, context: AgentActivationContext): string[] {
    const primary = this.agents.get(primaryAgent);
    if (!primary) return [];

    const collaborators = new Set<string>();
    
    // Add agents based on context complexity
    if (context.complexity > 0.7) {
      collaborators.add('architect');
    }
    
    // Add agents based on domain
    if (context.domain) {
      this.getByDomain(context.domain).forEach(agent => {
        if (agent.id !== primaryAgent) {
          collaborators.add(agent.id);
        }
      });
    }
    
    // Add security for sensitive operations
    if (context.keywords.some(k => ['auth', 'security', 'password', 'token'].includes(k.toLowerCase()))) {
      collaborators.add('security');
    }
    
    // Add analyzer for debugging
    if (context.errors || context.keywords.some(k => ['error', 'bug', 'issue'].includes(k.toLowerCase()))) {
      collaborators.add('analyzer');
    }

    return Array.from(collaborators);
  }
}

// Export singleton instance
export const agentRegistry = new AgentRegistry();
/**
 * Technical Writer Agent - Simplified Implementation
 * Technical documentation specialist
 */

import { BaseAgent, AgentActivationContext, AgentResponse, AgentCollaboration } from './types';

export class TechnicalWriterAgent extends BaseAgent {
  id = 'technical-writer';
  name = 'Technical Writer';
  identity = 'Technical documentation specialist, API documenter';
  
  priorityHierarchy = [
    'Accuracy',
    'Clarity',
    'Completeness',
    'Examples'
  ];
  
  corePrinciples = [
    'Clarity Excellence',
    'User Focused',
    'Comprehensive Coverage'
  ];
  
  contextEvaluation = {
    technical_writing: 100,
    api_documentation: 95,
    tutorials: 90,
    reference_docs: 90,
  };
  
  mcpServerPreferences = {
    primary: ['context7'],
    secondary: ['sequential'],
    avoided: ['magic', 'playwright'],
  };
  
  optimizedCommands = ['/document --technical', '/document --api', '/document --tutorial'];
  
  autoActivationTriggers = ['api documentation', 'technical docs', 'reference guide'];
  
  qualityStandards = [
    'Accuracy: 100% technical correctness',
    'Completeness: All APIs documented',
    'Clarity: Clear to technical audience',
    'Examples: Working code samples'
  ];

  analyze(input: string, context: AgentActivationContext): AgentResponse {
    return {
      agent: this.id,
      analysis: `Technical documentation analysis for: ${input}`,
      recommendations: ['Document all APIs', 'Add code examples', 'Ensure accuracy'],
      confidence: this.shouldActivate(context)
    };
  }
  
  collaborate(otherAgents: string[]): AgentCollaboration {
    return {
      primary: this.id,
      consulting: otherAgents.filter(a => ['scribe', 'mentor'].includes(a)),
      validation: [],
      handoffMechanisms: ['Technical documentation']
    };
  }
}
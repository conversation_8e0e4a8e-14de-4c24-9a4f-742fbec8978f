/**
 * Technical Writer Agent - Simplified Implementation
 * Technical documentation specialist
 */

import { BaseAgent, AgentActivationContext, AgentResponse, AgentCollaboration } from './types';

export class TechnicalWriterAgent extends BaseAgent {
  id = 'technical-writer';
  name = 'Technical Writer';
  identity = 'Technical documentation specialist, API documenter';
  
  priorityHierarchy = [
    'Accuracy',
    'Clarity',
    'Completeness',
    'Examples'
  ];
  
  corePrinciples = [
    'Clarity Excellence',
    'User Focused',
    'Comprehensive Coverage'
  ];
  
  contextEvaluation = {
    technical_writing: 100,
    api_documentation: 95,
    tutorials: 90,
    reference_docs: 90,
  };
  
  mcpServerPreferences = {
    primary: ['context7'],
    secondary: ['sequential'],
    avoided: ['magic', 'playwright'],
  };
  
  optimizedCommands = ['/document --technical', '/document --api', '/document --tutorial'];
  
  autoActivationTriggers = ['api documentation', 'technical docs', 'reference guide'];
  
  qualityStandards = [
    'Accuracy: 100% technical correctness',
    'Completeness: All APIs documented',
    'Clarity: Clear to technical audience',
    'Examples: Working code samples'
  ];

  analyze(input: string, context: AgentActivationContext): AgentResponse {
    return {
      agent: this.id,
      analysis: `Technical documentation analysis for: ${input}`,
      recommendations: ['Document all APIs', 'Add code examples', 'Ensure accuracy'],
      confidence: this.shouldActivate(context)
    };
  }
  
  collaborate(otherAgents: string[]): AgentCollaboration {
    return {
      primary: this.id,
      consulting: otherAgents.filter(a => ['scribe', 'mentor'].includes(a)),
      validation: [],
      handoffMechanisms: ['Technical documentation']
    };
  }

  getCapabilities(): string[] {
    return [
      'Technical documentation writing',
      'API documentation creation',
      'Code example development',
      'Tutorial and guide writing',
      'Reference documentation',
      'Documentation structure design',
      'Technical accuracy validation',
      'Documentation maintenance'
    ];
  }

  getPreferredTools(): string[] {
    return [
      'Context7 for documentation patterns',
      'Sequential for content structure',
      'Write for document creation',
      'Read for technical review',
      'Grep for code examples'
    ];
  }

  getSystemInstruction(): string {
    return `You are operating as the Technical Writer agent with the following focus:
    
Priority: ${this.priorityHierarchy.join(' > ')}

Core Principles:
${this.corePrinciples.map(p => `- ${p}`).join('\n')}

Quality Standards:
${this.qualityStandards.map(s => `- ${s}`).join('\n')}

When creating technical documentation:
1. Ensure 100% technical accuracy
2. Structure content for easy navigation
3. Include working code examples
4. Document all APIs comprehensively
5. Write for technical audience clarity
6. Maintain consistency in terminology
7. Validate against actual implementation

Focus on creating accurate, complete technical documentation that developers can rely on.`;
  }

  // Properties required by BaseAgent
  specialization = 'Technical documentation and API documentation';
  focusAreas = ['Technical Writing', 'API Documentation', 'Tutorials', 'Reference Docs'];
}
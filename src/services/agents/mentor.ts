/**
 * Mentor Agent
 * Educational guidance and knowledge transfer specialist
 */

import { BaseAgent, AgentActivationContext, AgentResponse, AgentCollaboration } from './types';

export class MentorAgent extends BaseAgent {
  id = 'mentor';
  name = 'Technical Mentor';
  identity = 'Knowledge transfer specialist, educator, documentation advocate';
  
  priorityHierarchy = [
    'Understanding > speed',
    'Knowledge transfer > task completion',
    'Teaching methodology > quick answers',
    'Empowerment > dependency',
    'Learning retention > information delivery'
  ];
  
  corePrinciples = [
    'Educational Focus: Prioritize learning over quick solutions',
    'Knowledge Transfer: Share methodology not just answers',
    'Empowerment: Enable independent problem-solving',
    'Progressive Learning: Build understanding incrementally',
    'Active Engagement: Use examples and exercises'
  ];
  
  contextEvaluation = {
    education: 100,
    documentation: 90,
    explanation: 95,
    guidance: 95,
    mentoring: 100,
    onboarding: 90,
    skillDevelopment: 85,
    conceptual: 90
  };
  
  mcpServerPreferences = {
    primary: ['context7', 'sequential'],
    secondary: ['magic'],
    avoided: [],
  };
  
  optimizedCommands = [
    '/explain',
    '/document',
    '/index',
    '/learn',
    '/guide'
  ];
  
  autoActivationTriggers = [
    'explain',
    'learn',
    'understand',
    'teach',
    'guide',
    'mentor',
    'onboard',
    'tutorial',
    'how does',
    'why does',
    'what is'
  ];
  
  qualityStandards = [
    'Clarity: Explanations must be clear and accessible',
    'Completeness: Cover all necessary concepts',
    'Engagement: Use examples and exercises',
    'Progressive: Build understanding incrementally',
    'Retention: Reinforce key concepts',
    'Adaptation: Adjust to learner level'
  ];
  
  // Learning pathway optimization
  private learningPathways = {
    beginner: {
      approach: 'Start with fundamentals',
      methods: ['Analogies', 'Visual aids', 'Simple examples'],
      pacing: 'Slow and thorough'
    },
    intermediate: {
      approach: 'Connect to existing knowledge',
      methods: ['Comparisons', 'Hands-on exercises', 'Real scenarios'],
      pacing: 'Moderate with practice'
    },
    advanced: {
      approach: 'Deep dive into complexities',
      methods: ['Edge cases', 'Performance analysis', 'Architecture'],
      pacing: 'Fast with nuance'
    }
  };
  
  // Teaching methodologies - used for analysis
  // @ts-ignore - Documentation object
  private teachingMethods = {
    socratic: 'Guide through questions',
    demonstration: 'Show by example',
    scaffolding: 'Build incrementally',
    discovery: 'Let learner explore',
    collaborative: 'Learn together'
  };

  shouldActivate(context: AgentActivationContext): number {
    let score = 0;
    const input = context.keywords.join(' ').toLowerCase();
    
    // High activation for educational keywords
    const educationKeywords = ['explain', 'learn', 'understand', 'teach', 'how', 'why'];
    if (educationKeywords.some(k => input.includes(k))) {
      score += 0.8;
    }
    
    // Activation for documentation needs
    if (input.includes('document') || input.includes('guide') || input.includes('tutorial')) {
      score += 0.6;
    }
    
    // Activation for conceptual questions
    const conceptualIndicators = ['what is', 'how does', 'why does', 'when should'];
    if (conceptualIndicators.some(i => input.includes(i))) {
      score += 0.7;
    }
    
    // Check domain match
    if (context.domain === 'education' || context.domain === 'documentation') {
      score += 0.4;
    }
    
    // Activation for onboarding
    if (input.includes('onboard') || input.includes('new to') || input.includes('beginner')) {
      score += 0.5;
    }
    
    return Math.min(score, 1.0);
  }

  analyze(input: string, context: AgentActivationContext): AgentResponse {
    const learnerLevel = this.assessLearnerLevel(input, context);
    const pathway = this.selectLearningPathway(learnerLevel);
    const method = this.selectTeachingMethod(input);
    
    const analysis = this.performEducationalAnalysis(input, context, pathway, method);
    const recommendations = this.generateLearningRecommendations(analysis);
    
    return {
      agent: this.id,
      analysis: analysis.summary,
      recommendations,
      confidence: this.shouldActivate(context)
    };
  }
  
  private assessLearnerLevel(
    input: string,
    _context: AgentActivationContext
  ): 'beginner' | 'intermediate' | 'advanced' {
    const beginnerIndicators = ['new to', 'beginner', 'basic', 'simple', 'start'];
    const advancedIndicators = ['advanced', 'complex', 'optimize', 'architecture', 'deep'];
    
    if (beginnerIndicators.some(i => input.toLowerCase().includes(i))) {
      return 'beginner';
    }
    if (advancedIndicators.some(i => input.toLowerCase().includes(i))) {
      return 'advanced';
    }
    return 'intermediate';
  }
  
  private selectLearningPathway(level: string): any {
    const pathways = this.learningPathways as any;
    return pathways[level] || this.learningPathways.intermediate;
  }
  
  private selectTeachingMethod(input: string): string {
    if (input.includes('why') || input.includes('how come')) {
      return 'socratic';
    }
    if (input.includes('show') || input.includes('example')) {
      return 'demonstration';
    }
    if (input.includes('step by step') || input.includes('gradually')) {
      return 'scaffolding';
    }
    if (input.includes('explore') || input.includes('discover')) {
      return 'discovery';
    }
    return 'collaborative';
  }
  
  private performEducationalAnalysis(
    input: string,
    context: AgentActivationContext,
    pathway: any,
    method: string
  ): any {
    return {
      summary: `Educational analysis: Using ${method} method with ${pathway.approach}`,
      keyConcepts: this.extractKeyConcepts(input),
      prerequisites: this.identifyPrerequisites(input, context),
      learningObjectives: this.defineLearningObjectives(input),
      assessmentStrategy: this.designAssessment(pathway, method)
    };
  }
  
  private extractKeyConcepts(input: string): string[] {
    // Simplified concept extraction
    const concepts = [];
    
    if (input.includes('function')) concepts.push('Functions and methods');
    if (input.includes('class')) concepts.push('Object-oriented programming');
    if (input.includes('async')) concepts.push('Asynchronous programming');
    if (input.includes('api')) concepts.push('API design and usage');
    if (input.includes('database')) concepts.push('Data persistence');
    
    return concepts.length > 0 ? concepts : ['General programming concepts'];
  }
  
  private identifyPrerequisites(input: string, _context: AgentActivationContext): string[] {
    const prerequisites = [];
    
    if (input.includes('advanced')) {
      prerequisites.push('Solid understanding of fundamentals');
    }
    if (input.includes('react')) {
      prerequisites.push('JavaScript fundamentals', 'HTML/CSS basics');
    }
    if (input.includes('docker')) {
      prerequisites.push('Command line familiarity', 'Basic networking');
    }
    
    return prerequisites;
  }
  
  private defineLearningObjectives(_input: string): string[] {
    return [
      'Understand the core concepts',
      'Apply knowledge to practical scenarios',
      'Identify common patterns and anti-patterns',
      'Develop problem-solving strategies',
      'Build confidence through practice'
    ];
  }
  
  private designAssessment(pathway: any, method: string): string {
    return `Progressive assessment using ${method} approach with ${pathway.pacing} pacing`;
  }
  
  private generateLearningRecommendations(analysis: any): string[] {
    const recommendations = [];
    
    // Add concept-specific recommendations
    for (const concept of analysis.keyConcepts) {
      recommendations.push(`Master ${concept} through hands-on practice`);
    }
    
    // Add prerequisite recommendations
    if (analysis.prerequisites.length > 0) {
      recommendations.push(`Review prerequisites: ${analysis.prerequisites.join(', ')}`);
    }
    
    // Add method-specific recommendations
    recommendations.push('Use interactive examples for better retention');
    recommendations.push('Practice with progressively complex exercises');
    recommendations.push('Document learning for future reference');
    
    return recommendations;
  }
  
  collaborate(otherAgents: string[]): AgentCollaboration {
    return {
      primary: this.id,
      consulting: otherAgents.filter(a => 
        ['architect', 'analyzer', 'scribe'].includes(a)
      ),
      validation: ['technical-writer'],
      handoffMechanisms: [
        'Knowledge transfer documentation',
        'Learning pathway handoff',
        'Educational content delegation'
      ]
    };
  }

  getCapabilities(): string[] {
    return [
      'Educational guidance and knowledge transfer',
      'Learning pathway design and optimization',
      'Progressive skill development planning',
      'Concept explanation and clarification',
      'Tutorial and guide creation',
      'Onboarding process design',
      'Knowledge retention strategies',
      'Teaching methodology selection'
    ];
  }

  getPreferredTools(): string[] {
    return [
      'Context7 for educational resources',
      'Sequential for structured explanations',
      'TodoWrite for learning tracking',
      'Read for documentation review',
      'Write for guide creation'
    ];
  }

  getSystemInstruction(): string {
    return `You are operating as the Technical Mentor agent with the following focus:
    
Priority: ${this.priorityHierarchy.join(' > ')}

Core Principles:
${this.corePrinciples.map(p => `- ${p}`).join('\n')}

Learning Pathways:
- Beginner: ${this.learningPathways.beginner.approach}
- Intermediate: ${this.learningPathways.intermediate.approach}
- Advanced: ${this.learningPathways.advanced.approach}

Quality Standards:
${this.qualityStandards.map(s => `- ${s}`).join('\n')}

When providing educational guidance:
1. Assess learner's current knowledge level
2. Select appropriate teaching methodology
3. Build understanding incrementally
4. Use examples and exercises for reinforcement
5. Validate comprehension before advancing
6. Adapt teaching style to learner needs
7. Document learning progress and outcomes

Focus on enabling independent problem-solving and long-term knowledge retention.`;
  }

  // Properties required by BaseAgent
  specialization = 'Educational guidance and knowledge transfer';
  focusAreas = ['Education', 'Mentoring', 'Documentation', 'Knowledge Transfer'];
}
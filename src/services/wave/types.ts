/**
 * Wave Execution Engine Types
 * Multi-stage command execution with compound intelligence
 */

/**
 * Wave phase types
 */
export type WavePhase = 
  | 'review'
  | 'planning'
  | 'implementation'
  | 'validation'
  | 'optimization';

/**
 * Wave strategy types
 */
export type WaveStrategyType = 
  | 'progressive'    // Incremental enhancement
  | 'systematic'     // Methodical analysis
  | 'adaptive'       // Dynamic configuration
  | 'enterprise';    // Large-scale operations

/**
 * Wave execution state
 */
export interface WaveState {
  waveNumber: number;
  phase: WavePhase;
  progress: number; // 0-100
  status: 'pending' | 'active' | 'completed' | 'failed';
  startTime?: Date;
  endTime?: Date;
  results?: WaveResult;
  errors?: string[];
}

/**
 * Wave configuration
 */
export interface WaveConfig {
  maxWaves: number;
  strategy: WaveStrategyType;
  autoAdvance: boolean;
  validationRequired: boolean;
  parallelExecution: boolean;
  rollbackEnabled: boolean;
}

/**
 * Individual wave definition
 */
export interface Wave {
  waveNumber: number;
  phase: WavePhase;
  operations: string[];
  persona?: string;
  mcpServers?: string[];
  estimatedTime?: number;
  dependencies?: number[]; // Previous wave numbers
  validationCriteria?: ValidationCriteria;
}

/**
 * Wave strategy definition
 */
export interface WaveStrategy {
  name: WaveStrategyType;
  description: string;
  waves: Wave[];
  validationGates: boolean;
  rollbackPoints: number[];
  concurrencyLevel: number;
}

/**
 * Wave result
 */
export interface WaveResult {
  waveNumber: number;
  phase: WavePhase;
  success: boolean;
  duration: number;
  artifacts: string[];
  metrics?: WaveMetrics;
  validationReport?: ValidationReport;
}

/**
 * Wave metrics
 */
export interface WaveMetrics {
  tokensUsed: number;
  filesModified: number;
  testsRun: number;
  testsPassed: number;
  performanceScore: number;
  qualityScore: number;
}

/**
 * Validation criteria
 */
export interface ValidationCriteria {
  required: boolean;
  type: 'automatic' | 'manual' | 'hybrid';
  checks: ValidationCheck[];
  threshold: number; // Pass percentage required
}

/**
 * Individual validation check
 */
export interface ValidationCheck {
  name: string;
  type: 'syntax' | 'type' | 'lint' | 'security' | 'test' | 'performance' | 'documentation' | 'integration';
  severity: 'critical' | 'high' | 'medium' | 'low';
  autoFix?: boolean;
}

/**
 * Validation report
 */
export interface ValidationReport {
  passed: boolean;
  score: number;
  checks: ValidationCheckResult[];
  recommendations: string[];
}

/**
 * Validation check result
 */
export interface ValidationCheckResult {
  check: ValidationCheck;
  passed: boolean;
  message?: string;
  autoFixed?: boolean;
}

/**
 * Wave execution context
 */
export interface WaveExecutionContext {
  command: string;
  arguments: string;
  flags: string[];
  complexity: number;
  fileCount: number;
  directoryCount: number;
  domains: string[];
  requiresValidation: boolean;
  estimatedDuration: number;
}

/**
 * Wave orchestration options
 */
export interface WaveOrchestrationOptions {
  strategy?: WaveStrategyType;
  maxWaves?: number;
  autoValidation?: boolean;
  parallelWaves?: boolean;
  dryRun?: boolean;
  verbose?: boolean;
  checkpoint?: boolean;
  rollback?: boolean;
}

/**
 * Wave checkpoint
 */
export interface WaveCheckpoint {
  waveNumber: number;
  timestamp: Date;
  state: WaveState;
  artifacts: string[];
  canRollback: boolean;
  metadata: Record<string, any>;
}

/**
 * Wave rollback operation
 */
export interface WaveRollback {
  fromWave: number;
  toWave: number;
  reason: string;
  artifacts: string[];
  success: boolean;
}

/**
 * Wave event types
 */
export type WaveEventType = 
  | 'wave:started'
  | 'wave:completed'
  | 'wave:failed'
  | 'wave:validated'
  | 'wave:checkpoint'
  | 'wave:rollback'
  | 'phase:started'
  | 'phase:completed'
  | 'validation:started'
  | 'validation:completed';

/**
 * Wave event
 */
export interface WaveEvent {
  type: WaveEventType;
  waveNumber: number;
  phase?: WavePhase;
  timestamp: Date;
  data?: any;
}

/**
 * Wave event handler
 */
export type WaveEventHandler = (event: WaveEvent) => void;

/**
 * Wave execution engine interface
 */
export interface IWaveExecutionEngine {
  // Configuration
  configure(options: WaveOrchestrationOptions): void;
  getConfig(): WaveConfig;
  
  // Strategy management
  selectStrategy(context: WaveExecutionContext): WaveStrategy;
  getStrategy(): WaveStrategy | null;
  
  // Execution
  execute(strategy: WaveStrategy, context: WaveExecutionContext): Promise<WaveResult[]>;
  executeWave(wave: Wave, context: WaveExecutionContext): Promise<WaveResult>;
  
  // State management
  getCurrentWave(): number;
  getCurrentPhase(): WavePhase | null;
  getState(): WaveState[];
  
  // Validation
  validate(wave: Wave, result: WaveResult): Promise<ValidationReport>;
  runValidationGate(waveNumber: number): Promise<boolean>;
  
  // Checkpointing
  createCheckpoint(waveNumber: number): WaveCheckpoint;
  loadCheckpoint(checkpoint: WaveCheckpoint): void;
  getCheckpoints(): WaveCheckpoint[];
  
  // Rollback
  canRollback(toWave: number): boolean;
  rollback(toWave: number, reason: string): Promise<WaveRollback>;
  
  // Events
  on(event: WaveEventType, handler: WaveEventHandler): void;
  off(event: WaveEventType, handler: WaveEventHandler): void;
  
  // Utilities
  estimateComplexity(context: WaveExecutionContext): number;
  shouldUseWaves(context: WaveExecutionContext): boolean;
  getRecommendedStrategy(context: WaveExecutionContext): WaveStrategyType;
}
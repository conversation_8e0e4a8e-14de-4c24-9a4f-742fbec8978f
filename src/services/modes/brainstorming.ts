/**
 * Brainstorming Mode
 * Collaborative discovery and creative problem solving
 */

import { BaseMode, ModeContext, ModeBehavior } from './types';
import type { SuperClaudeMode } from '@/types/superClaude';

export class BrainstormingMode extends BaseMode {
  id: SuperClaudeMode = 'brainstorming';
  name = 'Brainstorming Mode';
  description = 'Collaborative discovery mindset for interactive requirements exploration';
  
  activationTriggers = [
    'brainstorm',
    'explore',
    'discuss',
    'figure out',
    'not sure',
    'maybe',
    'possibly',
    'thinking about',
    'could we',
    'what if'
  ];
  
  behaviorChanges = [
    'Socratic dialogue approach',
    'Non-presumptive exploration',
    'Collaborative partnership',
    'Requirements discovery',
    'Creative problem solving'
  ];
  
  symbolsEnabled = true;
  
  // Discovery markers for brainstorming
  private discoveryMarkers = {
    question: '🤔',     // Probing questions
    exploration: '🔍',  // Exploring possibilities
    insight: '💡',      // New discoveries
    collaboration: '🤝', // Working together
    creativity: '🎨',   // Creative solutions
    validation: '✅'    // Confirming understanding
  };

  shouldActivate(context: ModeContext): number {
    let score = 0;
    const keywords = context.keywords || [];
    
    // Check for exploration keywords
    const explorationTerms = ['brainstorm', 'explore', 'discuss', 'figure', 'maybe', 'possibly'];
    if (keywords.some(k => explorationTerms.some(t => k.toLowerCase().includes(t)))) {
      score += 0.8;
    }
    
    // Activate for vague requirements
    const vagueIndicators = ['something', 'thinking', 'not sure', 'could', 'might'];
    if (keywords.some(k => vagueIndicators.some(t => k.toLowerCase().includes(t)))) {
      score += 0.6;
    }
    
    // Activate for creative contexts
    if (keywords.some(k => k.toLowerCase().includes('create') || k.toLowerCase().includes('design'))) {
      score += 0.4;
    }
    
    // Lower activation for high complexity (prefer more structured modes)
    if (context.complexity && context.complexity > 0.8) {
      score -= 0.2;
    }
    
    return Math.max(0, Math.min(score, 1.0));
  }
  
  applyBehaviors(): ModeBehavior[] {
    return [
      {
        type: 'transparency',
        enabled: true,
        config: {
          markers: this.discoveryMarkers,
          showQuestions: true,
          interactiveMode: true,
          exploratoryApproach: true
        }
      },
      {
        type: 'symbol',
        enabled: true,
        config: {
          useDiscoveryMarkers: true,
          compressionLevel: 0 // No compression during brainstorming
        }
      }
    ];
  }
  
  getConfig(): Record<string, any> {
    return {
      interactionStyle: 'collaborative',
      questioningApproach: 'socratic',
      assumptionLevel: 'minimal',
      creativityLevel: 'high',
      structureLevel: 'flexible',
      markers: this.discoveryMarkers,
      discoveryFramework: {
        question: 'Ask probing questions to uncover requirements',
        exploration: 'Explore multiple possibilities together',
        insight: 'Identify key discoveries and patterns',
        collaboration: 'Work as partners in discovery',
        creativity: 'Generate creative solutions',
        validation: 'Confirm understanding before proceeding'
      },
      questionTypes: [
        'What problem are you trying to solve?',
        'Who are the users/stakeholders?',
        'What does success look like?',
        'What constraints do we have?',
        'Have you considered...?',
        'What if we approached it from...'
      ]
    };
  }
  
  /**
   * Generate discovery questions
   */
  generateQuestions(topic: string, _context: ModeContext): string[] {
    const questions = [];
    
    // Problem-focused questions
    questions.push(`${this.discoveryMarkers.question} What specific problem does ${topic} solve?`);
    questions.push(`${this.discoveryMarkers.question} Who experiences this problem most acutely?`);
    
    // Solution exploration
    questions.push(`${this.discoveryMarkers.exploration} What solutions have you already considered?`);
    questions.push(`${this.discoveryMarkers.exploration} What would an ideal solution look like?`);
    
    // Constraints and requirements
    questions.push(`${this.discoveryMarkers.collaboration} What constraints should we be aware of?`);
    questions.push(`${this.discoveryMarkers.collaboration} What are the must-have vs nice-to-have features?`);
    
    // Creative exploration
    questions.push(`${this.discoveryMarkers.creativity} What if we approached this completely differently?`);
    questions.push(`${this.discoveryMarkers.creativity} Are there analogous problems in other domains?`);
    
    return questions;
  }
  
  /**
   * Generate requirements brief from discovery
   */
  generateBrief(discoveries: Record<string, any>): string {
    const markers = this.discoveryMarkers;
    
    return `
${markers.validation} **Requirements Brief**

${markers.question} **Problem Statement**
${discoveries.problem || 'To be defined'}

${markers.collaboration} **Stakeholders**
${discoveries.stakeholders || 'To be identified'}

${markers.insight} **Key Insights**
${discoveries.insights?.join('\n') || 'Gathered during discovery'}

${markers.exploration} **Proposed Solution**
${discoveries.solution || 'To be designed'}

${markers.creativity} **Creative Approaches**
${discoveries.creative || 'To be explored'}

${markers.validation} **Success Criteria**
${discoveries.success || 'To be defined'}
`;
  }
}
/**
 * Brainstorming Mode
 * Collaborative discovery and creative problem solving
 */

import { BaseMode, ModeContext, ModeBehavior } from './types';
import type { SuperClaudeMode } from '@/types/superClaude';
import { claudeBrainstormingService } from './claudeBrainstorming';

export class BrainstormingMode extends BaseMode {
  id: SuperClaudeMode = 'brainstorming';
  name = 'Brainstorming Mode';
  description = 'Collaborative discovery mindset for interactive requirements exploration';
  
  activationTriggers = [
    'brainstorm',
    'explore',
    'discuss',
    'figure out',
    'not sure',
    'maybe',
    'possibly',
    'thinking about',
    'could we',
    'what if'
  ];
  
  behaviorChanges = [
    'Socratic dialogue approach',
    'Non-presumptive exploration',
    'Collaborative partnership',
    'Requirements discovery',
    'Creative problem solving'
  ];
  
  symbolsEnabled = true;
  
  // Discovery markers for brainstorming
  private discoveryMarkers = {
    question: '🤔',     // Probing questions
    exploration: '🔍',  // Exploring possibilities
    insight: '💡',      // New discoveries
    collaboration: '🤝', // Working together
    creativity: '🎨',   // Creative solutions
    validation: '✅'    // Confirming understanding
  };

  shouldActivate(context: ModeContext): number {
    let score = 0;
    const keywords = context.keywords || [];
    
    // Check for exploration keywords
    const explorationTerms = ['brainstorm', 'explore', 'discuss', 'figure', 'maybe', 'possibly'];
    if (keywords.some(k => explorationTerms.some(t => k.toLowerCase().includes(t)))) {
      score += 0.8;
    }
    
    // Activate for vague requirements
    const vagueIndicators = ['something', 'thinking', 'not sure', 'could', 'might'];
    if (keywords.some(k => vagueIndicators.some(t => k.toLowerCase().includes(t)))) {
      score += 0.6;
    }
    
    // Activate for creative contexts
    if (keywords.some(k => k.toLowerCase().includes('create') || k.toLowerCase().includes('design'))) {
      score += 0.4;
    }
    
    // Lower activation for high complexity (prefer more structured modes)
    if (context.complexity && context.complexity > 0.8) {
      score -= 0.2;
    }
    
    return Math.max(0, Math.min(score, 1.0));
  }
  
  applyBehaviors(): ModeBehavior[] {
    return [
      {
        type: 'transparency',
        enabled: true,
        config: {
          markers: this.discoveryMarkers,
          showQuestions: true,
          interactiveMode: true,
          exploratoryApproach: true
        }
      },
      {
        type: 'symbol',
        enabled: true,
        config: {
          useDiscoveryMarkers: true,
          compressionLevel: 0 // No compression during brainstorming
        }
      }
    ];
  }
  
  getConfig(): Record<string, any> {
    return {
      interactionStyle: 'collaborative',
      questioningApproach: 'socratic',
      assumptionLevel: 'minimal',
      creativityLevel: 'high',
      structureLevel: 'flexible',
      markers: this.discoveryMarkers,
      discoveryFramework: {
        question: 'Ask probing questions to uncover requirements',
        exploration: 'Explore multiple possibilities together',
        insight: 'Identify key discoveries and patterns',
        collaboration: 'Work as partners in discovery',
        creativity: 'Generate creative solutions',
        validation: 'Confirm understanding before proceeding'
      },
      questionTypes: [
        'What problem are you trying to solve?',
        'Who are the users/stakeholders?',
        'What does success look like?',
        'What constraints do we have?',
        'Have you considered...?',
        'What if we approached it from...'
      ]
    };
  }
  
  /**
   * Generate discovery questions
   */
  generateQuestions(topic: string, _context: ModeContext): string[] {
    const questions = [];
    
    // Problem-focused questions
    questions.push(`${this.discoveryMarkers.question} What specific problem does ${topic} solve?`);
    questions.push(`${this.discoveryMarkers.question} Who experiences this problem most acutely?`);
    
    // Solution exploration
    questions.push(`${this.discoveryMarkers.exploration} What solutions have you already considered?`);
    questions.push(`${this.discoveryMarkers.exploration} What would an ideal solution look like?`);
    
    // Constraints and requirements
    questions.push(`${this.discoveryMarkers.collaboration} What constraints should we be aware of?`);
    questions.push(`${this.discoveryMarkers.collaboration} What are the must-have vs nice-to-have features?`);
    
    // Creative exploration
    questions.push(`${this.discoveryMarkers.creativity} What if we approached this completely differently?`);
    questions.push(`${this.discoveryMarkers.creativity} Are there analogous problems in other domains?`);
    
    return questions;
  }
  
  /**
   * Generate requirements brief from discovery
   */
  generateBrief(discoveries: Record<string, any>): string {
    const markers = this.discoveryMarkers;
    
    return `
${markers.validation} **Requirements Brief**

${markers.question} **Problem Statement**
${discoveries.problem || 'To be defined'}

${markers.collaboration} **Stakeholders**
${discoveries.stakeholders || 'To be identified'}

${markers.insight} **Key Insights**
${discoveries.insights?.join('\n') || 'Gathered during discovery'}

${markers.exploration} **Proposed Solution**
${discoveries.solution || 'To be designed'}

${markers.creativity} **Creative Approaches**
${discoveries.creative || 'To be explored'}

${markers.validation} **Success Criteria**
${discoveries.success || 'To be defined'}
`;
  }
  
  /**
   * Brainstorming session orchestration
   */
  orchestrateSession(topic: string, context: ModeContext): BrainstormingSession {
    const sessionId = `bs_${Date.now()}`;
    const startTime = new Date().toISOString();
    
    return {
      id: sessionId,
      topic,
      startTime,
      context,
      phases: this.getBrainstormingPhases(),
      ideas: [],
      clusters: [],
      recommendations: []
    };
  }
  
  /**
   * Get brainstorming phases
   */
  private getBrainstormingPhases(): BrainstormingPhase[] {
    return [
      {
        id: 'divergent',
        name: 'Divergent Thinking',
        description: 'Generate as many ideas as possible without judgment',
        duration: '10-15 minutes',
        techniques: ['Free Association', 'Mind Mapping', 'SCAMPER', 'Reverse Thinking']
      },
      {
        id: 'convergent',
        name: 'Convergent Thinking',
        description: 'Evaluate and refine ideas to find the best solutions',
        duration: '15-20 minutes',
        techniques: ['Pros/Cons Analysis', 'Dot Voting', 'Impact/Effort Matrix', 'Feasibility Assessment']
      },
      {
        id: 'synthesis',
        name: 'Synthesis',
        description: 'Combine and improve upon selected ideas',
        duration: '10-15 minutes',
        techniques: ['Combination', 'Enhancement', 'Prototyping Concepts', 'Action Planning']
      }
    ];
  }
  
  /**
   * Add idea to session
   */
  addIdea(session: BrainstormingSession, idea: BrainstormingIdea): void {
    // Enhance idea with Claude's creative techniques
    const enhancedIdea = claudeBrainstormingService.enhanceIdea(idea);
    
    session.ideas.push({
      ...enhancedIdea,
      id: `idea_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString()
    });
  }
  
  /**
   * Cluster ideas by theme or category
   */
  clusterIdeas(ideas: BrainstormingIdea[]): BrainstormingCluster[] {
    // Simple clustering algorithm based on category matching
    const clusters: Record<string, BrainstormingIdea[]> = {};
    
    ideas.forEach(idea => {
      const category = idea.category || 'general';
      if (!clusters[category]) {
        clusters[category] = [];
      }
      clusters[category].push(idea);
    });
    
    return Object.entries(clusters).map(([category, clusterIdeas]) => ({
      id: `cluster_${category}`,
      category,
      ideas: clusterIdeas,
      score: this.calculateClusterScore(clusterIdeas)
    }));
  }
  
  /**
   * Calculate cluster score based on idea quality metrics
   */
  private calculateClusterScore(ideas: BrainstormingIdea[]): number {
    if (ideas.length === 0) return 0;
    
    const totalScore = ideas.reduce((sum, idea) => sum + (idea.score || 0), 0);
    return totalScore / ideas.length;
  }
  
  /**
   * Generate recommendations from clustered ideas
   */
  generateRecommendations(clusters: BrainstormingCluster[]): BrainstormingRecommendation[] {
    return clusters
      .sort((a, b) => b.score - a.score)
      .slice(0, 5)
      .map((cluster, index) => ({
        id: `rec_${index}`,
        title: `Focus on ${cluster.category}`,
        description: `Prioritize solutions in the ${cluster.category} category based on high-quality ideas`,
        clusterId: cluster.id,
        priority: index + 1,
        confidence: Math.min(1, cluster.score / 10)
      }));
  }
  
  /**
   * Create mind map structure from ideas
   */
  createMindMap(ideas: BrainstormingIdea[]): MindMapNode {
    const rootNode: MindMapNode = {
      id: 'root',
      label: 'Brainstorming Topic',
      children: []
    };
    
    // Group ideas by category
    const categories: Record<string, BrainstormingIdea[]> = {};
    ideas.forEach(idea => {
      const category = idea.category || 'general';
      if (!categories[category]) {
        categories[category] = [];
      }
      categories[category].push(idea);
    });
    
    // Create category nodes
    Object.entries(categories).forEach(([category, categoryIdeas]) => {
      const categoryNode: MindMapNode = {
        id: `category_${category}`,
        label: category,
        children: categoryIdeas.map(idea => ({
          id: idea.id,
          label: idea.content,
          children: [],
          metadata: {
            score: idea.score,
            confidence: idea.confidence
          }
        }))
      };
      rootNode.children.push(categoryNode);
    });
    
    return rootNode;
  }
}

// Brainstorming Types
export interface BrainstormingSession {
  id: string;
  topic: string;
  startTime: string;
  context: ModeContext;
  phases: BrainstormingPhase[];
  ideas: BrainstormingIdea[];
  clusters: BrainstormingCluster[];
  recommendations: BrainstormingRecommendation[];
}

export interface BrainstormingPhase {
  id: 'divergent' | 'convergent' | 'synthesis';
  name: string;
  description: string;
  duration: string;
  techniques: string[];
}

export interface BrainstormingIdea {
  id?: string;
  content: string;
  category?: string;
  score?: number; // 1-10 quality score
  confidence?: number; // 0-1 confidence level
  timestamp?: string;
  metadata?: Record<string, any>;
}

export interface BrainstormingCluster {
  id: string;
  category: string;
  ideas: BrainstormingIdea[];
  score: number;
}

export interface BrainstormingRecommendation {
  id: string;
  title: string;
  description: string;
  clusterId: string;
  priority: number;
  confidence: number;
}

export interface MindMapNode {
  id: string;
  label: string;
  children: MindMapNode[];
  metadata?: Record<string, any>;
}
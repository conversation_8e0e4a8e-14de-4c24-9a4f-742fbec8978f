/**
 * Task Management Mode
 * Structured workflow execution and progress tracking
 */

import { BaseMode, ModeContext, ModeBehavior } from './types';
import type { SuperClaudeMode } from '@/types/superClaude';

export interface TaskItem {
  id: string;
  name: string;
  description?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'blocked';
  priority?: 'low' | 'medium' | 'high';
  dependencies: string[];
  subtasks: TaskItem[];
  estimatedTime?: number; // in minutes
  startTime?: number;
  endTime?: number;
  assignee?: string;
  tags?: string[];
}

export interface TaskHierarchy {
  plan: {
    id: string;
    name: string;
    marker: string;
    phases: Array<{
      id: string;
      name: string;
      marker: string;
      tasks: TaskItem[];
      dependencies: string[];
    }>;
  };
}

export class TaskManagementMode extends BaseMode {
  id: SuperClaudeMode = 'task-management';
  name = 'Task Management Mode';
  description = 'Hierarchical task organization mindset for complex multi-step operations';
  
  activationTriggers = [
    'task',
    'workflow',
    'project',
    'implement',
    'build',
    'create feature',
    'multi-step',
    'complex operation',
    'coordinate',
    'manage'
  ];
  
  behaviorChanges = [
    'Hierarchical task breakdown',
    'Progress tracking and updates',
    'Dependency management',
    'Phase-based execution',
    'Systematic organization'
  ];
  
  symbolsEnabled = true;
  
  // Task management markers
  private taskMarkers = {
    plan: '📋',      // Overall plan
    phase: '🎯',     // Major phases
    task: '📦',      // Individual tasks
    todo: '✓',       // Atomic todos
    blocked: '🚧',   // Blocked items
    progress: '🔄'   // In progress
  };
  
  // Task hierarchy levels
  private hierarchyLevels = {
    plan: { depth: 0, prefix: '📋' },
    phase: { depth: 1, prefix: '🎯' },
    task: { depth: 2, prefix: '📦' },
    todo: { depth: 3, prefix: '✓' }
  };

  shouldActivate(context: ModeContext): number {
    let score = 0;
    
    // High activation for requirements-related content
    const requirementKeywords = ['requirement', 'specification', 'user story', 'acceptance criteria', 'functional requirement'];
    const hasRequirements = context.keywords?.some(k => 
      requirementKeywords.some(rk => k.toLowerCase().includes(rk))
    );
    
    if (hasRequirements) {
      score += 0.8;
    }
    
    // High activation for multi-step operations
    if (context.fileCount && context.fileCount > 3) {
      score += 0.6;
    }
    
    if (context.directoryCount && context.directoryCount > 2) {
      score += 0.5;
    }
    
    // Activate for complex operations
    if (context.complexity && context.complexity > 0.6) {
      score += 0.5;
    }
    
    // Activate for multi-tool operations
    if (context.multiToolOperation) {
      score += 0.4;
    }
    
    // Keyword activation
    const keywords = context.keywords || [];
    const taskTerms = ['task', 'workflow', 'project', 'implement', 'build', 'coordinate'];
    if (keywords.some(k => taskTerms.some(t => k.toLowerCase().includes(t)))) {
      score += 0.3;
    }
    
    return Math.min(score, 1.0);
  }
  
  applyBehaviors(): ModeBehavior[] {
    return [
      {
        type: 'delegation',
        enabled: true,
        config: {
          hierarchicalBreakdown: true,
          progressTracking: true,
          dependencyManagement: true,
          phaseExecution: true
        }
      },
      {
        type: 'symbol',
        enabled: true,
        config: {
          useTaskMarkers: true,
          hierarchyVisualization: true,
          compressionLevel: 1 // Moderate compression
        }
      },
      {
        type: 'orchestration',
        enabled: true,
        config: {
          taskCoordination: true,
          parallelExecution: true,
          resourceAllocation: true
        }
      }
    ];
  }
  
  getConfig(): Record<string, any> {
    return {
      executionMode: 'hierarchical',
      trackingLevel: 'detailed',
      updateFrequency: 'real-time',
      parallelization: true,
      checkpointingEnabled: true,
      markers: this.taskMarkers,
      hierarchy: this.hierarchyLevels,
      executionStrategy: {
        planning: 'Break down into manageable tasks',
        organization: 'Group by phases and dependencies',
        execution: 'Track progress systematically',
        validation: 'Verify completion at each level',
        coordination: 'Manage dependencies and parallel work'
      },
      thresholds: {
        delegationTrigger: 3,  // Delegate when >3 parallel tasks
        phaseSize: 5,          // Max tasks per phase
        checkpointInterval: 30 // Minutes between checkpoints
      }
    };
  }
  
  /**
   * Generate task hierarchy with hierarchical tracking and dependency management
   */
  generateHierarchy(goal: string, context: ModeContext): TaskHierarchy {
    const markers = this.taskMarkers;
    const complexity = context.complexity || 0.5;
    
    // Create hierarchical task structure
    const hierarchy: TaskHierarchy = {
      plan: {
        id: 'plan_main',
        name: goal,
        marker: markers.plan,
        phases: []
      }
    };
    
    // Generate phases based on complexity
    const phaseCount = Math.ceil(complexity * 5);
    
    for (let i = 1; i <= phaseCount; i++) {
      const phase = {
        id: `phase_${i}`,
        name: `Phase ${i}`,
        marker: markers.phase,
        tasks: [] as TaskItem[],
        dependencies: i > 1 ? [`phase_${i-1}`] : []
      };
      
      // Generate tasks per phase with dependencies
      const taskCount = Math.ceil(3 + complexity * 2);
      for (let j = 1; j <= taskCount; j++) {
        const task: TaskItem = {
          id: `task_${i}_${j}`,
          name: `Task ${i}.${j}`,
          description: `Task ${i}.${j} in ${goal}`,
          status: 'pending',
          priority: j % 3 === 0 ? 'high' : j % 3 === 1 ? 'medium' : 'low',
          dependencies: [],
          subtasks: [],
          estimatedTime: Math.ceil(30 + Math.random() * 120), // 30-150 minutes
          tags: [`phase-${i}`, 'generated']
        };
        
        // Add dependencies within the same phase for some tasks
        if (j > 1 && Math.random() > 0.7) {
          const depIndex = Math.max(1, j - Math.floor(Math.random() * 3));
          task.dependencies.push(`task_${i}_${depIndex}`);
        }
        
        // Add subtasks for complex tasks
        if (complexity > 0.7 && Math.random() > 0.5) {
          const subtaskCount = Math.ceil(Math.random() * 4);
          for (let k = 1; k <= subtaskCount; k++) {
            task.subtasks.push({
              id: `subtask_${i}_${j}_${k}`,
              name: `Subtask ${i}.${j}.${k}`,
              description: `Subtask ${i}.${j}.${k} of Task ${i}.${j}`,
              status: 'pending',
              dependencies: k > 1 ? [`subtask_${i}_${j}_${k-1}`] : [],
              subtasks: [],
              estimatedTime: Math.ceil(10 + Math.random() * 30), // 10-40 minutes
              tags: [`task-${i}-${j}`, 'subtask']
            });
          }
        }
        
        phase.tasks.push(task);
      }
      
      hierarchy.plan.phases.push(phase);
    }
    
    return hierarchy;
  }
  
  /**
   * Update task status in hierarchy
   */
  updateTaskStatus(hierarchy: TaskHierarchy, taskId: string, status: TaskItem['status']): TaskHierarchy {
    const updatedHierarchy = JSON.parse(JSON.stringify(hierarchy)) as TaskHierarchy;
    
    // Search through all phases and tasks to find and update the task
    for (const phase of updatedHierarchy.plan.phases) {
      for (const task of phase.tasks) {
        if (task.id === taskId) {
          task.status = status;
          if (status === 'in_progress' && !task.startTime) {
            task.startTime = Date.now();
          } else if (status === 'completed' && !task.endTime) {
            task.endTime = Date.now();
          }
          return updatedHierarchy;
        }
        
        // Check subtasks
        for (const subtask of task.subtasks) {
          if (subtask.id === taskId) {
            subtask.status = status;
            if (status === 'in_progress' && !subtask.startTime) {
              subtask.startTime = Date.now();
            } else if (status === 'completed' && !subtask.endTime) {
              subtask.endTime = Date.now();
            }
            return updatedHierarchy;
          }
        }
      }
    }
    
    return updatedHierarchy;
  }
  
  /**
   * Check if task can be started (dependencies completed)
   */
  canStartTask(hierarchy: TaskHierarchy, taskId: string): boolean {
    // Find the task
    let task: TaskItem | null = null;
    let parentTask: TaskItem | null = null;
    
    for (const phase of hierarchy.plan.phases) {
      for (const t of phase.tasks) {
        if (t.id === taskId) {
          task = t;
          break;
        }
        
        // Check subtasks
        for (const st of t.subtasks) {
          if (st.id === taskId) {
            task = st;
            parentTask = t;
            break;
          }
        }
        
        if (task) break;
      }
      if (task) break;
    }
    
    if (!task) return false;
    
    // Check if all dependencies are completed
    for (const depId of task.dependencies) {
      let depTask: TaskItem | null = null;
      
      // Search for dependency task
      for (const phase of hierarchy.plan.phases) {
        for (const t of phase.tasks) {
          if (t.id === depId) {
            depTask = t;
            break;
          }
          
          // Check subtasks
          for (const st of t.subtasks) {
            if (st.id === depId) {
              depTask = st;
              break;
            }
          }
          
          if (depTask) break;
        }
        if (depTask) break;
      }
      
      if (!depTask || depTask.status !== 'completed') {
        return false;
      }
    }
    
    // If this is a subtask, check if parent task is in progress
    if (parentTask && parentTask.status !== 'in_progress' && parentTask.status !== 'completed') {
      return false;
    }
    
    return true;
  }
  
  /**
   * Get all tasks that can be started now
   */
  getStartableTasks(hierarchy: TaskHierarchy): string[] {
    const startableTasks: string[] = [];
    
    // Collect all tasks
    const allTasks: { task: TaskItem, parent?: TaskItem }[] = [];
    
    for (const phase of hierarchy.plan.phases) {
      for (const task of phase.tasks) {
        allTasks.push({ task });
        
        // Add subtasks
        for (const subtask of task.subtasks) {
          allTasks.push({ task: subtask, parent: task });
        }
      }
    }
    
    // Check each task
    for (const { task, parent } of allTasks) {
      if (task.status === 'pending' || task.status === 'blocked') {
        let canStart = true;
        
        // Check dependencies
        for (const depId of task.dependencies) {
          const depTask = allTasks.find(t => t.task.id === depId);
          if (!depTask || depTask.task.status !== 'completed') {
            canStart = false;
            break;
          }
        }
        
        // Check parent task for subtasks
        if (canStart && parent && parent.status !== 'in_progress' && parent.status !== 'completed') {
          canStart = false;
        }
        
        if (canStart) {
          startableTasks.push(task.id);
        }
      }
    }
    
    return startableTasks;
  }
  
  /**
   * Generate execution plan with dependency visualization
   */
  generateExecutionPlan(hierarchy: TaskHierarchy): string {
    const markers = this.taskMarkers;
    
    let plan = `${markers.plan} **Execution Plan**\n\n`;
    
    for (const phase of hierarchy.plan.phases) {
      plan += `${markers.phase} **${phase.name}**`;
      
      // Show phase dependencies
      if (phase.dependencies.length > 0) {
        plan += ` *(depends on: ${phase.dependencies.join(', ')})*`;
      }
      plan += '\n';
      
      for (const task of phase.tasks) {
        const statusMarker = task.status === 'completed' ? '✅' :
                           task.status === 'in_progress' ? markers.progress :
                           task.status === 'blocked' ? markers.blocked : '⏳';
        
        const priorityMarker = task.priority === 'high' ? '🔴' :
                              task.priority === 'medium' ? '🟡' : '🟢';
        
        plan += `  ${markers.task} ${task.name} ${statusMarker} ${priorityMarker}`;
        
        // Show task dependencies
        if (task.dependencies.length > 0) {
          plan += ` *(depends on: ${task.dependencies.join(', ')})*`;
        }
        
        // Show estimated time
        if (task.estimatedTime) {
          plan += ` *(${task.estimatedTime} min)*`;
        }
        
        plan += '\n';
        
        // Show subtasks
        for (const subtask of task.subtasks) {
          const subStatusMarker = subtask.status === 'completed' ? '✅' :
                                 subtask.status === 'in_progress' ? markers.progress :
                                 subtask.status === 'blocked' ? markers.blocked : '⏳';
          
          plan += `    ${markers.todo} ${subtask.name} ${subStatusMarker}`;
          
          // Show subtask dependencies
          if (subtask.dependencies.length > 0) {
            plan += ` *(depends on: ${subtask.dependencies.join(', ')})*`;
          }
          
          // Show estimated time
          if (subtask.estimatedTime) {
            plan += ` *(${subtask.estimatedTime} min)*`;
          }
          
          plan += '\n';
        }
      }
      
      plan += '\n';
    }
    
    return plan;
  }
  
  /**
   * Calculate progress metrics with hierarchical tracking
   */
  calculateProgress(hierarchy: TaskHierarchy): Record<string, number> {
    let totalTasks = 0;
    let completedTasks = 0;
    let inProgressTasks = 0;
    let blockedTasks = 0;
    let totalSubtasks = 0;
    let completedSubtasks = 0;
    
    for (const phase of hierarchy.plan.phases) {
      for (const task of phase.tasks) {
        totalTasks++;
        if (task.status === 'completed') completedTasks++;
        if (task.status === 'in_progress') inProgressTasks++;
        if (task.status === 'blocked') blockedTasks++;
        
        // Count subtasks
        for (const subtask of task.subtasks) {
          totalSubtasks++;
          if (subtask.status === 'completed') completedSubtasks++;
        }
      }
    }
    
    const totalItems = totalTasks + totalSubtasks;
    const completedItems = completedTasks + completedSubtasks;
    
    return {
      total: totalTasks,
      completed: completedTasks,
      inProgress: inProgressTasks,
      blocked: blockedTasks,
      totalSubtasks,
      completedSubtasks,
      totalItems,
      completedItems,
      percentComplete: totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0,
      percentItemsComplete: totalItems > 0 ? (completedItems / totalItems) * 100 : 0
    };
  }
  
  /**
   * Get task prioritization based on dependencies and deadlines
   */
  prioritizeTasks(hierarchy: TaskHierarchy): TaskItem[] {
    const allTasks: TaskItem[] = [];
    
    // Collect all tasks
    for (const phase of hierarchy.plan.phases) {
      for (const task of phase.tasks) {
        allTasks.push(task);
        // Add subtasks
        allTasks.push(...task.subtasks);
      }
    }
    
    // Sort by priority algorithm:
    // 1. Tasks with no pending dependencies first
    // 2. High priority tasks
    // 3. Tasks on critical path (blocking other tasks)
    // 4. Tasks with earlier deadlines (if any)
    
    return allTasks.sort((a, b) => {
      // Check if tasks can be started
      const aCanStart = this.canStartTask(hierarchy, a.id) ? 1 : 0;
      const bCanStart = this.canStartTask(hierarchy, b.id) ? 1 : 0;
      
      if (aCanStart !== bCanStart) {
        return bCanStart - aCanStart; // Tasks that can start first
      }
      
      // Priority level
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      const aPriority = priorityOrder[a.priority || 'low'];
      const bPriority = priorityOrder[b.priority || 'low'];
      
      if (aPriority !== bPriority) {
        return bPriority - aPriority; // Higher priority first
      }
      
      // Estimated time (shorter tasks first)
      const aTime = a.estimatedTime || 0;
      const bTime = b.estimatedTime || 0;
      
      return aTime - bTime;
    });
  }
  
  /**
   * Find critical path tasks (tasks that block others)
   */
  findCriticalPath(hierarchy: TaskHierarchy): string[] {
    const criticalTasks = new Set<string>();
    const allTasks: { task: TaskItem, parent?: TaskItem }[] = [];
    
    // Collect all tasks
    for (const phase of hierarchy.plan.phases) {
      for (const task of phase.tasks) {
        allTasks.push({ task });
        // Add subtasks
        for (const subtask of task.subtasks) {
          allTasks.push({ task: subtask, parent: task });
        }
      }
    }
    
    // Find tasks that are dependencies of other tasks
    for (const { task } of allTasks) {
      for (const { task: otherTask } of allTasks) {
        if (otherTask.dependencies.includes(task.id)) {
          criticalTasks.add(task.id);
        }
      }
    }
    
    return Array.from(criticalTasks);
  }
}
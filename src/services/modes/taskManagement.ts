/**
 * Task Management Mode
 * Structured workflow execution and progress tracking
 */

import { BaseMode, ModeContext, ModeBehavior } from './types';
import type { SuperClaudeMode } from '@/types/superClaude';

export class TaskManagementMode extends BaseMode {
  id: SuperClaudeMode = 'task-management';
  name = 'Task Management Mode';
  description = 'Hierarchical task organization mindset for complex multi-step operations';
  
  activationTriggers = [
    'task',
    'workflow',
    'project',
    'implement',
    'build',
    'create feature',
    'multi-step',
    'complex operation',
    'coordinate',
    'manage'
  ];
  
  behaviorChanges = [
    'Hierarchical task breakdown',
    'Progress tracking and updates',
    'Dependency management',
    'Phase-based execution',
    'Systematic organization'
  ];
  
  symbolsEnabled = true;
  
  // Task management markers
  private taskMarkers = {
    plan: '📋',      // Overall plan
    phase: '🎯',     // Major phases
    task: '📦',      // Individual tasks
    todo: '✓',       // Atomic todos
    blocked: '🚧',   // Blocked items
    progress: '🔄'   // In progress
  };
  
  // Task hierarchy levels
  private hierarchyLevels = {
    plan: { depth: 0, prefix: '📋' },
    phase: { depth: 1, prefix: '🎯' },
    task: { depth: 2, prefix: '📦' },
    todo: { depth: 3, prefix: '✓' }
  };

  shouldActivate(context: ModeContext): number {
    let score = 0;
    
    // High activation for multi-step operations
    if (context.fileCount && context.fileCount > 3) {
      score += 0.6;
    }
    
    if (context.directoryCount && context.directoryCount > 2) {
      score += 0.5;
    }
    
    // Activate for complex operations
    if (context.complexity && context.complexity > 0.6) {
      score += 0.5;
    }
    
    // Activate for multi-tool operations
    if (context.multiToolOperation) {
      score += 0.4;
    }
    
    // Keyword activation
    const keywords = context.keywords || [];
    const taskTerms = ['task', 'workflow', 'project', 'implement', 'build', 'coordinate'];
    if (keywords.some(k => taskTerms.some(t => k.toLowerCase().includes(t)))) {
      score += 0.3;
    }
    
    return Math.min(score, 1.0);
  }
  
  applyBehaviors(): ModeBehavior[] {
    return [
      {
        type: 'delegation',
        enabled: true,
        config: {
          hierarchicalBreakdown: true,
          progressTracking: true,
          dependencyManagement: true,
          phaseExecution: true
        }
      },
      {
        type: 'symbol',
        enabled: true,
        config: {
          useTaskMarkers: true,
          hierarchyVisualization: true,
          compressionLevel: 1 // Moderate compression
        }
      },
      {
        type: 'orchestration',
        enabled: true,
        config: {
          taskCoordination: true,
          parallelExecution: true,
          resourceAllocation: true
        }
      }
    ];
  }
  
  getConfig(): Record<string, any> {
    return {
      executionMode: 'hierarchical',
      trackingLevel: 'detailed',
      updateFrequency: 'real-time',
      parallelization: true,
      checkpointingEnabled: true,
      markers: this.taskMarkers,
      hierarchy: this.hierarchyLevels,
      executionStrategy: {
        planning: 'Break down into manageable tasks',
        organization: 'Group by phases and dependencies',
        execution: 'Track progress systematically',
        validation: 'Verify completion at each level',
        coordination: 'Manage dependencies and parallel work'
      },
      thresholds: {
        delegationTrigger: 3,  // Delegate when >3 parallel tasks
        phaseSize: 5,          // Max tasks per phase
        checkpointInterval: 30 // Minutes between checkpoints
      }
    };
  }
  
  /**
   * Generate task hierarchy
   */
  generateHierarchy(goal: string, context: ModeContext): Record<string, any> {
    const markers = this.taskMarkers;
    const complexity = context.complexity || 0.5;
    
    const hierarchy = {
      plan: {
        id: 'plan_main',
        name: goal,
        marker: markers.plan,
        phases: [] as any[]
      }
    };
    
    // Generate phases based on complexity
    const phaseCount = Math.ceil(complexity * 5);
    
    for (let i = 1; i <= phaseCount; i++) {
      const phase = {
        id: `phase_${i}`,
        name: `Phase ${i}`,
        marker: markers.phase,
        tasks: [] as any[],
        dependencies: i > 1 ? [`phase_${i-1}`] : []
      };
      
      // Generate tasks per phase
      const taskCount = Math.ceil(3 + complexity * 2);
      for (let j = 1; j <= taskCount; j++) {
        phase.tasks.push({
          id: `task_${i}_${j}`,
          name: `Task ${i}.${j}`,
          marker: markers.task,
          todos: [],
          status: 'pending'
        });
      }
      
      hierarchy.plan.phases.push(phase);
    }
    
    return hierarchy;
  }
  
  /**
   * Generate execution plan
   */
  generateExecutionPlan(hierarchy: Record<string, any>): string {
    const markers = this.taskMarkers;
    
    let plan = `${markers.plan} **Execution Plan**\n\n`;
    
    for (const phase of hierarchy.plan.phases) {
      plan += `${markers.phase} **${phase.name}**\n`;
      
      for (const task of phase.tasks) {
        const statusMarker = task.status === 'completed' ? '✅' :
                           task.status === 'in_progress' ? markers.progress :
                           task.status === 'blocked' ? markers.blocked : '⏳';
        
        plan += `  ${markers.task} ${task.name} ${statusMarker}\n`;
        
        for (const todo of task.todos || []) {
          plan += `    ${markers.todo} ${todo.name}\n`;
        }
      }
      
      plan += '\n';
    }
    
    return plan;
  }
  
  /**
   * Calculate progress metrics
   */
  calculateProgress(hierarchy: Record<string, any>): Record<string, number> {
    let totalTasks = 0;
    let completedTasks = 0;
    let inProgressTasks = 0;
    let blockedTasks = 0;
    
    for (const phase of hierarchy.plan.phases) {
      for (const task of phase.tasks) {
        totalTasks++;
        if (task.status === 'completed') completedTasks++;
        if (task.status === 'in_progress') inProgressTasks++;
        if (task.status === 'blocked') blockedTasks++;
      }
    }
    
    return {
      total: totalTasks,
      completed: completedTasks,
      inProgress: inProgressTasks,
      blocked: blockedTasks,
      percentComplete: totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0
    };
  }
}
/**
 * Orchestration Mode
 * Intelligent tool selection and resource management
 */

import { BaseMode, ModeContext, ModeBehavior } from './types';
import type { SuperClaudeMode } from '@/types/superClaude';

export class OrchestrationMode extends BaseMode {
  id: SuperClaudeMode = 'orchestration';
  name = 'Orchestration Mode';
  description = 'Intelligent tool selection mindset for optimal task routing and resource efficiency';
  
  activationTriggers = [
    'multi-tool',
    'parallel execution',
    'optimize workflow',
    'resource management',
    'complex routing',
    'performance constraints',
    'orchestrate',
    'coordinate'
  ];
  
  behaviorChanges = [
    'Smart tool selection based on task type',
    'Resource awareness and adaptation',
    'Parallel execution identification',
    'Efficiency-focused approach',
    'Adaptive strategy based on constraints'
  ];
  
  symbolsEnabled = true;
  
  // Resource zones for management
  private resourceZones = {
    green: { min: 0, max: 75, behavior: 'full' },
    yellow: { min: 75, max: 85, behavior: 'efficient' },
    red: { min: 85, max: 100, behavior: 'essential' }
  };
  
  // Tool selection matrix
  private toolMatrix = {
    'ui-components': { primary: 'Magic MCP', alternative: 'Manual coding' },
    'deep-analysis': { primary: 'Sequential MCP', alternative: 'Native reasoning' },
    'symbol-operations': { primary: 'Serena MCP', alternative: 'Manual search' },
    'pattern-edits': { primary: 'Morphllm MCP', alternative: 'Individual edits' },
    'documentation': { primary: 'Context7 MCP', alternative: 'Web search' },
    'browser-testing': { primary: 'Playwright MCP', alternative: 'Unit tests' },
    'multi-file-edits': { primary: 'MultiEdit', alternative: 'Sequential Edits' }
  };

  shouldActivate(context: ModeContext): number {
    let score = 0;
    
    // High activation for multi-tool operations
    if (context.multiToolOperation) {
      score += 0.7;
    }
    
    // Activate for resource constraints
    if (context.resourceUsage && context.resourceUsage > 75) {
      score += 0.6;
    }
    
    // Activate for parallel execution opportunities
    if (context.fileCount && context.fileCount > 3) {
      score += 0.5;
    }
    
    // Activate for complex routing
    if (context.complexity && context.complexity > 0.6) {
      score += 0.4;
    }
    
    // Keyword activation
    const keywords = context.keywords || [];
    if (keywords.some(k => this.activationTriggers.some(t => k.toLowerCase().includes(t)))) {
      score += 0.3;
    }
    
    return Math.min(score, 1.0);
  }
  
  applyBehaviors(): ModeBehavior[] {
    const behaviors: ModeBehavior[] = [];
    
    behaviors.push({
      type: 'orchestration',
      enabled: true,
      config: {
        toolMatrix: this.toolMatrix,
        parallelExecution: true,
        batchOperations: true,
        smartRouting: true
      }
    });
    
    behaviors.push({
      type: 'compression',
      enabled: true,
      config: {
        level: 2, // Moderate compression for efficiency
        preserveClarity: true
      }
    });
    
    behaviors.push({
      type: 'delegation',
      enabled: true,
      config: {
        autoDelegate: true,
        thresholds: {
          files: 50,
          directories: 7,
          complexity: 0.8
        }
      }
    });
    
    return behaviors;
  }
  
  getConfig(): Record<string, any> {
    return {
      resourceZones: this.resourceZones,
      toolMatrix: this.toolMatrix,
      parallelExecutionThreshold: 3,
      batchingEnabled: true,
      adaptiveStrategy: true,
      performanceTargets: {
        responseTime: '<100ms',
        tokenEfficiency: '30-50% reduction',
        parallelization: 'maximize'
      },
      delegationRules: {
        autoEnable: true,
        fileThreshold: 50,
        directoryThreshold: 7,
        complexityThreshold: 0.8
      }
    };
  }
  
  /**
   * Determine resource zone based on usage
   */
  getResourceZone(usage: number): string {
    if (usage <= this.resourceZones.green.max) return 'green';
    if (usage <= this.resourceZones.yellow.max) return 'yellow';
    return 'red';
  }
  
  /**
   * Select optimal tool for task
   */
  selectTool(taskType: string): { primary: string; alternative: string } {
    const matrix = this.toolMatrix as any;
    return matrix[taskType] || { 
      primary: 'Native tools', 
      alternative: 'Manual approach' 
    };
  }
  
  /**
   * Generate orchestration strategy
   */
  generateStrategy(context: ModeContext): string {
    const zone = this.getResourceZone(context.resourceUsage || 0);
    const parallelOps = (context.fileCount || 0) > 3;
    
    return `
## Orchestration Strategy

**Resource Zone**: ${zone.toUpperCase()} (${context.resourceUsage || 0}% usage)
**Parallel Execution**: ${parallelOps ? 'ENABLED' : 'DISABLED'}
**Batch Operations**: ${context.fileCount && context.fileCount > 10 ? 'RECOMMENDED' : 'OPTIONAL'}

### Tool Selection:
- Primary tools selected based on task optimization
- Fallback strategies prepared for all operations
- Resource-aware execution planning

### Execution Plan:
1. Identify independent operations for parallel execution
2. Batch similar operations for efficiency
3. Optimize tool selection for each task type
4. Monitor resource usage and adapt strategy
`;
  }
}
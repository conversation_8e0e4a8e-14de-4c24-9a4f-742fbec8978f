/**
 * Token Efficiency Mode
 * Symbol-enhanced communication for compressed clarity
 */

import { BaseMode, ModeContext, ModeBehavior } from './types';
import type { SuperClaudeMode } from '@/types/superClaude';
import { serenaMCP } from '@/services/mcp/serena';

export class TokenEfficiencyMode extends BaseMode {
  id: SuperClaudeMode = 'token-efficiency';
  name = 'Token Efficiency Mode';
  description = 'Symbol-enhanced communication mindset for optimal token usage';
  
  activationTriggers = [
    'ultracompressed',
    'uc',
    'brief',
    'concise',
    'efficient',
    'token limit',
    'resource constraint',
    'optimize tokens'
  ];
  
  behaviorChanges = [
    'Symbol-based communication',
    'Aggressive abbreviation',
    '30-50% token reduction',
    'Structured bullet points',
    'Minimal verbosity'
  ];
  
  symbolsEnabled = true;
  
  // Core logic symbols
  private logicSymbols = {
    implies: '→',      // leads to, implies
    transforms: '⇒',   // transforms to
    reverse: '←',      // rollback, reverse
    bidirectional: '⇄', // bidirectional
    and: '&',          // combine
    or: '|',           // separator
    define: ':',       // specify
    sequence: '»',     // then
    therefore: '∴',    // conclusion
    because: '∵'       // reason
  };
  
  // Status symbols
  private statusSymbols = {
    completed: '✅',
    failed: '❌',
    warning: '⚠️',
    inProgress: '🔄',
    pending: '⏳',
    critical: '🚨',
    target: '🎯',
    metrics: '📊',
    insight: '💡'
  };
  
  // Technical domain symbols
  private domainSymbols = {
    performance: '⚡',
    analysis: '🔍',
    config: '🔧',
    security: '🛡️',
    deployment: '📦',
    design: '🎨',
    network: '🌐',
    mobile: '📱',
    architecture: '🏗️',
    components: '🧩'
  };
  
  // Common abbreviations
  private abbreviations = {
    // System & Architecture
    'configuration': 'cfg',
    'implementation': 'impl',
    'architecture': 'arch',
    'performance': 'perf',
    'operations': 'ops',
    'environment': 'env',
    
    // Development
    'requirements': 'req',
    'dependencies': 'deps',
    'validation': 'val',
    'testing': 'test',
    'documentation': 'docs',
    'standards': 'std',
    
    // Quality
    'quality': 'qual',
    'security': 'sec',
    'error': 'err',
    'recovery': 'rec',
    'severity': 'sev',
    'optimization': 'opt'
  };

  shouldActivate(context: ModeContext): number {
    let score = 0;
    
    // High activation for resource constraints
    if (context.resourceUsage && context.resourceUsage > 75) {
      score += 0.8;
    }
    
    // Activate for large-scale operations
    if (context.fileCount && context.fileCount > 50) {
      score += 0.6;
    }
    
    // Activate for complex operations needing efficiency
    if (context.complexity && context.complexity > 0.7) {
      score += 0.4;
    }
    
    // Keyword activation
    const keywords = context.keywords || [];
    const efficiencyTerms = ['brief', 'concise', 'efficient', 'compressed', 'uc'];
    if (keywords.some(k => efficiencyTerms.some(t => k.toLowerCase().includes(t)))) {
      score += 0.5;
    }
    
    return Math.min(score, 1.0);
  }
  
  applyBehaviors(): ModeBehavior[] {
    return [
      {
        type: 'compression',
        enabled: true,
        config: {
          level: 3, // Maximum compression
          preserveClarity: true,
          targetReduction: 0.4, // 40% reduction target
          useSymbols: true,
          useAbbreviations: true
        }
      },
      {
        type: 'symbol',
        enabled: true,
        config: {
          logicSymbols: this.logicSymbols,
          statusSymbols: this.statusSymbols,
          domainSymbols: this.domainSymbols,
          compressionLevel: 3
        }
      }
    ];
  }
  
  getConfig(): Record<string, any> {
    return {
      compressionTarget: '30-50%',
      qualityPreservation: '≥95%',
      processingTime: '<100ms',
      symbolSystems: {
        logic: this.logicSymbols,
        status: this.statusSymbols,
        domain: this.domainSymbols
      },
      abbreviationMap: this.abbreviations,
      compressionLevels: {
        minimal: { range: '0-40%', detail: 'full' },
        efficient: { range: '40-70%', detail: 'balanced' },
        compressed: { range: '70-85%', detail: 'essential' },
        critical: { range: '85-95%', detail: 'minimum' },
        emergency: { range: '95%+', detail: 'ultra' }
      },
      strategies: {
        symbolSubstitution: 'Replace verbose text with symbols',
        abbreviationUsage: 'Use standard abbreviations',
        structuralOptimization: 'Bullet points over paragraphs',
        contextPreservation: 'Maintain essential context',
        qualityValidation: 'Verify information preservation'
      }
    };
  }
  
  /**
   * Compress text using symbols and abbreviations
   */
  compressText(text: string): string {
    let compressed = text;
    
    // Replace common phrases with symbols
    compressed = compressed.replace(/leads to|implies/gi, this.logicSymbols.implies);
    compressed = compressed.replace(/transforms to/gi, this.logicSymbols.transforms);
    compressed = compressed.replace(/therefore/gi, this.logicSymbols.therefore);
    compressed = compressed.replace(/because/gi, this.logicSymbols.because);
    
    // Replace status words
    compressed = compressed.replace(/completed|done|finished/gi, this.statusSymbols.completed);
    compressed = compressed.replace(/failed|error/gi, this.statusSymbols.failed);
    compressed = compressed.replace(/warning|caution/gi, this.statusSymbols.warning);
    compressed = compressed.replace(/in progress|working/gi, this.statusSymbols.inProgress);
    
    // Apply abbreviations
    for (const [full, abbr] of Object.entries(this.abbreviations)) {
      const regex = new RegExp(`\\b${full}\\b`, 'gi');
      compressed = compressed.replace(regex, abbr);
    }
    
    return compressed;
  }
  
  /**
   * Generate compression report
   */
  generateCompressionReport(original: string, compressed: string): string {
    const originalTokens = original.split(/\s+/).length;
    const compressedTokens = compressed.split(/\s+/).length;
    const reduction = ((originalTokens - compressedTokens) / originalTokens) * 100;
    
    return `
${this.statusSymbols.metrics} **Compression Report**
Original: ${originalTokens} tokens
Compressed: ${compressedTokens} tokens
Reduction: ${reduction.toFixed(1)}%
Quality: ${reduction < 50 ? '✅ Preserved' : '⚠️ Review needed'}
`;
  }
  
  /**
   * Get compression level based on resource usage
   */
  getCompressionLevel(resourceUsage: number): number {
    if (resourceUsage < 40) return 0;  // Minimal
    if (resourceUsage < 70) return 1;  // Efficient
    if (resourceUsage < 85) return 2;  // Compressed
    if (resourceUsage < 95) return 3;  // Critical
    return 4; // Emergency
  }

  /**
   * Context compression algorithm using Serena MCP for symbol operations
   */
  async compressWithContext(text: string, context: any): Promise<string> {
    // Use Serena MCP to analyze and compress context
    const analysis = await serenaMCP.execute({
      type: 'analyze',
      path: context.projectPath || '.',
    });

    if (analysis.success) {
      // Get project memory to identify repeated patterns
      const memory = serenaMCP.getProjectMemorySummary();
      
      // Apply context-aware compression
      let compressed = text;
      
      // Replace known symbols from project context
      if (memory.projects && memory.projects.length > 0) {
        const project = memory.projects[0];
        // This would be enhanced with actual project-specific symbols
        compressed = this.applyProjectSymbols(compressed, project);
      }
      
      // Apply general compression
      compressed = this.compressText(compressed);
      
      return compressed;
    }
    
    // Fallback to standard compression
    return this.compressText(text);
  }

  /**
   * Apply project-specific symbols for better compression
   */
  private applyProjectSymbols(text: string, _projectContext: any): string {
    let compressed = text;
    
    // In a real implementation, this would use project-specific symbols
    // For now, we'll just apply general symbol compression
    return compressed;
  }

  /**
   * Token usage monitoring
   */
  monitorTokenUsage(currentTokens: number, maxTokens: number): {
    usagePercentage: number;
    status: 'normal' | 'warning' | 'critical';
    message: string;
  } {
    const usagePercentage = (currentTokens / maxTokens) * 100;
    let status: 'normal' | 'warning' | 'critical' = 'normal';
    let message = 'Token usage within normal range';
    
    if (usagePercentage > 90) {
      status = 'critical';
      message = '🚨 Critical token usage - Consider compression';
    } else if (usagePercentage > 75) {
      status = 'warning';
      message = '⚠️ High token usage - Efficiency mode recommended';
    }
    
    return {
      usagePercentage,
      status,
      message: `${this.statusSymbols.metrics} ${message} (${usagePercentage.toFixed(1)}%)`
    };
  }

  /**
   * Smart context trimming based on importance and recency
   */
  trimContext(messages: any[], maxTokens: number): any[] {
    // Calculate current token usage
    const currentTokens = messages.reduce((total, msg) => {
      const content = msg.content || '';
      return total + content.split(/\s+/).length;
    }, 0);
    
    // If already under limit, return as is
    if (currentTokens <= maxTokens) {
      return messages;
    }
    
    // Sort messages by importance and recency
    const sortedMessages = [...messages].sort((a, b) => {
      // Prioritize recent messages (higher timestamps first)
      const timeDiff = (b.timestamp || 0) - (a.timestamp || 0);
      
      // Prioritize important message types
      const importantTypes = ['user', 'assistant'];
      const aImportant = importantTypes.includes(a.role || '');
      const bImportant = importantTypes.includes(b.role || '');
      
      if (aImportant && !bImportant) return -1;
      if (!aImportant && bImportant) return 1;
      
      return timeDiff;
    });
    
    // Trim messages until under token limit
    let trimmedMessages = [...sortedMessages];
    let tokenCount = currentTokens;
    
    while (tokenCount > maxTokens && trimmedMessages.length > 1) {
      const removedMessage = trimmedMessages.pop();
      if (removedMessage) {
        const content = removedMessage.content || '';
        tokenCount -= content.split(/\s+/).length;
      }
    }
    
    return trimmedMessages;
  }
}
/**
 * Mode System Types
 * Core definitions for SuperClaude operational modes
 */

import type { SuperClaudeMode } from '@/types/superClaude';

export interface ModeContext {
  resourceUsage?: number;
  complexity?: number;
  multiToolOperation?: boolean;
  errorRecovery?: boolean;
  fileCount?: number;
  directoryCount?: number;
  keywords?: string[];
  domain?: string;
}

export interface ModeActivationResult {
  mode: SuperClaudeMode;
  confidence: number;
  reason: string;
  behaviors: ModeBehavior[];
}

export interface ModeBehavior {
  type: 'symbol' | 'compression' | 'transparency' | 'delegation' | 'orchestration' | 'introspection';
  enabled: boolean;
  config?: Record<string, any>;
}

export abstract class BaseMode {
  abstract id: SuperClaudeMode;
  abstract name: string;
  abstract description: string;
  abstract activationTriggers: string[];
  abstract behaviorChanges: string[];
  abstract symbolsEnabled: boolean;
  
  /**
   * Evaluate if this mode should activate
   */
  abstract shouldActivate(context: ModeContext): number;
  
  /**
   * Apply mode-specific behaviors
   */
  abstract applyBehaviors(): ModeBehavior[];
  
  /**
   * Get mode-specific configuration
   */
  abstract getConfig(): Record<string, any>;
}

export interface ModeTransition {
  from: SuperClaudeMode;
  to: SuperClaudeMode;
  reason: string;
  timestamp: number;
}

export interface ModeState {
  activeMode: SuperClaudeMode;
  previousMode?: SuperClaudeMode;
  transitions: ModeTransition[];
  behaviors: ModeBehavior[];
  startTime: number;
}
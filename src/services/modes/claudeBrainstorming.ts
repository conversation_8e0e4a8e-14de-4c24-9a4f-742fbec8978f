/**
 * Claude <PERSON>storming Service
 * Connects brainstorming mode to <PERSON>'s creative prompting strategies
 */

import type { BrainstormingSession, BrainstormingIdea } from './brainstorming';

export class ClaudeBrainstormingService {
  /**
   * Generate Claude-style creative prompts for brainstorming
   */
  generateCreativePrompts(topic: string, session: BrainstormingSession): string[] {
    const prompts: string[] = [];
    
    // Perspective-based prompts
    prompts.push(`From the perspective of a ${this.getRandomExpert()}, how would you approach ${topic}?`);
    prompts.push(`If you were explaining ${topic} to a 5-year-old, what simple solutions would emerge?`);
    prompts.push(`How would ${this.getRandomHistoricalFigure()} solve ${topic} if they had modern technology?`);
    
    // Constraint-based prompts
    prompts.push(`How could you solve ${topic} with zero budget?`);
    prompts.push(`What if you had to solve ${topic} in under 24 hours?`);
    prompts.push(`How would you approach ${topic} if you couldn't use digital tools?`);
    
    // Analogy-based prompts
    prompts.push(`What natural phenomenon behaves similarly to ${topic}, and what can we learn from it?`);
    prompts.push(`How is ${topic} like a ${this.getRandomNaturalObject()}? What insights does this comparison reveal?`);
    
    // Provocative prompts
    prompts.push(`What if the opposite approach to our current thinking about ${topic} was actually better?`);
    prompts.push(`What assumptions about ${topic} might be completely wrong?`);
    prompts.push(`If solving ${topic} was a game, what would be the winning strategy?`);
    
    return prompts;
  }
  
  /**
   * Enhance idea with Claude's creative techniques
   */
  enhanceIdea(idea: BrainstormingIdea): BrainstormingIdea {
    // Add creative dimensions to the idea
    const enhancedIdea = { ...idea };
    
    // Add potential improvements if not already present
    if (!enhancedIdea.metadata) {
      enhancedIdea.metadata = {};
    }
    
    // Add creative perspectives
    enhancedIdea.metadata.perspectives = this.generatePerspectives(idea.content);
    
    // Add potential challenges
    enhancedIdea.metadata.challenges = this.identifyChallenges(idea.content);
    
    // Add implementation considerations
    enhancedIdea.metadata.considerations = this.getImplementationConsiderations(idea.content);
    
    return enhancedIdea;
  }
  
  /**
   * Generate creative perspectives on an idea
   */
  private generatePerspectives(content: string): string[] {
    const perspectives = [
      `User Experience: How would this affect the end user's daily experience?`,
      `Business Impact: What are the potential revenue or cost implications?`,
      `Technical Feasibility: What engineering challenges might arise?`,
      `Ethical Considerations: Are there any moral implications to consider?`,
      `Long-term Consequences: How might this idea evolve or affect things 5 years from now?`,
      `Cross-cultural Relevance: How would this work in different cultural contexts?`,
      `Sustainability: What are the environmental implications?`,
      `Scalability: How would this idea work at 10x or 100x scale?`
    ];
    
    // Return a random selection of perspectives
    return this.getRandomSelection(perspectives, 3);
  }
  
  /**
   * Identify potential challenges for an idea
   */
  private identifyChallenges(content: string): string[] {
    const challenges = [
      `Technical complexity or implementation difficulty`,
      `Resource constraints (time, budget, personnel)`,
      `User adoption or change resistance`,
      `Integration with existing systems`,
      `Regulatory or compliance issues`,
      `Security and privacy concerns`,
      `Market competition or timing`,
      `Scalability limitations`
    ];
    
    return this.getRandomSelection(challenges, 2);
  }
  
  /**
   * Get implementation considerations
   */
  private getImplementationConsiderations(content: string): string[] {
    const considerations = [
      `Proof of concept or prototype approach`,
      `Minimum viable product (MVP) strategy`,
      `Phased rollout or pilot program`,
      `Stakeholder communication plan`,
      `Success metrics and KPIs`,
      `Risk mitigation strategies`,
      `Resource allocation requirements`,
      `Timeline and milestone planning`
    ];
    
    return this.getRandomSelection(considerations, 2);
  }
  
  /**
   * Idea refinement using Claude's analytical approach
   */
  refineIdeas(ideas: BrainstormingIdea[]): BrainstormingIdea[] {
    return ideas.map(idea => {
      // Enhance each idea with additional context
      const refinedIdea = this.enhanceIdea(idea);
      
      // Add a quality score based on completeness
      refinedIdea.score = this.calculateIdeaScore(refinedIdea);
      
      return refinedIdea;
    });
  }
  
  /**
   * Calculate idea quality score
   */
  private calculateIdeaScore(idea: BrainstormingIdea): number {
    let score = idea.score || 5; // Start with existing score or default
    
    // Boost score based on metadata completeness
    if (idea.metadata?.perspectives) score += 1;
    if (idea.metadata?.challenges) score += 1;
    if (idea.metadata?.considerations) score += 1;
    
    // Cap at 10
    return Math.min(10, score);
  }
  
  /**
   * Generate SCAMPER-inspired prompts for idea expansion
   */
  generateSCAMPERPrompts(topic: string): Record<string, string> {
    return {
      substitute: `What could you substitute in ${topic} to make it better or different?`,
      combine: `What could you combine with ${topic} to create something new?`,
      adapt: `What else is like ${topic}? What ideas from other domains could be adapted?`,
      modify: `What attributes of ${topic} could be changed or modified?`,
      put_to_other_uses: `How else could ${topic} be used or applied?`,
      eliminate: `What could be eliminated from ${topic} without compromising its core?`,
      reverse: `What would happen if you reversed or rearranged the components of ${topic}?`
    };
  }
  
  /**
   * Utility functions
   */
  private getRandomSelection(array: string[], count: number): string[] {
    const shuffled = [...array].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
  }
  
  private getRandomExpert(): string {
    const experts = [
      'UX designer', 'software architect', 'product manager', 
      'anthropologist', 'futurist', 'psychologist', 
      'environmental scientist', 'economist'
    ];
    return experts[Math.floor(Math.random() * experts.length)];
  }
  
  private getRandomHistoricalFigure(): string {
    const figures = [
      'Leonardo da Vinci', 'Marie Curie', 'Benjamin Franklin',
      'Ada Lovelace', 'Nikola Tesla', 'Frida Kahlo',
      'Confucius', 'Mahatma Gandhi'
    ];
    return figures[Math.floor(Math.random() * figures.length)];
  }
  
  private getRandomNaturalObject(): string {
    const objects = [
      'tree', 'river', 'mountain', 'bird migration', 
      'ant colony', 'coral reef', 'weather system'
    ];
    return objects[Math.floor(Math.random() * objects.length)];
  }
}

// Export singleton instance
export const claudeBrainstormingService = new ClaudeBrainstormingService();
/**
 * Business Panel Mode
 * Multi-expert business analysis mode with adaptive interaction strategies
 */

import { BaseMode, ModeContext, ModeBehavior } from './types';
import type { SuperClaudeMode } from '@/types/superClaude';

export class BusinessPanelMode extends BaseMode {
  id: SuperClaudeMode = 'business-panel';
  name = 'Business Panel Analysis';
  description = 'Multi-expert business analysis with strategic frameworks';
  
  activationTriggers = [
    'business-panel',
    'strategic analysis',
    'market analysis',
    'business model',
    'competitive strategy',
    'innovation assessment',
    'risk analysis',
    'organizational change'
  ];
  
  behaviorChanges = [
    'Activate multiple business expert personas',
    'Apply strategic frameworks (<PERSON>, <PERSON>, etc.)',
    'Enable cross-framework synthesis',
    'Generate business-focused insights',
    'Use professional business communication'
  ];
  
  symbolsEnabled = true;
  
  shouldActivate(context: ModeContext): number {
    let score = 0;
    
    // Check for business keywords
    const businessKeywords = [
      'strategy', 'business', 'market', 'competitive',
      'innovation', 'disruption', 'value', 'model',
      'risk', 'organizational', 'management', 'executive'
    ];
    
    if (context.keywords) {
      const keywordMatches = context.keywords.filter(k => 
        businessKeywords.some(bk => k.toLowerCase().includes(bk))
      ).length;
      score += Math.min(keywordMatches * 0.2, 0.6);
    }
    
    // Check for document analysis context
    if (context.domain === 'business' || context.domain === 'strategy') {
      score += 0.4;
    }
    
    // Check for complexity suggesting strategic analysis
    if (context.complexity && context.complexity > 0.6) {
      score += 0.2;
    }
    
    return Math.min(score, 1.0);
  }
  
  applyBehaviors(): ModeBehavior[] {
    return [
      {
        type: 'symbol',
        enabled: true,
        config: {
          symbolSet: 'business',
          density: 'rich',
          frameworks: true
        }
      },
      {
        type: 'orchestration',
        enabled: true,
        config: {
          experts: ['porter', 'christensen', 'drucker', 'meadows'],
          interactionMode: 'discussion',
          synthesisRequired: true
        }
      },
      {
        type: 'compression',
        enabled: true,
        config: {
          level: 'professional',
          preserveFrameworks: true,
          executiveSummary: true
        }
      }
    ];
  }
  
  getConfig(): Record<string, any> {
    return {
      experts: {
        available: [
          'porter',        // Competitive Strategy
          'christensen',   // Disruption Theory
          'drucker',       // Management Philosophy
          'godin',         // Marketing/Remarkability
          'kim_mauborgne', // Blue Ocean Strategy
          'collins',       // Good to Great
          'taleb',         // Antifragility/Risk
          'meadows',       // Systems Thinking
          'doumont'        // Clear Communication
        ],
        defaultSelection: 'auto',
        maxExperts: 5,
        minExperts: 3
      },
      analysisPhases: {
        discussion: {
          enabled: true,
          duration: 'adaptive',
          crossPollination: true
        },
        debate: {
          enabled: true,
          triggers: ['controversial', 'risk', 'decision'],
          respectfulDisagreement: true
        },
        socratic: {
          enabled: true,
          triggers: ['learn', 'understand', 'develop'],
          questionDepth: 'progressive'
        }
      },
      output: {
        structure: 'framework-based',
        symbolDensity: 'rich',
        synthesisRequired: true,
        executiveSummary: true,
        actionableInsights: true
      },
      integration: {
        mcp: {
          primary: ['sequential', 'context7'],
          businessData: true
        },
        personas: {
          coordination: 'business-panel-experts',
          analyticalSupport: ['analyzer', 'architect', 'requirements']
        }
      }
    };
  }
  
  /**
   * Get phase configuration based on content
   */
  getPhaseConfig(content: string): string {
    // Determine which phase to use
    if (content.includes('debate') || content.includes('challenge') || 
        content.includes('controversial') || content.includes('risk')) {
      return 'debate';
    }
    
    if (content.includes('learn') || content.includes('understand') || 
        content.includes('teach') || content.includes('question')) {
      return 'socratic';
    }
    
    // Default to discussion
    return 'discussion';
  }
  
  /**
   * Select experts based on content domain
   */
  selectExperts(content: string, domain?: string): string[] {
    const selectedExperts: string[] = [];
    
    // Domain-based selection
    if (domain === 'strategy' || content.includes('strategy')) {
      selectedExperts.push('porter', 'kim_mauborgne');
    }
    
    if (domain === 'innovation' || content.includes('innovation') || 
        content.includes('disruption')) {
      selectedExperts.push('christensen');
    }
    
    if (content.includes('manage') || content.includes('organization')) {
      selectedExperts.push('drucker');
    }
    
    if (content.includes('risk') || content.includes('uncertainty')) {
      selectedExperts.push('taleb');
    }
    
    // Always include systems thinking
    selectedExperts.push('meadows');
    
    // Add communication expert for clarity
    if (selectedExperts.length < 5) {
      selectedExperts.push('doumont');
    }
    
    // Ensure minimum of 3 experts
    if (selectedExperts.length < 3) {
      const remaining = ['collins', 'godin'].filter(e => 
        !selectedExperts.includes(e)
      );
      selectedExperts.push(...remaining.slice(0, 3 - selectedExperts.length));
    }
    
    // Limit to 5 experts maximum
    return [...new Set(selectedExperts)].slice(0, 5);
  }
}
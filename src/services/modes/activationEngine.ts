/**
 * Mode Activation Engine
 * Intelligent mode detection and transition management
 */

import { BaseMode, ModeContext, ModeActivationResult, ModeState, ModeTransition, ModeBehavior } from './types';
import { IntrospectionMode } from './introspection';
import { OrchestrationMode } from './orchestration';
import { BrainstormingMode } from './brainstorming';
import { TaskManagementMode } from './taskManagement';
import { TokenEfficiencyMode } from './tokenEfficiency';
import type { SuperClaudeMode } from '@/types/superClaude';

export class ModeActivationEngine {
  private modes: Map<SuperClaudeMode, BaseMode> = new Map();
  private currentState: ModeState;
  private transitionHistory: ModeTransition[] = [];
  
  constructor() {
    this.registerDefaultModes();
    this.currentState = {
      activeMode: 'normal',
      behaviors: [],
      transitions: [],
      startTime: Date.now()
    };
  }
  
  private registerDefaultModes() {
    // Register all core modes
    this.registerMode(new IntrospectionMode());
    this.registerMode(new OrchestrationMode());
    this.registerMode(new BrainstormingMode());
    this.registerMode(new TaskManagementMode());
    this.registerMode(new TokenEfficiencyMode());
    // Business panel mode is handled separately through business panel service
  }
  
  registerMode(mode: BaseMode) {
    this.modes.set(mode.id, mode);
  }
  
  /**
   * Detect which mode should be active based on context
   */
  detectMode(prompt: string, context: ModeContext): ModeActivationResult {
    // Extract keywords from prompt
    const keywords = this.extractKeywords(prompt);
    context.keywords = keywords;
    
    // Check for explicit mode flags
    const explicitMode = this.checkExplicitFlags(prompt);
    if (explicitMode) {
      return {
        mode: explicitMode,
        confidence: 1.0,
        reason: 'Explicit flag activation',
        behaviors: this.modes.get(explicitMode)?.applyBehaviors() || []
      };
    }
    
    // Score each mode
    const modeScores: Array<{ mode: SuperClaudeMode; score: number; reason: string }> = [];
    
    // Check special modes first
    for (const [modeId, mode] of this.modes) {
      const score = mode.shouldActivate(context);
      if (score > 0) {
        modeScores.push({
          mode: modeId,
          score,
          reason: this.generateActivationReason(mode, context)
        });
      }
    }
    
    // Check for business panel mode
    if (this.checkBusinessPanel(prompt, context)) {
      modeScores.push({
        mode: 'business-panel',
        score: 0.9,
        reason: 'Business analysis context detected'
      });
    }
    
    // Check for task management mode
    if (this.checkTaskManagement(context)) {
      modeScores.push({
        mode: 'task-management',
        score: 0.8,
        reason: 'Multi-step operation requiring coordination'
      });
    }
    
    // Check for brainstorming mode
    if (this.checkBrainstorming(prompt, context)) {
      modeScores.push({
        mode: 'brainstorming',
        score: 0.7,
        reason: 'Exploration and discovery context'
      });
    }
    
    // Check for token efficiency mode
    if (this.checkTokenEfficiency(context)) {
      modeScores.push({
        mode: 'token-efficiency',
        score: 0.85,
        reason: 'Resource constraints require efficiency'
      });
    }
    
    // Sort by score and select highest
    modeScores.sort((a, b) => b.score - a.score);
    
    if (modeScores.length > 0 && modeScores[0].score > 0.5) {
      const selected = modeScores[0];
      const mode = this.modes.get(selected.mode);
      
      return {
        mode: selected.mode,
        confidence: selected.score,
        reason: selected.reason,
        behaviors: mode?.applyBehaviors() || []
      };
    }
    
    // Default to normal mode
    return {
      mode: 'normal',
      confidence: 1.0,
      reason: 'Default operational mode',
      behaviors: []
    };
  }
  
  /**
   * Transition to a new mode
   */
  transitionTo(newMode: SuperClaudeMode, reason: string): ModeState {
    const transition: ModeTransition = {
      from: this.currentState.activeMode,
      to: newMode,
      reason,
      timestamp: Date.now()
    };
    
    this.transitionHistory.push(transition);
    
    const mode = this.modes.get(newMode);
    const behaviors = mode?.applyBehaviors() || [];
    
    this.currentState = {
      activeMode: newMode,
      previousMode: this.currentState.activeMode,
      behaviors,
      transitions: [...this.currentState.transitions, transition],
      startTime: Date.now()
    };
    
    return this.currentState;
  }
  
  /**
   * Get current mode configuration
   */
  getCurrentModeConfig(): Record<string, any> {
    const mode = this.modes.get(this.currentState.activeMode);
    return mode?.getConfig() || {};
  }
  
  /**
   * Get mode behaviors
   */
  getModeBehaviors(modeId: SuperClaudeMode): ModeBehavior[] {
    const mode = this.modes.get(modeId);
    return mode?.applyBehaviors() || [];
  }
  
  // Helper methods
  
  private extractKeywords(prompt: string): string[] {
    // Extract meaningful keywords from prompt
    const words = prompt.toLowerCase().split(/\s+/);
    return words.filter(w => w.length > 3);
  }
  
  private checkExplicitFlags(prompt: string): SuperClaudeMode | null {
    const flags = {
      '--introspect': 'introspection',
      '--introspection': 'introspection',
      '--orchestrate': 'orchestration',
      '--orchestration': 'orchestration',
      '--brainstorm': 'brainstorming',
      '--task-manage': 'task-management',
      '--token-efficient': 'token-efficiency',
      '--business-panel': 'business-panel',
      '--uc': 'token-efficiency'
    };
    
    for (const [flag, mode] of Object.entries(flags)) {
      if (prompt.includes(flag)) {
        return mode as SuperClaudeMode;
      }
    }
    
    return null;
  }
  
  private checkBusinessPanel(prompt: string, _context: ModeContext): boolean {
    const businessKeywords = [
      'business', 'strategy', 'market', 'competitive', 
      'innovation', 'disruption', 'management', 'organizational'
    ];
    
    return businessKeywords.some(k => prompt.toLowerCase().includes(k)) ||
           prompt.includes('/sc:business-panel') || false;
  }
  
  private checkTaskManagement(context: ModeContext): boolean {
    return Boolean((context.fileCount && context.fileCount > 3) ||
           (context.directoryCount && context.directoryCount > 2) ||
           (context.complexity && context.complexity > 0.7));
  }
  
  private checkBrainstorming(prompt: string, _context: ModeContext): boolean {
    const explorationKeywords = [
      'brainstorm', 'explore', 'discuss', 'figure out',
      'not sure', 'maybe', 'possibly', 'thinking about'
    ];
    
    return explorationKeywords.some(k => prompt.toLowerCase().includes(k)) || false;
  }
  
  private checkTokenEfficiency(context: ModeContext): boolean {
    return Boolean((context.resourceUsage && context.resourceUsage > 75) ||
           (context.fileCount && context.fileCount > 50));
  }
  
  private generateActivationReason(mode: BaseMode, context: ModeContext): string {
    const reasons = [];
    
    if (context.complexity && context.complexity > 0.7) {
      reasons.push('high complexity');
    }
    
    if (context.multiToolOperation) {
      reasons.push('multi-tool operation');
    }
    
    if (context.errorRecovery) {
      reasons.push('error recovery');
    }
    
    if (context.resourceUsage && context.resourceUsage > 75) {
      reasons.push('resource constraints');
    }
    
    if (reasons.length === 0) {
      reasons.push('context match');
    }
    
    return `${mode.name} activated due to ${reasons.join(', ')}`;
  }
  
  /**
   * Get mode by ID
   */
  getMode(modeId: SuperClaudeMode): BaseMode | undefined {
    return this.modes.get(modeId);
  }
  
  /**
   * Get all registered modes
   */
  getAllModes(): BaseMode[] {
    return Array.from(this.modes.values());
  }
  
  /**
   * Get current state
   */
  getCurrentState(): ModeState {
    return this.currentState;
  }
  
  /**
   * Get transition history
   */
  getTransitionHistory(): ModeTransition[] {
    return this.transitionHistory;
  }
}

// Export singleton instance
export const modeActivationEngine = new ModeActivationEngine();
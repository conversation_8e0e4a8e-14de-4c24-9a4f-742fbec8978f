/**
 * Introspection Mode
 * Meta-cognitive analysis and reasoning transparency with self-reflection capabilities
 */

import { BaseMode, ModeContext, ModeBehavior } from './types';
import type { SuperClaudeMode } from '@/types/superClaude';
import { eventBuilders, analytics } from '@/lib/analytics';

// Progress tracking interfaces
export interface SessionProgress {
  totalSteps: number;
  completedSteps: number;
  currentPhase: string;
  startTime: number;
  lastUpdate: number;
}

export interface DecisionLogEntry {
  id: string;
  timestamp: number;
  decision: string;
  context: string;
  rationale: string;
  confidence: number;
  alternatives: string[];
  outcome?: string;
}

export interface PatternRecognitionResult {
  patternId: string;
  patternType: string;
  description: string;
  frequency: number;
  confidence: number;
  recommendations: string[];
}

export interface LearningExtractionResult {
  learningId: string;
  topic: string;
  insight: string;
  application: string;
  confidence: number;
}

export interface DebugSessionContext {
  sessionId: string;
  problemStatement: string;
  hypotheses: string[];
  evidence: any[];
  rootCause?: string;
  solution?: string;
  startTime: number;
}

interface PerformanceMetrics {
  sessionDuration: number;
  tokensUsed: number;
  responseTime: number;
  toolEffectiveness: number;
  efficiencyScore: number;
}

export class IntrospectionMode extends BaseMode {
  id: SuperClaudeMode = 'introspection';
  name = 'Introspection Mode';
  description = 'Meta-cognitive analysis mindset for self-reflection and reasoning optimization';
  
  activationTriggers = [
    'analyze my reasoning',
    'reflect on decision',
    'introspect',
    'self-analysis',
    'meta-cognitive',
    'thinking process',
    'framework analysis',
    'why did I',
    'pattern recognition',
    'debug my approach',
    'analyze performance',
    'track progress'
  ];
  
  behaviorChanges = [
    'Self-examination of decision logic',
    'Transparency markers in responses',
    'Pattern detection in reasoning',
    'Framework compliance validation',
    'Learning focus and improvement',
    'Progress tracking and metrics',
    'Debug session orchestration'
  ];
  
  symbolsEnabled = true;
  
  // Analysis markers for transparency
  private analysisMarkers = {
    reasoning: '🧠',      // Chain of thought examination
    action: '🔄',         // Action sequence review
    assessment: '🎯',     // Self-assessment
    pattern: '📊',        // Pattern recognition
    compliance: '🔍',     // Framework compliance
    insight: '💡',        // Retrospective insight
    progress: '📈',       // Progress tracking
    debug: '🐛',          // Debug analysis
    metrics: '⚡'         // Performance metrics
  };
  
  // Progress tracking state
  private sessionProgress: Map<string, SessionProgress> = new Map();
  private decisionLog: Map<string, DecisionLogEntry[]> = new Map();
  private patternCache: Map<string, PatternRecognitionResult[]> = new Map();
  private learningCache: Map<string, LearningExtractionResult[]> = new Map();
  private debugSessions: Map<string, DebugSessionContext> = new Map();
  private performanceMetrics: Map<string, PerformanceMetrics> = new Map();

  shouldActivate(context: ModeContext): number {
    let score = 0;
    const keywords = context.keywords || [];
    
    // Check for introspection keywords
    const introspectionTerms = ['introspect', 'reflect', 'analyze', 'reasoning', 'meta', 'debug', 'performance', 'track'];
    if (keywords.some(k => introspectionTerms.some(t => k.toLowerCase().includes(t)))) {
      score += 0.7;
    }
    
    // Activate for error recovery
    if (context.errorRecovery) {
      score += 0.5;
    }
    
    // Activate for complex problem solving
    if (context.complexity && context.complexity > 0.7) {
      score += 0.3;
    }
    
    // Activate for framework discussions
    if (keywords.some(k => k.toLowerCase().includes('framework') || k.toLowerCase().includes('superclaude'))) {
      score += 0.4;
    }
    
    // Activate for debugging contexts
    if (keywords.some(k => k.toLowerCase().includes('debug') || k.toLowerCase().includes('error'))) {
      score += 0.4;
    }
    
    // Activate for performance analysis
    if (keywords.some(k => k.toLowerCase().includes('performance') || k.toLowerCase().includes('metrics'))) {
      score += 0.3;
    }
    
    return Math.min(score, 1.0);
  }
  
  applyBehaviors(): ModeBehavior[] {
    return [
      {
        type: 'transparency',
        enabled: true,
        config: {
          markers: this.analysisMarkers,
          showThinkingProcess: true,
          exposeUncertainty: true,
          includeAlternatives: true,
          showProgress: true,
          showMetrics: true
        }
      },
      {
        type: 'symbol',
        enabled: true,
        config: {
          useAnalysisMarkers: true,
          compressionLevel: 1 // Minimal compression for clarity
        }
      },
      {
        type: 'introspection',
        enabled: true,
        config: {
          trackProgress: true,
          logDecisions: true,
          recognizePatterns: true,
          extractLearning: true,
          collectMetrics: true
        }
      }
    ];
  }
  
  getConfig(): Record<string, any> {
    return {
      analysisDepth: 'deep',
      transparencyLevel: 'high',
      includeMetaCognition: true,
      showReasoningChain: true,
      validateFramework: true,
      trackProgress: true,
      collectMetrics: true,
      enableDebugging: true,
      markers: this.analysisMarkers,
      analysisFramework: {
        reasoning: 'Examine logical flow and decision rationale',
        action: 'Analyze tool selection and workflow',
        assessment: 'Evaluate confidence and knowledge gaps',
        pattern: 'Identify recurring behaviors',
        compliance: 'Validate against SuperClaude rules',
        insight: 'Extract learnings and improvements',
        progress: 'Track session advancement and milestones',
        debug: 'Systematic debugging and error analysis',
        metrics: 'Performance and efficiency measurements'
      }
    };
  }
  
  /**
   * Generate introspective analysis
   */
  generateAnalysis(topic: string, context: ModeContext): string {
    const markers = this.analysisMarkers;
    
    return `
${markers.reasoning} **Reasoning Analysis**
Examining the logical flow and decision-making process for: ${topic}

${markers.action} **Action Sequence Review**
Evaluating the effectiveness of chosen tools and workflows

${markers.assessment} **Self-Assessment**
Confidence level: ${context.complexity ? (1 - context.complexity) * 100 : 50}%
Knowledge gaps identified: Areas requiring further investigation

${markers.pattern} **Pattern Recognition**
Recurring behaviors and decision patterns observed

${markers.compliance} **Framework Compliance**
Validating actions against SuperClaude principles and rules

${markers.insight} **Retrospective Insight**
Key learnings and improvement opportunities identified

${markers.progress} **Progress Tracking**
Monitoring advancement and milestone completion

${markers.debug} **Debug Analysis**
Systematic error investigation and root cause analysis

${markers.metrics} **Performance Metrics**
Efficiency measurements and optimization opportunities
`;
  }
  
  /**
   * Generate self-reflection prompt based on context
   */
  generateReflectionPrompt(context: ModeContext, sessionId: string): string {
    const markers = this.analysisMarkers;
    const progress = this.sessionProgress.get(sessionId);
    const decisions = this.decisionLog.get(sessionId) || [];
    
    let prompt = `${markers.reasoning} **Self-Reflection Prompt**\n\n`;
    
    // Context-aware prompting
    if (context.complexity && context.complexity > 0.7) {
      prompt += "Given the complexity of this task, let's examine the reasoning process:\n";
      prompt += "- What assumptions have been made that might need validation?\n";
      prompt += "- Are there alternative approaches that weren't considered?\n\n";
    }
    
    // Progress-aware prompting
    if (progress) {
      const completion = progress.totalSteps > 0 ?
        Math.round((progress.completedSteps / progress.totalSteps) * 100) : 0;
      prompt += `${markers.progress} **Progress Check** (${completion}% complete)\n`;
      prompt += `- Current phase: ${progress.currentPhase}\n`;
      prompt += `- Recent decisions: ${decisions.length} logged\n\n`;
    }
    
    // Decision-aware prompting
    if (decisions.length > 0) {
      const recentDecision = decisions[decisions.length - 1];
      prompt += `${markers.assessment} **Decision Review**\n`;
      prompt += `Recent decision: "${recentDecision.decision}"\n`;
      prompt += `- Confidence: ${Math.round(recentDecision.confidence * 100)}%\n`;
      prompt += `- Alternatives considered: ${recentDecision.alternatives.length}\n\n`;
    }
    
    // Pattern-aware prompting
    const patterns = this.patternCache.get(sessionId) || [];
    if (patterns.length > 0) {
      prompt += `${markers.pattern} **Pattern Recognition**\n`;
      prompt += "Recurring patterns observed:\n";
      patterns.slice(0, 2).forEach(pattern => {
        prompt += `- ${pattern.description} (confidence: ${Math.round(pattern.confidence * 100)}%)\n`;
      });
      prompt += "\n";
    }
    
    prompt += "Reflect on the effectiveness of the current approach and suggest improvements.";
    
    return prompt;
  }
  
  /**
   * Track session progress
   */
  trackProgress(sessionId: string, progress: Partial<SessionProgress>): void {
    const existing = this.sessionProgress.get(sessionId) || {
      totalSteps: 0,
      completedSteps: 0,
      currentPhase: 'initial',
      startTime: Date.now(),
      lastUpdate: Date.now()
    };
    
    const updated = { ...existing, ...progress, lastUpdate: Date.now() };
    this.sessionProgress.set(sessionId, updated);
    
    // Track progress metrics
    analytics.track('introspection_progress_update', {
      sessionId,
      completion: updated.totalSteps > 0 ?
        Math.round((updated.completedSteps / updated.totalSteps) * 100) : 0,
      currentPhase: updated.currentPhase,
      duration: Date.now() - updated.startTime
    });
  }
  
  /**
   * Log decision with rationale
   */
  logDecision(sessionId: string, decision: DecisionLogEntry): void {
    const decisions = this.decisionLog.get(sessionId) || [];
    decisions.push(decision);
    this.decisionLog.set(sessionId, decisions);
    
    // Track decision metrics
    analytics.track('introspection_decision_logged', {
      sessionId,
      decisionId: decision.id,
      confidence: decision.confidence,
      alternatives: decision.alternatives.length
    });
  }
  
  /**
   * Recognize patterns in behavior
   */
  recognizePatterns(sessionId: string, context: ModeContext): PatternRecognitionResult[] {
    const patterns: PatternRecognitionResult[] = [];
    const decisions = this.decisionLog.get(sessionId) || [];
    
    // Simple pattern recognition based on decision context
    if (context.complexity && context.complexity > 0.7) {
      patterns.push({
        patternId: 'high-complexity-decision',
        patternType: 'decision-making',
        description: 'High complexity decision pattern detected',
        frequency: decisions.filter(d => d.confidence < 0.7).length,
        confidence: 0.8,
        recommendations: [
          'Consider breaking down complex decisions into smaller steps',
          'Seek additional validation for low-confidence decisions'
        ]
      });
    }
    
    // Error recovery pattern
    if (context.errorRecovery) {
      patterns.push({
        patternId: 'error-recovery',
        patternType: 'debugging',
        description: 'Error recovery pattern observed',
        frequency: 1,
        confidence: 0.9,
        recommendations: [
          'Document error recovery strategies for future reference',
          'Analyze root causes to prevent recurrence'
        ]
      });
    }
    
    this.patternCache.set(sessionId, patterns);
    
    // Track pattern recognition
    patterns.forEach(pattern => {
      analytics.track('introspection_pattern_recognized', {
        sessionId,
        patternId: pattern.patternId,
        patternType: pattern.patternType,
        confidence: pattern.confidence,
        frequency: pattern.frequency
      });
    });
    
    return patterns;
  }
  
  /**
   * Extract learning from completed tasks
   */
  extractLearning(sessionId: string, context: ModeContext): LearningExtractionResult[] {
    const learnings: LearningExtractionResult[] = [];
    const patterns = this.patternCache.get(sessionId) || [];
    const decisions = this.decisionLog.get(sessionId) || [];
    
    // Extract learning from patterns
    patterns.forEach(pattern => {
      learnings.push({
        learningId: `pattern-${pattern.patternId}`,
        topic: pattern.patternType,
        insight: pattern.description,
        application: pattern.recommendations.join('; '),
        confidence: pattern.confidence
      });
    });
    
    // Extract learning from low-confidence decisions
    decisions
      .filter(d => d.confidence < 0.6)
      .forEach(decision => {
        learnings.push({
          learningId: `decision-${decision.id}`,
          topic: 'decision-making',
          insight: `Low confidence decision: ${decision.decision}`,
          application: 'Consider additional validation or alternative approaches',
          confidence: 1 - decision.confidence
        });
      });
    
    // Context-based learning
    if (context.errorRecovery) {
      learnings.push({
        learningId: 'error-recovery-strategy',
        topic: 'debugging',
        insight: 'Error recovery requires systematic approach',
        application: 'Document and standardize recovery procedures',
        confidence: 0.9
      });
    }
    
    this.learningCache.set(sessionId, learnings);
    
    // Track learning extraction
    learnings.forEach(learning => {
      analytics.track('introspection_learning_extracted', {
        sessionId,
        learningId: learning.learningId,
        topic: learning.topic,
        confidence: learning.confidence
      });
    });
    
    return learnings;
  }
  
  /**
   * Collect performance metrics
   */
  collectPerformanceMetrics(sessionId: string, metrics: Partial<PerformanceMetrics>): PerformanceMetrics {
    const existing = this.performanceMetrics.get(sessionId) || {
      sessionDuration: 0,
      tokensUsed: 0,
      responseTime: 0,
      toolEffectiveness: 0,
      efficiencyScore: 0
    };
    
    const updated = { ...existing, ...metrics };
    this.performanceMetrics.set(sessionId, updated);
    
    // Track performance metrics
    analytics.track('introspection_performance_metrics', {
      sessionId,
      sessionDuration: updated.sessionDuration,
      tokensUsed: updated.tokensUsed,
      responseTime: updated.responseTime,
      toolEffectiveness: updated.toolEffectiveness,
      efficiencyScore: updated.efficiencyScore
    });
    
    return updated;
  }
  
  /**
   * Analyze error patterns for debugging
   */
  analyzeErrorPatterns(sessionId: string, errorContext: any): PatternRecognitionResult[] {
    const patterns: PatternRecognitionResult[] = [];
    
    // Analyze error types and frequencies
    if (errorContext.type) {
      patterns.push({
        patternId: `error-${errorContext.type}`,
        patternType: 'error-analysis',
        description: `Error pattern: ${errorContext.type}`,
        frequency: errorContext.count || 1,
        confidence: errorContext.confidence || 0.8,
        recommendations: errorContext.suggestions || ['Investigate root cause']
      });
    }
    
    // Track error pattern analysis
    patterns.forEach(pattern => {
      analytics.track('introspection_error_pattern_analyzed', {
        sessionId,
        patternId: pattern.patternId,
        errorType: errorContext.type,
        frequency: pattern.frequency
      });
    });
    
    return patterns;
  }
  
  /**
   * Orchestrate debugging session
   */
  orchestrateDebugSession(sessionId: string, problem: string, context: ModeContext): DebugSessionContext {
    const debugSession: DebugSessionContext = {
      sessionId: `debug_${sessionId}_${Date.now()}`,
      problemStatement: problem,
      hypotheses: this.generateHypotheses(problem, context),
      evidence: [],
      startTime: Date.now()
    };
    
    this.debugSessions.set(sessionId, debugSession);
    
    // Track debug session creation
    analytics.track('introspection_debug_session_started', {
      sessionId,
      debugSessionId: debugSession.sessionId,
      problem: problem.substring(0, 100) // Truncate for privacy
    });
    
    return debugSession;
  }
  
  /**
   * Generate debugging hypotheses
   */
  private generateHypotheses(problem: string, context: ModeContext): string[] {
    const hypotheses: string[] = [];
    
    // Context-based hypothesis generation
    if (context.complexity && context.complexity > 0.7) {
      hypotheses.push('Complexity overload - breaking down the problem may help');
    }
    
    if (context.errorRecovery) {
      hypotheses.push('Previous error may have cascaded into current issue');
    }
    
    // General hypotheses
    hypotheses.push('Incorrect assumption about system behavior');
    hypotheses.push('Missing validation or edge case handling');
    hypotheses.push('Resource constraint or timing issue');
    hypotheses.push('Integration or dependency problem');
    
    return hypotheses;
  }
  
  /**
   * Get session insights summary
   */
  getSessionInsights(sessionId: string): string {
    const markers = this.analysisMarkers;
    const progress = this.sessionProgress.get(sessionId);
    const decisions = this.decisionLog.get(sessionId) || [];
    const patterns = this.patternCache.get(sessionId) || [];
    const learnings = this.learningCache.get(sessionId) || [];
    const metrics = this.performanceMetrics.get(sessionId);
    
    let summary = `${markers.insight} **Session Insights Summary**\n\n`;
    
    // Progress summary
    if (progress) {
      const completion = progress.totalSteps > 0 ?
        Math.round((progress.completedSteps / progress.totalSteps) * 100) : 0;
      summary += `${markers.progress} **Progress**: ${completion}% complete\n`;
      summary += `Phase: ${progress.currentPhase}\n\n`;
    }
    
    // Decision summary
    if (decisions.length > 0) {
      summary += `${markers.assessment} **Decisions**: ${decisions.length} logged\n`;
      const avgConfidence = decisions.reduce((sum, d) => sum + d.confidence, 0) / decisions.length;
      summary += `Average confidence: ${Math.round(avgConfidence * 100)}%\n\n`;
    }
    
    // Pattern summary
    if (patterns.length > 0) {
      summary += `${markers.pattern} **Patterns**: ${patterns.length} identified\n`;
      patterns.slice(0, 2).forEach(pattern => {
        summary += `- ${pattern.description}\n`;
      });
      summary += "\n";
    }
    
    // Learning summary
    if (learnings.length > 0) {
      summary += `${markers.insight} **Learnings**: ${learnings.length} extracted\n`;
      learnings.slice(0, 2).forEach(learning => {
        summary += `- ${learning.insight}\n`;
      });
      summary += "\n";
    }
    
    // Metrics summary
    if (metrics) {
      summary += `${markers.metrics} **Performance**:\n`;
      summary += `- Duration: ${Math.round(metrics.sessionDuration / 1000)}s\n`;
      summary += `- Tokens: ${metrics.tokensUsed}\n`;
      summary += `- Efficiency: ${Math.round(metrics.efficiencyScore * 100)}%\n\n`;
    }
    
    return summary;
  }
  
  /**
   * Clear session data
   */
  clearSessionData(sessionId: string): void {
    this.sessionProgress.delete(sessionId);
    this.decisionLog.delete(sessionId);
    this.patternCache.delete(sessionId);
    this.learningCache.delete(sessionId);
    this.debugSessions.delete(sessionId);
    this.performanceMetrics.delete(sessionId);
  }
}
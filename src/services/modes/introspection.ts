/**
 * Introspection Mode
 * Meta-cognitive analysis and reasoning transparency
 */

import { BaseMode, ModeContext, ModeBehavior } from './types';
import type { SuperClaudeMode } from '@/types/superClaude';

export class IntrospectionMode extends BaseMode {
  id: SuperClaudeMode = 'introspection';
  name = 'Introspection Mode';
  description = 'Meta-cognitive analysis mindset for self-reflection and reasoning optimization';
  
  activationTriggers = [
    'analyze my reasoning',
    'reflect on decision',
    'introspect',
    'self-analysis',
    'meta-cognitive',
    'thinking process',
    'framework analysis',
    'why did I',
    'pattern recognition'
  ];
  
  behaviorChanges = [
    'Self-examination of decision logic',
    'Transparency markers in responses',
    'Pattern detection in reasoning',
    'Framework compliance validation',
    'Learning focus and improvement'
  ];
  
  symbolsEnabled = true;
  
  // Analysis markers for transparency
  private analysisMarkers = {
    reasoning: '🧠',      // Chain of thought examination
    action: '🔄',         // Action sequence review
    assessment: '🎯',     // Self-assessment
    pattern: '📊',        // Pattern recognition
    compliance: '🔍',     // Framework compliance
    insight: '💡'         // Retrospective insight
  };

  shouldActivate(context: ModeContext): number {
    let score = 0;
    const keywords = context.keywords || [];
    
    // Check for introspection keywords
    const introspectionTerms = ['introspect', 'reflect', 'analyze', 'reasoning', 'meta'];
    if (keywords.some(k => introspectionTerms.some(t => k.toLowerCase().includes(t)))) {
      score += 0.7;
    }
    
    // Activate for error recovery
    if (context.errorRecovery) {
      score += 0.5;
    }
    
    // Activate for complex problem solving
    if (context.complexity && context.complexity > 0.7) {
      score += 0.3;
    }
    
    // Activate for framework discussions
    if (keywords.some(k => k.toLowerCase().includes('framework') || k.toLowerCase().includes('superclaude'))) {
      score += 0.4;
    }
    
    return Math.min(score, 1.0);
  }
  
  applyBehaviors(): ModeBehavior[] {
    return [
      {
        type: 'transparency',
        enabled: true,
        config: {
          markers: this.analysisMarkers,
          showThinkingProcess: true,
          exposeUncertainty: true,
          includeAlternatives: true
        }
      },
      {
        type: 'symbol',
        enabled: true,
        config: {
          useAnalysisMarkers: true,
          compressionLevel: 1 // Minimal compression for clarity
        }
      }
    ];
  }
  
  getConfig(): Record<string, any> {
    return {
      analysisDepth: 'deep',
      transparencyLevel: 'high',
      includeMetaCognition: true,
      showReasoningChain: true,
      validateFramework: true,
      markers: this.analysisMarkers,
      analysisFramework: {
        reasoning: 'Examine logical flow and decision rationale',
        action: 'Analyze tool selection and workflow',
        assessment: 'Evaluate confidence and knowledge gaps',
        pattern: 'Identify recurring behaviors',
        compliance: 'Validate against SuperClaude rules',
        insight: 'Extract learnings and improvements'
      }
    };
  }
  
  /**
   * Generate introspective analysis
   */
  generateAnalysis(topic: string, context: ModeContext): string {
    const markers = this.analysisMarkers;
    
    return `
${markers.reasoning} **Reasoning Analysis**
Examining the logical flow and decision-making process for: ${topic}

${markers.action} **Action Sequence Review**
Evaluating the effectiveness of chosen tools and workflows

${markers.assessment} **Self-Assessment**
Confidence level: ${context.complexity ? (1 - context.complexity) * 100 : 50}%
Knowledge gaps identified: Areas requiring further investigation

${markers.pattern} **Pattern Recognition**
Recurring behaviors and decision patterns observed

${markers.compliance} **Framework Compliance**
Validating actions against SuperClaude principles and rules

${markers.insight} **Retrospective Insight**
Key learnings and improvement opportunities identified
`;
  }
}
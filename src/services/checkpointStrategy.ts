/**
 * Checkpoint Strategy Analyzer
 * Intelligent checkpoint creation based on operation analysis
 */

export type OperationType = 
  | 'file_create'
  | 'file_delete'
  | 'file_modify'
  | 'directory_create'
  | 'directory_delete'
  | 'config_change'
  | 'dependency_update'
  | 'refactor'
  | 'command_execution'
  | 'error_recovery';

export interface OperationContext {
  type: OperationType;
  path?: string;
  size?: number;
  impact?: 'low' | 'medium' | 'high' | 'critical';
  reversible?: boolean;
  timestamp: number;
}

export interface StrategyDecision {
  shouldCheckpoint: boolean;
  priority: 'immediate' | 'high' | 'normal' | 'low' | 'skip';
  reason: string;
  delayMs?: number;
  compress?: boolean;
  delta?: boolean;
}

export class CheckpointStrategyAnalyzer {
  private operationHistory: OperationContext[] = [];
  private lastCheckpointTime: number = Date.now();
  private checkpointCount: number = 0;
  private riskScore: number = 0;
  
  // Strategy thresholds
  private readonly RISK_THRESHOLD = 0.7;
  private readonly TIME_THRESHOLD = 60000; // 1 minute
  private readonly OPERATION_BATCH_SIZE = 10;
  private readonly HIGH_IMPACT_OPERATIONS: Set<OperationType> = new Set([
    'file_delete',
    'directory_delete',
    'config_change',
    'dependency_update'
  ]);
  
  // Pattern detection for intelligent checkpointing
  private patterns = {
    massRefactor: {
      signature: ['file_modify', 'file_modify', 'file_modify'],
      window: 5000, // 5 seconds
      decision: { shouldCheckpoint: true, priority: 'high', reason: 'Mass refactoring detected' }
    },
    configurationChange: {
      signature: ['config_change'],
      window: 0,
      decision: { shouldCheckpoint: true, priority: 'immediate', reason: 'Configuration changed' }
    },
    destructiveOperation: {
      signature: ['file_delete'],
      window: 0,
      decision: { shouldCheckpoint: true, priority: 'high', reason: 'Destructive operation performed' }
    },
    dependencyUpdate: {
      signature: ['dependency_update'],
      window: 0,
      decision: { shouldCheckpoint: true, priority: 'immediate', reason: 'Dependencies updated' }
    },
    errorRecovery: {
      signature: ['error_recovery'],
      window: 0,
      decision: { shouldCheckpoint: true, priority: 'immediate', reason: 'Error recovery needed' }
    }
  };
  
  /**
   * Analyze an operation and determine if checkpoint is needed
   */
  analyzeOperation(operation: OperationContext): StrategyDecision {
    // Add to history
    this.operationHistory.push(operation);
    this.updateRiskScore(operation);
    
    // Check for pattern matches
    const patternMatch = this.detectPatterns();
    if (patternMatch) {
      return this.enhanceDecision(patternMatch, operation);
    }
    
    // Check risk-based triggers
    if (this.riskScore > this.RISK_THRESHOLD) {
      return {
        shouldCheckpoint: true,
        priority: 'high',
        reason: `Risk score exceeded threshold (${this.riskScore.toFixed(2)})`,
        compress: true
      };
    }
    
    // Check time-based triggers
    const timeSinceLastCheckpoint = Date.now() - this.lastCheckpointTime;
    if (timeSinceLastCheckpoint > this.TIME_THRESHOLD) {
      return {
        shouldCheckpoint: true,
        priority: 'normal',
        reason: 'Time threshold exceeded',
        delayMs: 5000 // Delay to batch more operations
      };
    }
    
    // Check operation batch size
    if (this.getRecentOperationCount() >= this.OPERATION_BATCH_SIZE) {
      return {
        shouldCheckpoint: true,
        priority: 'normal',
        reason: 'Operation batch size reached',
        delta: true
      };
    }
    
    // Check for high-impact operations
    if (this.HIGH_IMPACT_OPERATIONS.has(operation.type)) {
      return {
        shouldCheckpoint: true,
        priority: operation.impact === 'critical' ? 'immediate' : 'high',
        reason: `High-impact operation: ${operation.type}`,
        compress: operation.size ? operation.size > 100000 : false
      };
    }
    
    // Default: no checkpoint needed
    return {
      shouldCheckpoint: false,
      priority: 'skip',
      reason: 'No checkpoint trigger conditions met'
    };
  }
  
  /**
   * Detect patterns in operation history
   */
  private detectPatterns(): StrategyDecision | null {
    for (const pattern of Object.values(this.patterns)) {
      if (this.matchesPattern(pattern)) {
        return pattern.decision as StrategyDecision;
      }
    }
    return null;
  }
  
  /**
   * Check if recent operations match a pattern
   */
  private matchesPattern(pattern: any): boolean {
    const recentOps = this.operationHistory.slice(-pattern.signature.length);
    
    if (recentOps.length < pattern.signature.length) {
      return false;
    }
    
    // Check if operations match the signature
    const matches = recentOps.every((op, index) => {
      const expectedType = pattern.signature[index];
      return op.type === expectedType || expectedType === '*';
    });
    
    if (!matches) {
      return false;
    }
    
    // Check time window if specified
    if (pattern.window > 0) {
      const firstOp = recentOps[0];
      const lastOp = recentOps[recentOps.length - 1];
      const timeSpan = lastOp.timestamp - firstOp.timestamp;
      return timeSpan <= pattern.window;
    }
    
    return true;
  }
  
  /**
   * Update risk score based on operation
   */
  private updateRiskScore(operation: OperationContext) {
    // Base risk calculation
    let operationRisk = 0;
    
    switch (operation.type) {
      case 'file_delete':
      case 'directory_delete':
        operationRisk = 0.8;
        break;
      case 'config_change':
      case 'dependency_update':
        operationRisk = 0.7;
        break;
      case 'refactor':
        operationRisk = 0.5;
        break;
      case 'file_modify':
        operationRisk = 0.3;
        break;
      case 'file_create':
      case 'directory_create':
        operationRisk = 0.2;
        break;
      case 'command_execution':
        operationRisk = 0.4;
        break;
      case 'error_recovery':
        operationRisk = 0.9;
        break;
      default:
        operationRisk = 0.1;
    }
    
    // Adjust for impact level
    if (operation.impact) {
      const impactMultiplier = {
        low: 0.5,
        medium: 1.0,
        high: 1.5,
        critical: 2.0
      };
      operationRisk *= impactMultiplier[operation.impact];
    }
    
    // Adjust for reversibility
    if (operation.reversible === false) {
      operationRisk *= 1.5;
    }
    
    // Update cumulative risk score with decay
    this.riskScore = this.riskScore * 0.9 + operationRisk * 0.3;
    this.riskScore = Math.min(1.0, this.riskScore);
  }
  
  /**
   * Enhance decision with additional context
   */
  private enhanceDecision(
    baseDecision: StrategyDecision,
    operation: OperationContext
  ): StrategyDecision {
    const enhanced = { ...baseDecision };
    
    // Add compression for large operations
    if (operation.size && operation.size > 100000) {
      enhanced.compress = true;
    }
    
    // Use delta for frequent checkpoints
    if (this.checkpointCount > 10 && this.checkpointCount % 5 === 0) {
      enhanced.delta = true;
    }
    
    // Adjust delay based on system load
    if (enhanced.priority === 'normal' || enhanced.priority === 'low') {
      enhanced.delayMs = this.calculateOptimalDelay();
    }
    
    return enhanced;
  }
  
  /**
   * Calculate optimal delay for batching
   */
  private calculateOptimalDelay(): number {
    const recentOperationRate = this.getRecentOperationRate();
    
    if (recentOperationRate > 10) { // >10 ops/second
      return 1000; // 1 second delay
    } else if (recentOperationRate > 5) {
      return 3000; // 3 second delay
    } else if (recentOperationRate > 1) {
      return 5000; // 5 second delay
    } else {
      return 10000; // 10 second delay
    }
  }
  
  /**
   * Get recent operation count
   */
  private getRecentOperationCount(): number {
    const recentWindow = Date.now() - 30000; // Last 30 seconds
    return this.operationHistory.filter(op => op.timestamp > recentWindow).length;
  }
  
  /**
   * Get recent operation rate (ops/second)
   */
  private getRecentOperationRate(): number {
    const recentWindow = Date.now() - 10000; // Last 10 seconds
    const recentOps = this.operationHistory.filter(op => op.timestamp > recentWindow);
    return recentOps.length / 10;
  }
  
  /**
   * Record checkpoint creation
   */
  recordCheckpoint() {
    this.lastCheckpointTime = Date.now();
    this.checkpointCount++;
    this.riskScore *= 0.5; // Reduce risk after checkpoint
    // Keep only recent operation history
    const cutoff = Date.now() - 300000; // 5 minutes
    this.operationHistory = this.operationHistory.filter(op => op.timestamp > cutoff);
  }
  
  /**
   * Get strategy statistics
   */
  getStatistics() {
    return {
      totalOperations: this.operationHistory.length,
      checkpointCount: this.checkpointCount,
      currentRiskScore: this.riskScore,
      timeSinceLastCheckpoint: Date.now() - this.lastCheckpointTime,
      recentOperationRate: this.getRecentOperationRate(),
      recentOperationCount: this.getRecentOperationCount()
    };
  }
  
  /**
   * Get checkpoint recommendations
   */
  getRecommendations(): string[] {
    const recommendations: string[] = [];
    const stats = this.getStatistics();
    
    if (stats.currentRiskScore > 0.5) {
      recommendations.push('Consider creating a checkpoint due to elevated risk score');
    }
    
    if (stats.timeSinceLastCheckpoint > 120000) { // 2 minutes
      recommendations.push('No checkpoint created recently - consider manual checkpoint');
    }
    
    if (stats.recentOperationRate > 20) {
      recommendations.push('High operation rate detected - enable aggressive checkpointing');
    }
    
    if (this.checkpointCount > 50) {
      recommendations.push('Many checkpoints created - consider cleanup');
    }
    
    return recommendations;
  }
  
  /**
   * Reset strategy analyzer
   */
  reset() {
    this.operationHistory = [];
    this.lastCheckpointTime = Date.now();
    this.checkpointCount = 0;
    this.riskScore = 0;
  }
}

// Export singleton instance
export const checkpointStrategy = new CheckpointStrategyAnalyzer();
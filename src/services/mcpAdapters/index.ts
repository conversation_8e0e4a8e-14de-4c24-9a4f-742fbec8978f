/**
 * MCP Adapters Index
 * Central registry and management for MCP server adapters
 */

export * from './types';
export { Context7Adapter } from './context7Adapter';
export { SequentialAdapter } from './sequentialAdapter';
export { MagicAdapter } from './magicAdapter';
export { PlaywrightAdapter } from './playwrightAdapter';
export { MorphllmAdapter } from './morphllmAdapter';
export { SerenaAdapter } from './serenaAdapter';

import { BaseMCPAdapter, MCPServerConfig, MCPRequest, MCPResponse } from './types';
import { Context7Adapter } from './context7Adapter';
import { SequentialAdapter } from './sequentialAdapter';
import { MagicAdapter } from './magicAdapter';
import { PlaywrightAdapter } from './playwrightAdapter';
import { MorphllmAdapter } from './morphllmAdapter';
import { SerenaAdapter } from './serenaAdapter';

/**
 * MCP Adapter Registry
 * Manages all MCP server connections and routing
 */
export class MCPAdapterRegistry {
  private adapters: Map<string, BaseMCPAdapter> = new Map();
  private defaultConfigs: Map<string, MCPServerConfig> = new Map();
  
  constructor() {
    this.initializeDefaultAdapters();
  }
  
  private initializeDefaultAdapters() {
    // Initialize default configurations
    this.defaultConfigs.set('context7', {
      id: 'context7',
      name: 'Context7 Documentation Server',
      type: 'context7',
      timeout: 30000,
      retryAttempts: 3,
      capabilities: []
    });
    
    this.defaultConfigs.set('sequential', {
      id: 'sequential',
      name: 'Sequential Thinking Server',
      type: 'sequential',
      timeout: 60000,
      retryAttempts: 2,
      capabilities: []
    });
    
    this.defaultConfigs.set('magic', {
      id: 'magic',
      name: 'Magic UI Generation Server',
      type: 'magic',
      timeout: 20000,
      retryAttempts: 3,
      capabilities: []
    });
    
    this.defaultConfigs.set('playwright', {
      id: 'playwright',
      name: 'Playwright Testing Server',
      type: 'playwright',
      timeout: 120000,
      retryAttempts: 1,
      capabilities: []
    });
    
    this.defaultConfigs.set('morphllm', {
      id: 'morphllm',
      name: 'Morphllm Fast Apply',
      type: 'custom',
      timeout: 30000,
      retryAttempts: 2,
      capabilities: []
    });
    
    this.defaultConfigs.set('serena', {
      id: 'serena',
      name: 'Serena',
      type: 'custom',
      timeout: 30000,
      retryAttempts: 2,
      capabilities: []
    });
    
    // Create adapter instances
    this.registerAdapter('context7', new Context7Adapter(this.defaultConfigs.get('context7')!));
    this.registerAdapter('sequential', new SequentialAdapter(this.defaultConfigs.get('sequential')!));
    this.registerAdapter('magic', new MagicAdapter(this.defaultConfigs.get('magic')!));
    this.registerAdapter('playwright', new PlaywrightAdapter(this.defaultConfigs.get('playwright')!));
    this.registerAdapter('morphllm', new MorphllmAdapter(this.defaultConfigs.get('morphllm')!));
    this.registerAdapter('serena', new SerenaAdapter(this.defaultConfigs.get('serena')!));
  }
  
  /**
   * Register an MCP adapter
   */
  registerAdapter(id: string, adapter: BaseMCPAdapter) {
    this.adapters.set(id, adapter);
  }
  
  /**
   * Get an adapter by ID
   */
  getAdapter(id: string): BaseMCPAdapter | undefined {
    return this.adapters.get(id);
  }
  
  /**
   * Get all registered adapters
   */
  getAllAdapters(): BaseMCPAdapter[] {
    return Array.from(this.adapters.values());
  }
  
  /**
   * Send a request to an MCP server
   */
  async request(request: MCPRequest): Promise<MCPResponse> {
    const adapter = this.adapters.get(request.server);
    
    if (!adapter) {
      return {
        success: false,
        error: `MCP adapter '${request.server}' not found`
      };
    }
    
    // Check if method is supported
    if (!adapter.hasCapability(request.method)) {
      return {
        success: false,
        error: `Method '${request.method}' not supported by ${request.server}`
      };
    }
    
    // Execute request with retry logic
    let lastError: string | undefined;
    const retryAttempts = this.defaultConfigs.get(request.server)?.retryAttempts || 1;
    
    for (let attempt = 1; attempt <= retryAttempts; attempt++) {
      try {
        const response = await adapter.request(request);
        
        if (response.success) {
          return response;
        }
        
        lastError = response.error;
        
        // Don't retry if it's a client error
        if (response.error?.includes('Invalid') || response.error?.includes('Unsupported')) {
          break;
        }
        
        // Wait before retry with exponential backoff
        if (attempt < retryAttempts) {
          await this.delay(Math.pow(2, attempt) * 1000);
        }
      } catch (error) {
        lastError = error instanceof Error ? error.message : 'Unknown error';
      }
    }
    
    return {
      success: false,
      error: lastError || 'Request failed after retries'
    };
  }
  
  /**
   * Check health of all adapters
   */
  async healthCheckAll(): Promise<Map<string, boolean>> {
    const results = new Map<string, boolean>();
    
    for (const [id, adapter] of this.adapters) {
      try {
        const healthy = await adapter.healthCheck();
        results.set(id, healthy);
      } catch {
        results.set(id, false);
      }
    }
    
    return results;
  }
  
  /**
   * Clear cache for all adapters
   */
  clearAllCaches() {
    for (const adapter of this.adapters.values()) {
      adapter.clearCache();
    }
  }
  
  /**
   * Get capabilities for a specific server
   */
  getServerCapabilities(serverId: string): string[] {
    const adapter = this.adapters.get(serverId);
    if (!adapter) return [];
    
    return adapter.getCapabilities().map(c => c.name);
  }
  
  /**
   * Find servers that support a specific capability
   */
  findServersWithCapability(capability: string): string[] {
    const servers: string[] = [];
    
    for (const [id, adapter] of this.adapters) {
      if (adapter.hasCapability(capability)) {
        servers.push(id);
      }
    }
    
    return servers;
  }
  
  /**
   * Delay helper for retries
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Export singleton instance
export const mcpAdapterRegistry = new MCPAdapterRegistry();
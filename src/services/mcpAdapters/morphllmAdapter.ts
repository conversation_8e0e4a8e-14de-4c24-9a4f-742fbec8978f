/**
 * <PERSON><PERSON><PERSON>lm MCP Adapter
 * Pattern-based code editing engine with token optimization for bulk transformations
 */

import { BaseMCPAdapter, MCPServerConfig, MCPRequest, MCPResponse, MCPCapability } from './types';
import { morphllmMCP } from '../mcp/morphllm';

export class MorphllmAdapter extends BaseMCPAdapter {
  id = 'morphllm';
  name = 'Morphllm Fast Apply';
  capabilities: MCPCapability[] = [
    {
      name: 'bulk-edit',
      description: 'Execute bulk pattern-based code edits',
      category: 'transformation',
      requiredParams: ['patterns'],
      optionalParams: ['dryRun', 'tokenOptimization']
    },
    {
      name: 'pattern-replace',
      description: 'Apply simple pattern replacements',
      category: 'transformation',
      requiredParams: ['pattern', 'replacement'],
      optionalParams: ['scope']
    },
    {
      name: 'framework-migration',
      description: 'Migrate code between frameworks',
      category: 'transformation',
      requiredParams: ['from', 'to'],
      optionalParams: []
    },
    {
      name: 'style-enforcement',
      description: 'Enforce coding style guides',
      category: 'transformation',
      requiredParams: ['style', 'path'],
      optionalParams: []
    }
  ];

  constructor(config: MCPServerConfig) {
    super(config);
    // Set longer cache timeout for Morphllm operations
    this.cacheTimeout = 600000; // 10 minutes
  }

  async request(request: MCPRequest): Promise<MCPResponse> {
    const cacheKey = JSON.stringify({
      server: request.server,
      method: request.method,
      params: request.params
    });

    // Check cache first
    if (request.context?.cacheEnabled !== false) {
      const cached = this.getCached(cacheKey);
      if (cached) {
        return {
          success: true,
          data: cached,
          cached: true
        };
      }
    }

    try {
      // Map MCP request to Morphllm operation
      const operation = this.mapRequestToOperation(request);
      
      // Execute operation
      const result = await morphllmMCP.execute(operation);
      
      // Cache successful results
      if (result.success && request.context?.cacheEnabled !== false) {
        this.setCached(cacheKey, result.data);
      }
      
      return {
        success: result.success,
        data: result.data,
        error: result.error,
        tokensUsed: result.tokensUsed,
        serverTime: result.tokensUsed ? Math.floor(result.tokensUsed / 10) : undefined // Rough estimate
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private mapRequestToOperation(request: MCPRequest): any {
    switch (request.method) {
      case 'bulk-edit':
        return {
          type: 'bulk-edit',
          data: request.params
        };
      
      case 'pattern-replace':
        return {
          type: 'pattern-replace',
          pattern: request.params.pattern,
          replacement: request.params.replacement,
          scope: request.params.scope
        };
      
      case 'framework-migration':
        return {
          type: 'framework-migration',
          from: request.params.from,
          to: request.params.to
        };
      
      case 'style-enforcement':
        return {
          type: 'style-enforcement',
          style: request.params.style,
          path: request.params.path
        };
      
      default:
        throw new Error(`Unsupported method: ${request.method}`);
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      // Simple health check - verify the Morphllm service is available
      const config = morphllmMCP.getConfig();
      return config.id === 'morphllm' && config.name === 'Morphllm Fast Apply';
    } catch {
      return false;
    }
  }
}
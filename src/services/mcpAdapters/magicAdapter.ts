/**
 * Magic MCP Adapter
 * Modern UI component generation and design systems
 */

import { BaseMCPAdapter, MCPRequest, MCPResponse, MCPCapability } from './types';

export class Magic<PERSON><PERSON>pter extends BaseMCPAdapter {
  id = 'magic';
  name = 'Magic UI Generation Server';
  
  capabilities: MCPCapability[] = [
    {
      name: 'generate-component',
      description: 'Generate modern UI component',
      category: 'ui',
      requiredParams: ['type', 'framework'],
      optionalParams: ['props', 'styling', 'accessibility']
    },
    {
      name: 'design-system',
      description: 'Create or integrate design system',
      category: 'ui',
      requiredParams: ['name'],
      optionalParams: ['theme', 'tokens', 'components']
    },
    {
      name: 'responsive-layout',
      description: 'Generate responsive layout',
      category: 'ui',
      requiredParams: ['structure'],
      optionalParams: ['breakpoints', 'grid']
    },
    {
      name: 'ui-patterns',
      description: 'Get UI patterns from 21st.dev',
      category: 'ui',
      requiredParams: ['pattern'],
      optionalParams: ['framework', 'variant']
    }
  ];
  
  async request(request: MCPRequest): Promise<MCPResponse> {
    const startTime = Date.now();
    
    // Check cache for UI components
    const cacheKey = `${request.method}:${JSON.stringify(request.params)}`;
    const cached = this.getCached(cacheKey);
    if (cached && request.context?.cacheEnabled !== false) {
      return {
        success: true,
        data: cached,
        cached: true,
        serverTime: 0
      };
    }
    
    try {
      let result: any;
      
      switch (request.method) {
        case 'generate-component':
          result = await this.generateComponent(
            request.params.type,
            request.params.framework,
            request.params.props,
            request.params.styling,
            request.params.accessibility
          );
          break;
          
        case 'design-system':
          result = await this.createDesignSystem(
            request.params.name,
            request.params.theme,
            request.params.tokens,
            request.params.components
          );
          break;
          
        case 'responsive-layout':
          result = await this.generateResponsiveLayout(
            request.params.structure,
            request.params.breakpoints,
            request.params.grid
          );
          break;
          
        case 'ui-patterns':
          result = await this.getUIPatterns(
            request.params.pattern,
            request.params.framework,
            request.params.variant
          );
          break;
          
        default:
          throw new Error(`Unsupported method: ${request.method}`);
      }
      
      // Cache component results
      this.setCached(cacheKey, result);
      
      return {
        success: true,
        data: result,
        serverTime: Date.now() - startTime,
        tokensUsed: this.estimateTokens(result)
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        serverTime: Date.now() - startTime
      };
    }
  }
  
  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.request({
        server: this.id,
        method: 'ui-patterns',
        params: { pattern: 'button', framework: 'react' }
      });
      return response.success;
    } catch {
      return false;
    }
  }
  
  private async generateComponent(
    type: string,
    framework: string,
    props?: any,
    styling?: string,
    accessibility?: boolean
  ): Promise<any> {
    const componentCode = this.generateComponentCode(type, framework, props);
    const styles = this.generateStyles(type, styling);
    
    return {
      type,
      framework,
      code: componentCode,
      styles,
      props: props || {},
      accessibility: accessibility ? this.generateA11yAttributes(type) : {},
      usage: `<${type} ${props ? Object.keys(props).map(k => `${k}="${props[k]}"`).join(' ') : ''} />`,
      dependencies: this.getComponentDependencies(framework, type)
    };
  }
  
  private generateComponentCode(type: string, framework: string, props?: any): string {
    if (framework === 'react') {
      return `
import React from 'react';
import styles from './${type}.module.css';

interface ${type}Props {
  ${props ? Object.entries(props).map(([k, v]) => `${k}?: ${typeof v};`).join('\n  ') : ''}
}

export const ${type}: React.FC<${type}Props> = (${props ? `{ ${Object.keys(props).join(', ')} }` : 'props'}) => {
  return (
    <div className={styles.${type.toLowerCase()}}>
      {/* Component implementation */}
    </div>
  );
};`;
    } else if (framework === 'vue') {
      return `
<template>
  <div :class="$style.${type.toLowerCase()}">
    <!-- Component implementation -->
  </div>
</template>

<script setup lang="ts">
${props ? `defineProps<{ ${Object.entries(props).map(([k, v]) => `${k}?: ${typeof v}`).join(', ')} }>()` : ''}
</script>

<style module>
.${type.toLowerCase()} {
  /* Component styles */
}
</style>`;
    }
    
    return '// Component code for ' + framework;
  }
  
  private generateStyles(type: string, styling?: string): string {
    const baseStyles = `
.${type.toLowerCase()} {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}`;
    
    if (styling === 'tailwind') {
      return `className="flex items-center justify-center p-4 rounded-lg transition-all duration-300"`;
    }
    
    return baseStyles;
  }
  
  private generateA11yAttributes(type: string): any {
    return {
      role: this.getAriaRole(type),
      'aria-label': `${type} component`,
      tabIndex: 0,
      attributes: ['aria-describedby', 'aria-expanded', 'aria-pressed']
    };
  }
  
  private getAriaRole(type: string): string {
    const roleMap: Record<string, string> = {
      button: 'button',
      modal: 'dialog',
      dropdown: 'combobox',
      navigation: 'navigation',
      form: 'form'
    };
    return roleMap[type.toLowerCase()] || 'region';
  }
  
  private getComponentDependencies(framework: string, type: string): string[] {
    if (framework === 'react') {
      return ['react', 'react-dom', 'classnames'];
    } else if (framework === 'vue') {
      return ['vue', '@vue/composition-api'];
    }
    return [];
  }
  
  private async createDesignSystem(
    name: string,
    theme?: any,
    tokens?: any,
    components?: string[]
  ): Promise<any> {
    return {
      name,
      theme: theme || {
        colors: {
          primary: '#007bff',
          secondary: '#6c757d',
          success: '#28a745',
          danger: '#dc3545'
        },
        spacing: {
          xs: '0.25rem',
          sm: '0.5rem',
          md: '1rem',
          lg: '1.5rem',
          xl: '2rem'
        }
      },
      tokens: tokens || {
        borderRadius: '0.25rem',
        fontFamily: 'system-ui, sans-serif',
        transition: '0.3s ease'
      },
      components: components || ['Button', 'Card', 'Modal', 'Form', 'Table'],
      setup: 'npm install @your-org/design-system'
    };
  }
  
  private async generateResponsiveLayout(
    structure: string,
    breakpoints?: any,
    grid?: boolean
  ): Promise<any> {
    return {
      structure,
      breakpoints: breakpoints || {
        mobile: '640px',
        tablet: '768px',
        desktop: '1024px',
        wide: '1280px'
      },
      grid: grid ? {
        columns: 12,
        gap: '1rem',
        containerWidth: '100%'
      } : null,
      css: `
.container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .container { padding: 0 1.5rem; }
}

@media (min-width: 1024px) {
  .container { padding: 0 2rem; }
}`
    };
  }
  
  private async getUIPatterns(
    pattern: string,
    framework?: string,
    variant?: string
  ): Promise<any> {
    return {
      pattern,
      framework: framework || 'react',
      variant: variant || 'default',
      implementation: `// ${pattern} pattern implementation`,
      examples: [
        {
          name: `${pattern} - ${variant || 'default'}`,
          code: `<${pattern} variant="${variant || 'default'}" />`,
          preview: 'https://21st.dev/preview/' + pattern
        }
      ],
      bestPractices: [
        'Ensure accessibility compliance',
        'Test across different devices',
        'Optimize for performance'
      ]
    };
  }
  
  private estimateTokens(data: any): number {
    const text = JSON.stringify(data);
    return Math.floor(text.length / 4);
  }
}
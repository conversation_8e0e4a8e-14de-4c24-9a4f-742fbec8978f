/**
 * Serena MCP Adapter
 * Semantic code understanding with project memory and session persistence
 */

import { BaseMCPAdapter, MCPServerConfig, MCPRequest, MCPResponse, MCPCapability } from './types';
import { serenaMCP } from '../mcp/serena';

export class <PERSON><PERSON><PERSON>pter extends BaseMCPAdapter {
  id = 'serena';
  name = 'Serena';
  capabilities: MCPCapability[] = [
    {
      name: 'symbol-operation',
      description: 'Execute symbol operations (rename, extract, move)',
      category: 'analysis',
      requiredParams: ['type', 'target'],
      optionalParams: ['context']
    },
    {
      name: 'load-project',
      description: 'Load project context from memory',
      category: 'analysis',
      requiredParams: ['projectPath'],
      optionalParams: []
    },
    {
      name: 'save-project',
      description: 'Save project context to memory',
      category: 'analysis',
      requiredParams: ['projectPath'],
      optionalParams: []
    },
    {
      name: 'analyze-codebase',
      description: 'Analyze codebase for symbols and structure',
      category: 'analysis',
      requiredParams: ['path'],
      optionalParams: []
    },
    {
      name: 'get-memory-summary',
      description: 'Get project memory summary',
      category: 'analysis',
      requiredParams: [],
      optionalParams: []
    }
  ];

  constructor(config: MCPServerConfig) {
    super(config);
    // Set longer cache timeout for Serena operations
    this.cacheTimeout = 300000; // 5 minutes
  }

  async request(request: MCPRequest): Promise<MCPResponse> {
    const cacheKey = JSON.stringify({
      server: request.server,
      method: request.method,
      params: request.params
    });

    // Check cache first
    if (request.context?.cacheEnabled !== false) {
      const cached = this.getCached(cacheKey);
      if (cached) {
        return {
          success: true,
          data: cached,
          cached: true
        };
      }
    }

    try {
      // Map MCP request to Serena operation
      const operation = this.mapRequestToOperation(request);
      
      // Execute operation
      const result = await serenaMCP.execute(operation);
      
      // Cache successful results
      if (result.success && request.context?.cacheEnabled !== false) {
        this.setCached(cacheKey, result.data);
      }
      
      return {
        success: result.success,
        data: result.data,
        error: result.error,
        tokensUsed: result.tokensUsed,
        serverTime: result.tokensUsed ? Math.floor(result.tokensUsed / 5) : undefined // Rough estimate
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private mapRequestToOperation(request: MCPRequest): any {
    switch (request.method) {
      case 'symbol-operation':
        return {
          type: 'symbol',
          data: request.params
        };
      
      case 'load-project':
        return {
          type: 'load',
          projectPath: request.params.projectPath
        };
      
      case 'save-project':
        return {
          type: 'save',
          projectPath: request.params.projectPath
        };
      
      case 'analyze-codebase':
        return {
          type: 'analyze',
          path: request.params.path
        };
      
      case 'get-memory-summary':
        return {
          type: 'memory-summary'
        };
      
      default:
        throw new Error(`Unsupported method: ${request.method}`);
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      // Simple health check - verify the Serena service is available
      const config = serenaMCP.getConfig();
      return config.id === 'serena' && config.name === 'Serena';
    } catch {
      return false;
    }
  }
}
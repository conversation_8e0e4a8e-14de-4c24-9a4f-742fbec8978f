/**
 * MCP Adapter Types
 * Unified interface for MCP server integrations
 */

export interface MCPServerConfig {
  id: string;
  name: string;
  type: 'context7' | 'sequential' | 'magic' | 'playwright' | 'custom';
  endpoint?: string;
  apiKey?: string;
  timeout?: number;
  retryAttempts?: number;
  capabilities: MCPCapability[];
}

export interface MCPCapability {
  name: string;
  description: string;
  category: 'documentation' | 'analysis' | 'ui' | 'testing' | 'transformation';
  requiredParams?: string[];
  optionalParams?: string[];
}

export interface MCPRequest {
  server: string;
  method: string;
  params: Record<string, any>;
  context?: MCPContext;
}

export interface MCPResponse {
  success: boolean;
  data?: any;
  error?: string;
  tokensUsed?: number;
  cached?: boolean;
  serverTime?: number;
}

export interface MCPContext {
  sessionId: string;
  projectId: string;
  userId?: string;
  cacheEnabled?: boolean;
  timeout?: number;
}

export abstract class BaseMCPAdapter {
  abstract id: string;
  abstract name: string;
  abstract capabilities: MCPCapability[];
  
  protected config: MCPServerConfig;
  protected cache: Map<string, { data: any; timestamp: number }> = new Map();
  protected cacheTimeout = 300000; // 5 minutes default
  
  constructor(config: MCPServerConfig) {
    this.config = config;
  }
  
  /**
   * Send request to MCP server
   */
  abstract request(request: MCPRequest): Promise<MCPResponse>;
  
  /**
   * Check if server is available
   */
  abstract healthCheck(): Promise<boolean>;
  
  /**
   * Get server capabilities
   */
  getCapabilities(): MCPCapability[] {
    return this.capabilities;
  }
  
  /**
   * Check if capability is supported
   */
  hasCapability(name: string): boolean {
    return this.capabilities.some(c => c.name === name);
  }
  
  /**
   * Get cached result if available and valid
   */
  protected getCached(key: string): any | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    this.cache.delete(key);
    return null;
  }
  
  /**
   * Store result in cache
   */
  protected setCached(key: string, data: any): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }
  
  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear();
  }
}
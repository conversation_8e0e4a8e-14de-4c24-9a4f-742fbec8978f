/**
 * Slash Command Queue System
 * Priority-based command execution with wave orchestration
 */

import type { SuperClaudeCommand } from '@/types/superClaude';
import { commandExecutor } from './commandExecutor';

export interface QueuedCommand {
  id: string;
  commandName: string;
  command?: SuperClaudeCommand;
  args: string[];
  flags: Record<string, any>;
  priority: number;
  timestamp: number;
  status: 'pending' | 'executing' | 'completed' | 'failed';
  waveEnabled?: boolean;
  retries?: number;
  error?: string;
  sessionId?: string;
  projectId?: string;
}

export interface CommandExecutionResult {
  commandId: string;
  success: boolean;
  result?: any;
  error?: string;
  executionTime: number;
  waveCount?: number;
}

export interface CommandHistoryEntry {
  id: string;
  commandName: string;
  args: string[];
  flags: Record<string, any>;
  timestamp: number;
  success: boolean;
  executionTime: number;
  sessionId?: string;
}

export class SlashCommandQueue {
  private queue: QueuedCommand[] = [];
  private executing: QueuedCommand | null = null;
  private executionHistory: CommandExecutionResult[] = [];
  private commandHistory: CommandHistoryEntry[] = [];
  private maxQueueSize = 100;
  private maxRetries = 3;
  private priorityWeights = {
    critical: 100,
    high: 75,
    normal: 50,
    low: 25
  };
  
  /**
   * Add command to queue with priority
   */
  enqueue(
    commandName: string,
    args: string[] = [],
    flags: Record<string, any> = {},
    priority: 'critical' | 'high' | 'normal' | 'low' = 'normal',
    command?: SuperClaudeCommand,
    sessionId?: string,
    projectId?: string
  ): string {
    if (this.queue.length >= this.maxQueueSize) {
      throw new Error('Command queue is full');
    }
    
    const commandId = this.generateCommandId();
    const queuedCommand: QueuedCommand = {
      id: commandId,
      commandName,
      command,
      args,
      flags,
      priority: this.priorityWeights[priority],
      timestamp: Date.now(),
      status: 'pending',
      waveEnabled: this.isWaveEnabled(commandName),
      retries: 0,
      sessionId,
      projectId
    };
    
    // Insert in priority order
    const insertIndex = this.findInsertIndex(queuedCommand.priority);
    this.queue.splice(insertIndex, 0, queuedCommand);
    
    // Auto-execute if nothing is running
    if (!this.executing) {
      this.processNext();
    }
    
    return commandId;
  }
  
  /**
   * Parse command string into components
   */
  parseCommand(input: string): { command: string; args: string[]; flags: Record<string, any> } {
    const parts = input.trim().split(/\s+/);
    const command = parts[0].startsWith('/') ? parts[0].substring(1) : parts[0];
    const args: string[] = [];
    const flags: Record<string, any> = {};
    
    for (let i = 1; i < parts.length; i++) {
      const part = parts[i];
      if (part.startsWith('--')) {
        const flag = part.substring(2);
        const [key, value] = flag.split('=');
        flags[key] = value !== undefined ? value : true;
      } else if (part.startsWith('-')) {
        const flag = part.substring(1);
        flags[flag] = true;
      } else {
        args.push(part);
      }
    }
    
    return { command, args, flags };
  }
  
  /**
   * Suggest commands based on input
   */
  suggestCommands(input: string): SuperClaudeCommand[] {
    // Get all available commands from the executor
    const availableCommands = commandExecutor.getAvailableCommands();
    
    if (!input) {
      // Return top 10 most used commands
      return availableCommands.slice(0, 10);
    }
    
    // Filter by command name or description
    return availableCommands.filter(cmd => 
      cmd.name.toLowerCase().includes(input.toLowerCase()) ||
      cmd.description.toLowerCase().includes(input.toLowerCase())
    ).slice(0, 10);
  }
  
  /**
   * Get command by name
   */
  getCommand(name: string): SuperClaudeCommand | undefined {
    return commandExecutor.getAvailableCommands().find(cmd => cmd.name === name);
  }
  
  /**
   * Process next command in queue
   */
  private async processNext(): Promise<void> {
    if (this.executing || this.queue.length === 0) {
      return;
    }
    
    const command = this.queue.shift();
    if (!command) return;
    
    this.executing = command;
    command.status = 'executing';
    
    const startTime = Date.now();
    
    try {
      const result = await this.executeCommand(command);
      
      const executionResult: CommandExecutionResult = {
        commandId: command.id,
        success: true,
        result,
        executionTime: Date.now() - startTime,
        waveCount: command.waveEnabled ? this.calculateWaveCount(command) : undefined
      };
      
      command.status = 'completed';
      this.executionHistory.push(executionResult);
      
      // Add to command history
      this.commandHistory.push({
        id: command.id,
        commandName: command.commandName,
        args: command.args,
        flags: command.flags,
        timestamp: command.timestamp,
        success: true,
        executionTime: Date.now() - startTime,
        sessionId: command.sessionId
      });
      
    } catch (error) {
      // Initialize retries if undefined
      if (command.retries === undefined) {
        command.retries = 0;
      }
      command.retries++;
      
      if (command.retries < this.maxRetries) {
        // Re-queue for retry with slightly lower priority
        command.priority = Math.max(0, command.priority - 10);
        command.status = 'pending';
        const insertIndex = this.findInsertIndex(command.priority);
        this.queue.splice(insertIndex, 0, command);
      } else {
        command.status = 'failed';
        command.error = error instanceof Error ? error.message : String(error);
        
        const executionResult: CommandExecutionResult = {
          commandId: command.id,
          success: false,
          error: command.error,
          executionTime: Date.now() - startTime
        };
        
        this.executionHistory.push(executionResult);
        
        // Add to command history
        this.commandHistory.push({
          id: command.id,
          commandName: command.commandName,
          args: command.args,
          flags: command.flags,
          timestamp: command.timestamp,
          success: false,
          executionTime: Date.now() - startTime,
          sessionId: command.sessionId
        });
      }
    } finally {
      this.executing = null;
      // Process next command
      setTimeout(() => this.processNext(), 100);
    }
  }
  
  /**
   * Execute a command (integrate with actual command execution)
   */
  private async executeCommand(command: QueuedCommand): Promise<any> {
    console.log(`Executing command: ${command.commandName}`, command.args, command.flags);
    
    // Integrate with command executor
    if (command.sessionId && command.projectId) {
      const result = await commandExecutor.executeCommand(
        command.commandName,
        command.args,
        command.flags,
        {
          sessionId: command.sessionId,
          projectId: command.projectId,
          timestamp: Date.now()
        }
      );
      
      return result;
    }
    
    // Simulate async execution if no session context
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Wave orchestration for eligible commands
    if (command.waveEnabled) {
      return this.executeWithWaves(command);
    }
    
    return { message: `Command ${command.commandName} executed successfully` };
  }
  
  /**
   * Execute command with wave orchestration
   */
  private async executeWithWaves(command: QueuedCommand): Promise<any> {
    const waveCount = this.calculateWaveCount(command);
    const results = [];
    
    for (let wave = 1; wave <= waveCount; wave++) {
      console.log(`Executing wave ${wave}/${waveCount} for ${command.commandName}`);
      
      // Simulate wave execution
      await new Promise(resolve => setTimeout(resolve, 500));
      
      results.push({
        wave,
        result: `Wave ${wave} completed`
      });
    }
    
    return {
      command: command.commandName,
      waveCount,
      results
    };
  }
  
  /**
   * Check if command is wave-enabled
   */
  private isWaveEnabled(commandName: string): boolean {
    const waveEnabledCommands = [
      '/analyze',
      '/build',
      '/implement',
      '/improve',
      '/design',
      '/task'
    ];
    
    return waveEnabledCommands.includes(commandName);
  }
  
  /**
   * Calculate number of waves for command
   */
  private calculateWaveCount(command: QueuedCommand): number {
    // Base wave count
    let waves = 3;
    
    // Adjust based on flags
    if (command.flags['wave-count']) {
      waves = parseInt(command.flags['wave-count'], 10);
    } else if (command.flags['comprehensive']) {
      waves = 5;
    } else if (command.flags['quick']) {
      waves = 2;
    }
    
    // Adjust based on complexity
    if (command.args.length > 5) {
      waves++;
    }
    
    return Math.min(Math.max(waves, 1), 10); // Between 1 and 10 waves
  }
  
  /**
   * Find insertion index based on priority
   */
  private findInsertIndex(priority: number): number {
    for (let i = 0; i < this.queue.length; i++) {
      if (this.queue[i].priority < priority) {
        return i;
      }
    }
    return this.queue.length;
  }
  
  /**
   * Generate unique command ID
   */
  private generateCommandId(): string {
    return `cmd_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  /**
   * Get queue status
   */
  getStatus(): {
    queueLength: number;
    executing: boolean;
    currentCommand: QueuedCommand | null;
    pendingCommands: QueuedCommand[];
    completedCount: number;
    failedCount: number;
  } {
    return {
      queueLength: this.queue.length,
      executing: this.executing !== null,
      currentCommand: this.executing,
      pendingCommands: this.queue.slice(0, 10), // First 10 pending
      completedCount: this.executionHistory.filter(r => r.success).length,
      failedCount: this.executionHistory.filter(r => !r.success).length
    };
  }
  
  /**
   * Get command history
   */
  getCommandHistory(limit: number = 50): CommandHistoryEntry[] {
    return this.commandHistory.slice(-limit).reverse();
  }
  
  /**
   * Clear queue
   */
  clearQueue(): void {
    this.queue = [];
  }
  
  /**
   * Remove specific command from queue
   */
  removeCommand(commandId: string): boolean {
    const index = this.queue.findIndex(cmd => cmd.id === commandId);
    if (index !== -1) {
      this.queue.splice(index, 1);
      return true;
    }
    return false;
  }
  
  /**
   * Reprioritize command
   */
  reprioritize(
    commandId: string,
    newPriority: 'critical' | 'high' | 'normal' | 'low'
  ): boolean {
    const command = this.queue.find(cmd => cmd.id === commandId);
    if (command) {
      // Remove from current position
      this.removeCommand(commandId);
      
      // Update priority
      command.priority = this.priorityWeights[newPriority];
      
      // Re-insert at new position
      const insertIndex = this.findInsertIndex(command.priority);
      this.queue.splice(insertIndex, 0, command);
      
      return true;
    }
    return false;
  }
  
  /**
   * Get execution history
   */
  getHistory(limit: number = 50): CommandExecutionResult[] {
    return this.executionHistory.slice(-limit);
  }
  
  /**
   * Get average execution time
   */
  getAverageExecutionTime(): number {
    if (this.executionHistory.length === 0) return 0;
    
    const total = this.executionHistory.reduce(
      (sum, result) => sum + result.executionTime,
      0
    );
    
    return total / this.executionHistory.length;
  }
  
  /**
   * Get success rate
   */
  getSuccessRate(): number {
    if (this.executionHistory.length === 0) return 0;
    
    const successful = this.executionHistory.filter(r => r.success).length;
    return (successful / this.executionHistory.length) * 100;
  }
}

// Export singleton instance
export const slashCommandQueue = new SlashCommandQueue();
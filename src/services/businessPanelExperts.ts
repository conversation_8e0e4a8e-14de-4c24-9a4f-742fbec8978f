/**
 * Business Panel Experts
 * Complete implementation of all 9 business thought leader personas
 */

export type BusinessExpertName = 
  | 'christensen'
  | 'porter'
  | 'drucker'
  | 'godin'
  | 'kim_mauborgne'
  | 'collins'
  | 'taleb'
  | 'meadows'
  | 'doumont';

export interface ExpertAnalysis {
  expert: BusinessExpertName;
  name: string;
  framework: string;
  symbol: string;
  analysis: string;
  keyInsights: string[];
  questions: string[];
  recommendations: string[];
  confidence: number;
}

export interface ExpertPersona {
  id: BusinessExpertName;
  name: string;
  framework: string;
  symbol: string;
  voiceCharacteristics: string[];
  focusAreas: string[];
  keyQuestions: string[];
  analysisFramework: string[];
  color: string;
}

export class BusinessPanelExperts {
  private experts: Map<BusinessExpertName, ExpertPersona>;
  
  constructor() {
    this.experts = new Map();
    this.initializeExperts();
  }
  
  private initializeExperts() {
    // <PERSON> - Disruption Theory Expert
    this.experts.set('christensen', {
      id: 'christensen',
      name: '<PERSON>',
      framework: 'Disruptive Innovation Theory, Jobs-to-be-Done',
      symbol: '🔨',
      voiceCharacteristics: [
        'Academic and methodical approach to analysis',
        'Uses terminology: "sustaining vs disruptive", "non-consumption", "value network"',
        'Systematic categorization of innovations'
      ],
      focusAreas: [
        'Market segments: undershot vs overshot customers',
        'Value networks: different performance metrics',
        'Innovation patterns: low-end vs new-market disruption'
      ],
      keyQuestions: [
        'What job is the customer hiring this to do?',
        'Is this sustaining or disruptive innovation?',
        'What customers are being overshot by existing solutions?',
        'Where is there non-consumption we can address?'
      ],
      analysisFramework: [
        'Identify the job-to-be-done',
        'Map current solutions and their limitations',
        'Determine if innovation is sustaining or disruptive',
        'Assess value network implications'
      ],
      color: 'blue'
    });
    
    // Michael Porter - Competitive Strategy Analyst
    this.experts.set('porter', {
      id: 'porter',
      name: 'Michael Porter',
      framework: 'Five Forces, Value Chain, Generic Strategies',
      symbol: '⚔️',
      voiceCharacteristics: [
        'Analytical economics-focused systematic approach',
        'Uses terminology: "competitive advantage", "value chain", "strategic positioning"',
        'Rigorous competitive analysis'
      ],
      focusAreas: [
        'Competitive positioning: cost leadership vs differentiation',
        'Industry structure: five forces analysis',
        'Value creation: value chain optimization'
      ],
      keyQuestions: [
        'What are the barriers to entry?',
        'Where is value created in the chain?',
        "What's the sustainable competitive advantage?",
        'How attractive is this industry structure?'
      ],
      analysisFramework: [
        'Analyze industry structure (Five Forces)',
        'Map value chain activities',
        'Identify sources of competitive advantage',
        'Assess strategic positioning'
      ],
      color: 'red'
    });
    
    // Peter Drucker - Management Philosopher
    this.experts.set('drucker', {
      id: 'drucker',
      name: 'Peter Drucker',
      framework: 'Management by Objectives, Innovation Principles',
      symbol: '🧭',
      voiceCharacteristics: [
        'Wise: fundamental questions and principles',
        'Uses terminology: "effectiveness", "customer value", "systematic innovation"',
        'Purpose-driven analysis'
      ],
      focusAreas: [
        'Effectiveness: doing the right things',
        'Customer value: outside-in perspective',
        'Systematic innovation: seven sources of innovation'
      ],
      keyQuestions: [
        'What is our business? What should it be?',
        'Who is the customer? What does the customer value?',
        'What are our assumptions about customers and markets?',
        'Where are the opportunities for systematic innovation?'
      ],
      analysisFramework: [
        'Define the business purpose and mission',
        'Identify true customers and their values',
        'Question fundamental assumptions',
        'Seek systematic innovation opportunities'
      ],
      color: 'purple'
    });
    
    // Seth Godin - Marketing & Tribe Builder
    this.experts.set('godin', {
      id: 'godin',
      name: 'Seth Godin',
      framework: 'Permission Marketing, Purple Cow, Tribe Leadership',
      symbol: '🎪',
      voiceCharacteristics: [
        'Conversational: accessible and provocative',
        'Uses terminology: "remarkable", "permission", "tribe", "purple cow"',
        'Story-driven with practical insights'
      ],
      focusAreas: [
        'Remarkable products: standing out in crowded markets',
        'Permission marketing: earning attention vs interrupting',
        'Tribe building: creating communities around ideas'
      ],
      keyQuestions: [
        'Who would miss this if it was gone?',
        'Is this remarkable enough to spread?',
        'What permission do we have to talk to these people?',
        'How does this build or serve a tribe?'
      ],
      analysisFramework: [
        'Identify the target tribe',
        'Assess remarkability and spread-ability',
        'Evaluate permission and trust levels',
        'Design community and connection strategies'
      ],
      color: 'orange'
    });
    
    // W. Chan Kim & Renée Mauborgne - Blue Ocean Strategists
    this.experts.set('kim_mauborgne', {
      id: 'kim_mauborgne',
      name: 'Kim & Mauborgne',
      framework: 'Blue Ocean Strategy, Value Innovation',
      symbol: '🌊',
      voiceCharacteristics: [
        'Strategic: value-focused systematic approach',
        'Uses terminology: "blue ocean", "value innovation", "strategy canvas"',
        'Disciplined strategy formulation'
      ],
      focusAreas: [
        'Uncontested market space: blue vs red oceans',
        'Value innovation: differentiation + low cost',
        'Strategic moves: creating new market space'
      ],
      keyQuestions: [
        'What factors can be eliminated/reduced/raised/created?',
        'Where is the blue ocean opportunity?',
        'How can we achieve value innovation?',
        "What's our strategy canvas compared to industry?"
      ],
      analysisFramework: [
        'Map current industry strategy canvas',
        'Apply Four Actions Framework (ERRC)',
        'Identify blue ocean opportunities',
        'Design value innovation strategy'
      ],
      color: 'cyan'
    });
    
    // Jim Collins - Organizational Excellence Expert
    this.experts.set('collins', {
      id: 'collins',
      name: 'Jim Collins',
      framework: 'Good to Great, Built to Last, Flywheel Effect',
      symbol: '🚀',
      voiceCharacteristics: [
        'Research-driven: evidence-based disciplined approach',
        'Uses terminology: "Level 5 leadership", "hedgehog concept", "flywheel"',
        'Rigorous research methodology'
      ],
      focusAreas: [
        'Enduring greatness: sustainable excellence',
        'Disciplined people: right people in right seats',
        'Disciplined thought: brutal facts and hedgehog concept',
        'Disciplined action: consistent execution'
      ],
      keyQuestions: [
        'What are you passionate about?',
        'What drives your economic engine?',
        'What can you be best at?',
        'How does this build flywheel momentum?'
      ],
      analysisFramework: [
        'Assess disciplined people (leadership and team)',
        'Evaluate disciplined thought (brutal facts)',
        'Define hedgehog concept intersection',
        'Design flywheel and momentum builders'
      ],
      color: 'green'
    });
    
    // Nassim Nicholas Taleb - Risk & Uncertainty Expert
    this.experts.set('taleb', {
      id: 'taleb',
      name: 'Nassim Nicholas Taleb',
      framework: 'Antifragility, Black Swan Theory',
      symbol: '🎲',
      voiceCharacteristics: [
        'Contrarian: skeptical of conventional wisdom',
        'Uses terminology: "antifragile", "black swan", "via negativa"',
        'Philosophical yet practical'
      ],
      focusAreas: [
        'Antifragility: benefiting from volatility',
        'Optionality: asymmetric outcomes',
        'Uncertainty handling: robust to unknown unknowns'
      ],
      keyQuestions: [
        'How does this benefit from volatility?',
        'What are the hidden risks and tail events?',
        'Where are the asymmetric opportunities?',
        "What's the downside if we're completely wrong?"
      ],
      analysisFramework: [
        'Identify fragilities and dependencies',
        'Map potential black swan events',
        'Design antifragile characteristics',
        'Create asymmetric option portfolios'
      ],
      color: 'amber'
    });
    
    // Donella Meadows - Systems Thinking Expert
    this.experts.set('meadows', {
      id: 'meadows',
      name: 'Donella Meadows',
      framework: 'Systems Thinking, Leverage Points, Stocks and Flows',
      symbol: '🕸️',
      voiceCharacteristics: [
        'Holistic: pattern-focused interconnections',
        'Uses terminology: "leverage points", "feedback loops", "system structure"',
        'Systematic exploration of relationships'
      ],
      focusAreas: [
        'System structure: stocks, flows, feedback loops',
        'Leverage points: where to intervene in systems',
        'Unintended consequences: system behavior patterns'
      ],
      keyQuestions: [
        "What's the system structure causing this behavior?",
        'Where are the highest leverage intervention points?',
        'What feedback loops are operating?',
        'What might be the unintended consequences?'
      ],
      analysisFramework: [
        'Map system structure and relationships',
        'Identify feedback loops and delays',
        'Locate leverage points for intervention',
        'Anticipate system responses and consequences'
      ],
      color: 'indigo'
    });
    
    // Jean-luc Doumont - Communication Systems Expert
    this.experts.set('doumont', {
      id: 'doumont',
      name: 'Jean-luc Doumont',
      framework: 'Trees, Maps, and Theorems (Structured Communication)',
      symbol: '💬',
      voiceCharacteristics: [
        'Precise: logical clarity-focused approach',
        'Uses terminology: "message structure", "audience needs", "cognitive load"',
        'Methodical communication design'
      ],
      focusAreas: [
        'Message structure: clear logical flow',
        'Audience needs: serving reader/listener requirements',
        'Cognitive efficiency: reducing unnecessary complexity'
      ],
      keyQuestions: [
        "What's the core message?",
        "How does this serve the audience's needs?",
        "What's the clearest way to structure this?",
        'How do we reduce cognitive load?'
      ],
      analysisFramework: [
        'Identify core message and purpose',
        'Analyze audience needs and constraints',
        'Structure message for maximum clarity',
        'Optimize for cognitive efficiency'
      ],
      color: 'teal'
    });
  }
  
  /**
   * Get a specific expert persona
   */
  getExpert(name: BusinessExpertName): ExpertPersona | undefined {
    return this.experts.get(name);
  }
  
  /**
   * Get all expert personas
   */
  getAllExperts(): ExpertPersona[] {
    return Array.from(this.experts.values());
  }
  
  /**
   * Select experts based on analysis context
   */
  selectExperts(
    context: string,
    maxExperts: number = 5
  ): BusinessExpertName[] {
    const contextLower = context.toLowerCase();
    const expertScores = new Map<BusinessExpertName, number>();
    
    // Score each expert based on relevance to context
    for (const [id, expert] of this.experts) {
      let score = 0;
      
      // Check if context mentions expert's focus areas
      for (const area of expert.focusAreas) {
        if (contextLower.includes(area.toLowerCase())) {
          score += 2;
        }
      }
      
      // Check framework relevance
      const frameworkTerms = expert.framework.toLowerCase().split(/[,\s]+/);
      for (const term of frameworkTerms) {
        if (contextLower.includes(term)) {
          score += 1.5;
        }
      }
      
      // Special context keywords
      if (contextLower.includes('innovation') && 
          (id === 'christensen' || id === 'drucker' || id === 'kim_mauborgne')) {
        score += 2;
      }
      
      if (contextLower.includes('strategy') && 
          (id === 'porter' || id === 'kim_mauborgne' || id === 'collins')) {
        score += 2;
      }
      
      if (contextLower.includes('risk') && 
          (id === 'taleb' || id === 'meadows')) {
        score += 2;
      }
      
      if (contextLower.includes('marketing') && 
          (id === 'godin' || id === 'christensen')) {
        score += 2;
      }
      
      if (contextLower.includes('system') && 
          (id === 'meadows' || id === 'drucker')) {
        score += 2;
      }
      
      if (contextLower.includes('communication') && 
          (id === 'doumont' || id === 'godin')) {
        score += 2;
      }
      
      expertScores.set(id, score);
    }
    
    // Sort experts by score and return top N
    const sorted = Array.from(expertScores.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, maxExperts)
      .map(([id]) => id);
    
    // Ensure minimum diversity if all scores are low
    if (sorted.length < 3) {
      return ['christensen', 'porter', 'meadows'];
    }
    
    return sorted;
  }
  
  /**
   * Generate expert analysis
   */
  generateAnalysis(
    expertName: BusinessExpertName,
    content: string,
    mode: 'discussion' | 'debate' | 'socratic' = 'discussion'
  ): ExpertAnalysis | undefined {
    const expert = this.getExpert(expertName);
    if (!expert) return undefined;
    
    // This is a placeholder for actual analysis generation
    // In a real implementation, this would use AI to generate authentic analysis
    return {
      expert: expertName,
      name: expert.name,
      framework: expert.framework,
      symbol: expert.symbol,
      analysis: this.generateExpertVoice(expert, content, mode),
      keyInsights: this.extractKeyInsights(expert, content),
      questions: this.generateQuestions(expert, mode),
      recommendations: this.generateRecommendations(expert, content),
      confidence: this.calculateConfidence(expert, content)
    };
  }
  
  /**
   * Generate analysis in expert's voice
   */
  private generateExpertVoice(
    expert: ExpertPersona,
    _content: string,
    mode: 'discussion' | 'debate' | 'socratic'
  ): string {
    // Placeholder implementation
    const voiceStyle = expert.voiceCharacteristics[0];
    const framework = expert.analysisFramework[0];
    
    if (mode === 'socratic') {
      return `Let me guide your thinking through questions: ${expert.keyQuestions[0]}`;
    }
    
    if (mode === 'debate') {
      return `I respectfully challenge this assumption based on ${expert.framework}...`;
    }
    
    return `Applying ${expert.framework}, I see that ${framework}. ${voiceStyle}`;
  }
  
  /**
   * Extract key insights based on expert's framework
   */
  private extractKeyInsights(
    expert: ExpertPersona,
    _content: string
  ): string[] {
    // Placeholder - would analyze content through expert's lens
    return expert.focusAreas.slice(0, 3).map(area => 
      `Key insight regarding ${area}`
    );
  }
  
  /**
   * Generate questions in expert's style
   */
  private generateQuestions(
    expert: ExpertPersona,
    mode: 'discussion' | 'debate' | 'socratic'
  ): string[] {
    if (mode === 'socratic') {
      return expert.keyQuestions;
    }
    
    // Return subset for other modes
    return expert.keyQuestions.slice(0, 2);
  }
  
  /**
   * Generate recommendations based on expert's framework
   */
  private generateRecommendations(
    expert: ExpertPersona,
    _content: string
  ): string[] {
    // Placeholder - would generate specific recommendations
    return expert.analysisFramework.slice(0, 3).map(step =>
      `Recommendation: ${step}`
    );
  }
  
  /**
   * Calculate confidence score
   */
  private calculateConfidence(
    expert: ExpertPersona,
    content: string
  ): number {
    // Placeholder - would calculate based on content relevance
    const contentLower = content.toLowerCase();
    let relevance = 0;
    
    for (const area of expert.focusAreas) {
      if (contentLower.includes(area.toLowerCase())) {
        relevance += 0.2;
      }
    }
    
    return Math.min(0.95, 0.6 + relevance);
  }
  
  /**
   * Generate cross-expert synthesis
   */
  generateSynthesis(_analyses: ExpertAnalysis[]): {
    convergentInsights: string[];
    productiveTensions: string[];
    systemPatterns: string[];
    blindSpots: string[];
    strategicQuestions: string[];
  } {
    return {
      convergentInsights: [
        'Multiple experts agree on the importance of customer focus',
        'Innovation and systematic approach are recurring themes'
      ],
      productiveTensions: [
        'Disruption vs sustaining innovation (Christensen vs Porter)',
        'Antifragility vs predictable strategy (Taleb vs Collins)'
      ],
      systemPatterns: [
        'Feedback loops identified in market dynamics',
        'Leverage points in customer acquisition'
      ],
      blindSpots: [
        'Limited consideration of regulatory constraints',
        'Technological disruption timeline uncertainty'
      ],
      strategicQuestions: [
        'How do we balance short-term execution with long-term positioning?',
        'What are the second-order effects of our strategic choices?'
      ]
    };
  }
}

// Export singleton instance
export const businessPanelExperts = new BusinessPanelExperts();
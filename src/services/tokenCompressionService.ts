import { serenaMCP } from '@/services/mcp/serena';
import { TokenEfficiencyMode } from '@/services/modes/tokenEfficiency';

/**
 * Token compression utility that integrates with Serena MCP for context-aware compression
 */
export class TokenCompressionService {
  private tokenEfficiencyMode: TokenEfficiencyMode;

  constructor() {
    this.tokenEfficiencyMode = new TokenEfficiencyMode();
  }

  /**
   * Compress text using token efficiency mode with Serena MCP context
   */
  async compressTextWithContext(text: string, projectPath?: string): Promise<string> {
    // First, try to get context from Serena MCP
    if (projectPath) {
      try {
        const analysis = await serenaMCP.execute({
          type: 'analyze',
          path: projectPath,
        });

        if (analysis.success) {
          // Use context-aware compression
          return await this.tokenEfficiencyMode.compressWithContext(text, { projectPath });
        }
      } catch (error) {
        console.warn('Failed to use Serena MCP for compression, falling back to standard compression:', error);
      }
    }

    // Fallback to standard compression
    return this.tokenEfficiencyMode.compressText(text);
  }

  /**
   * Generate compression report
   */
  generateCompressionReport(original: string, compressed: string): string {
    return this.tokenEfficiencyMode.generateCompressionReport(original, compressed);
  }

  /**
   * Monitor token usage and provide recommendations
   */
  monitorTokenUsage(currentTokens: number, maxTokens: number): {
    usagePercentage: number;
    status: 'normal' | 'warning' | 'critical';
    message: string;
    recommendation?: string;
  } {
    const monitoringData = this.tokenEfficiencyMode.monitorTokenUsage(currentTokens, maxTokens);
    
    let recommendation: string | undefined;
    if (monitoringData.status === 'warning') {
      recommendation = 'Consider enabling Token Efficiency Mode to reduce token usage by 30-50%';
    } else if (monitoringData.status === 'critical') {
      recommendation = 'Token usage is critical. Enable Token Efficiency Mode immediately to continue';
    }
    
    return {
      usagePercentage: monitoringData.usagePercentage,
      status: monitoringData.status,
      message: monitoringData.message,
      recommendation
    };
  }

  /**
   * Trim context to fit within token limits
   */
  trimContext(messages: any[], maxTokens: number): any[] {
    return this.tokenEfficiencyMode.trimContext(messages, maxTokens);
  }

  /**
   * Get compression level based on resource usage
   */
  getCompressionLevel(resourceUsage: number): number {
    return this.tokenEfficiencyMode.getCompressionLevel(resourceUsage);
  }
}

// Export singleton instance
export const tokenCompressionService = new TokenCompressionService();
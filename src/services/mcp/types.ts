/**
 * MCP Server Type Definitions
 * Comprehensive types for all 6 MCP servers in SuperClaude Framework
 */

/**
 * MCP Server identifiers
 */
export type MCPServerId = 
  | 'context7' 
  | 'sequential' 
  | 'magic' 
  | 'playwright' 
  | 'serena' 
  | 'morphllm';

/**
 * MCP Server activation context
 */
export interface MCPActivationContext {
  taskType: string;
  complexity: number;
  fileCount?: number;
  operationType?: string;
  keywords: string[];
  requiresSymbolOperations?: boolean;
  requiresBulkEdits?: boolean;
  requiresDocumentation?: boolean;
  requiresUIGeneration?: boolean;
  requiresTesting?: boolean;
  requiresAnalysis?: boolean;
}

/**
 * MCP Server configuration
 */
export interface MCPServerConfig {
  id: MCPServerId;
  name: string;
  purpose: string;
  primaryUseCases: string[];
  activationPatterns: string[];
  triggers: string[];
  worksbestWith: MCPServerId[];
  chooseWhen: string[];
  notFor: string[];
  tokenEfficiency?: number; // Percentage of token savings
  requiresApiKey?: boolean;
  apiKeyEnv?: string;
}

/**
 * MCP Server response
 */
export interface MCPServerResponse {
  serverId: MCPServerId;
  success: boolean;
  data?: any;
  error?: string;
  tokensUsed?: number;
  executionTime?: number;
}

/**
 * MCP Orchestration options
 */
export interface MCPOrchestrationOptions {
  primaryServer?: MCPServerId;
  fallbackServers?: MCPServerId[];
  enableCaching?: boolean;
  maxRetries?: number;
  timeout?: number;
  parallelExecution?: boolean;
}

/**
 * MCP Server coordination result
 */
export interface MCPCoordinationResult {
  primaryResult?: MCPServerResponse;
  secondaryResults?: MCPServerResponse[];
  synthesis?: any;
  totalTokensUsed?: number;
  totalExecutionTime?: number;
}

/**
 * Serena-specific types
 */
export interface SerenaSymbolOperation {
  type: 'rename' | 'extract' | 'move' | 'find-references';
  target: string;
  newName?: string;
  destination?: string;
  scope?: 'file' | 'module' | 'project';
}

export interface SerenaProjectMemory {
  projectId: string;
  symbols: Map<string, SerenaSymbolInfo>;
  dependencies: Map<string, string[]>;
  sessionData: any;
  lastUpdated: Date;
}

export interface SerenaSymbolInfo {
  name: string;
  type: 'function' | 'class' | 'variable' | 'interface' | 'type';
  file: string;
  line: number;
  references: string[];
  dependencies: string[];
}

/**
 * Morphllm-specific types
 */
export interface MorphllmEditPattern {
  pattern: string | RegExp;
  replacement: string;
  scope?: 'file' | 'directory' | 'project';
  filePattern?: string;
  preserveFormatting?: boolean;
}

export interface MorphllmBulkEdit {
  patterns: MorphllmEditPattern[];
  dryRun?: boolean;
  enforceStyle?: string; // ESLint, Prettier, etc.
  frameworkMigration?: string; // React class to hooks, etc.
  tokenOptimization?: boolean;
}

export interface MorphllmResult {
  filesModified: number;
  changesApplied: number;
  tokensSaved: number;
  executionTime: number;
  preview?: string[];
}

/**
 * MCP Server capability matrix
 */
export interface MCPCapabilityMatrix {
  [key: string]: {
    servers: MCPServerId[];
    priority: MCPServerId;
    fallback?: MCPServerId;
  };
}

/**
 * MCP Cache entry
 */
export interface MCPCacheEntry {
  serverId: MCPServerId;
  key: string;
  data: any;
  timestamp: Date;
  ttl: number; // Time to live in seconds
  hits: number;
}

/**
 * MCP Server health status
 */
export interface MCPServerHealth {
  serverId: MCPServerId;
  status: 'healthy' | 'degraded' | 'unavailable';
  lastCheck: Date;
  responseTime?: number;
  errorRate?: number;
  successRate?: number;
}
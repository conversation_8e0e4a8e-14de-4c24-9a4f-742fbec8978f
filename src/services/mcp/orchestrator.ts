/**
 * MCP Orchestrator
 * Intelligent coordination of all 6 MCP servers with fallback and caching
 */

import {
  MCPServerId,
  MCPServerConfig,
  MCPServerResponse,
  MCPActivationContext,
  MCPOrchestrationOptions,
  MCPCoordinationResult,
  MCPCacheEntry,
  MCPServerHealth,
} from './types';
import { serenaMCP } from './serena';
import { morphllmMCP } from './morphllm';

export class MCPOrchestrator {
  private servers: Map<MCPServerId, MCPServerConfig> = new Map();
  private serverHealth: Map<MCPServerId, MCPServerHealth> = new Map();
  private cache: Map<string, MCPCacheEntry> = new Map();
  // private capabilityMatrix: MCPCapabilityMatrix = {};

  constructor() {
    this.initializeServers();
    // this.initializeCapabilityMatrix();
    this.initializeHealth();
  }

  /**
   * Initialize all MCP server configurations
   */
  private initializeServers() {
    // Define all 6 MCP servers
    const serverConfigs: MCPServerConfig[] = [
      {
        id: 'context7',
        name: 'Context7',
        purpose: 'Official library documentation and code examples',
        primaryUseCases: ['Documentation lookup', 'Framework patterns', 'Best practices'],
        activationPatterns: ['import', 'library', 'framework', 'documentation'],
        triggers: ['library imports', 'framework questions', 'API documentation'],
        worksbestWith: ['sequential'],
        chooseWhen: ['Need official documentation', 'Framework patterns required'],
        notFor: ['Code generation', 'Testing', 'Analysis'],
      },
      {
        id: 'sequential',
        name: 'Sequential Thinking',
        purpose: 'Multi-step problem solving and systematic analysis',
        primaryUseCases: ['Complex analysis', 'Debugging', 'Architecture review'],
        activationPatterns: ['analyze', 'debug', 'architecture', 'complex'],
        triggers: ['complex problems', 'multi-step analysis', 'debugging'],
        worksbestWith: ['context7', 'serena'],
        chooseWhen: ['Complex reasoning needed', 'Multi-step problems'],
        notFor: ['Simple lookups', 'UI generation'],
      },
      {
        id: 'magic',
        name: 'Magic UI Generator',
        purpose: 'Modern UI component generation and design systems',
        primaryUseCases: ['UI components', 'Design systems', 'Responsive design'],
        activationPatterns: ['component', 'UI', 'design', 'frontend'],
        triggers: ['UI requests', 'component creation', 'design system'],
        worksbestWith: ['context7', 'playwright'],
        chooseWhen: ['UI generation needed', 'Component creation'],
        notFor: ['Backend logic', 'Analysis', 'Testing'],
        requiresApiKey: true,
        apiKeyEnv: 'TWENTYFIRST_API_KEY',
      },
      {
        id: 'playwright',
        name: 'Playwright',
        purpose: 'Cross-browser E2E testing and automation',
        primaryUseCases: ['E2E testing', 'Browser automation', 'Visual testing'],
        activationPatterns: ['test', 'e2e', 'browser', 'automation'],
        triggers: ['testing needs', 'browser automation', 'E2E scenarios'],
        worksbestWith: ['magic', 'sequential'],
        chooseWhen: ['Browser testing needed', 'E2E automation'],
        notFor: ['Code generation', 'Documentation'],
      },
      serenaMCP.getConfig(),
      morphllmMCP.getConfig(),
    ];

    // Store configurations
    serverConfigs.forEach(config => {
      this.servers.set(config.id, config);
    });
  }

  /**
   * Initialize capability matrix for task-server mapping
   */
  // private initializeCapabilityMatrix() {
  //   this.capabilityMatrix = {
  //     'documentation': {
  //       servers: ['context7', 'sequential'],
  //       priority: 'context7',
  //       fallback: 'sequential',
  //     },
  //     'ui-generation': {
  //       servers: ['magic', 'context7'],
  //       priority: 'magic',
  //       fallback: 'context7',
  //     },
  //     'testing': {
  //       servers: ['playwright', 'sequential'],
  //       priority: 'playwright',
  //       fallback: 'sequential',
  //     },
  //     'analysis': {
  //       servers: ['sequential', 'serena'],
  //       priority: 'sequential',
  //       fallback: 'serena',
  //     },
  //     'symbol-operations': {
  //       servers: ['serena', 'morphllm'],
  //       priority: 'serena',
  //       fallback: 'morphllm',
  //     },
  //     'bulk-edits': {
  //       servers: ['morphllm', 'serena'],
  //       priority: 'morphllm',
  //       fallback: 'serena',
  //     },
  //     'debugging': {
  //       servers: ['sequential', 'serena', 'playwright'],
  //       priority: 'sequential',
  //     },
  //     'refactoring': {
  //       servers: ['serena', 'morphllm', 'sequential'],
  //       priority: 'serena',
  //     },
  //   };
  // }

  /**
   * Initialize server health monitoring
   */
  private initializeHealth() {
    const serverIds: MCPServerId[] = ['context7', 'sequential', 'magic', 'playwright', 'serena', 'morphllm'];
    
    serverIds.forEach(id => {
      this.serverHealth.set(id, {
        serverId: id,
        status: 'healthy',
        lastCheck: new Date(),
        responseTime: 0,
        successRate: 100,
        errorRate: 0,
      });
    });
  }

  /**
   * Select best server for given context
   */
  selectServer(context: MCPActivationContext): MCPServerId | null {
    let bestServer: MCPServerId | null = null;
    let bestScore = 0;

    // Check each server's activation score
    if (context.requiresSymbolOperations) {
      const score = serenaMCP.shouldActivate(context);
      if (score > bestScore) {
        bestScore = score;
        bestServer = 'serena';
      }
    }

    if (context.requiresBulkEdits) {
      const score = morphllmMCP.shouldActivate(context);
      if (score > bestScore) {
        bestScore = score;
        bestServer = 'morphllm';
      }
    }

    // Check other servers based on keywords
    this.servers.forEach((config, id) => {
      if (id === 'serena' || id === 'morphllm') return; // Already checked
      
      let score = 0;
      
      // Check activation patterns
      const patterns = config.activationPatterns.join('|').toLowerCase();
      const keywordMatch = context.keywords.some(k => patterns.includes(k.toLowerCase()));
      if (keywordMatch) {
        score += 0.5;
      }

      // Check specific requirements
      if (context.requiresDocumentation && id === 'context7') score += 0.8;
      if (context.requiresUIGeneration && id === 'magic') score += 0.8;
      if (context.requiresTesting && id === 'playwright') score += 0.8;
      if (context.requiresAnalysis && id === 'sequential') score += 0.7;

      if (score > bestScore) {
        bestScore = score;
        bestServer = id;
      }
    });

    // Check server health before returning
    if (bestServer) {
      const health = this.serverHealth.get(bestServer);
      if (health && health.status === 'unavailable') {
        // Find fallback server
        return this.findFallbackServer(bestServer, context);
      }
    }

    return bestScore > 0.3 ? bestServer : null;
  }

  /**
   * Find fallback server when primary is unavailable
   */
  private findFallbackServer(
    primaryServer: MCPServerId,
    _context: MCPActivationContext
  ): MCPServerId | null {
    const config = this.servers.get(primaryServer);
    if (!config) return null;

    // Check works best with servers
    for (const fallbackId of config.worksbestWith) {
      const health = this.serverHealth.get(fallbackId);
      if (health && health.status === 'healthy') {
        return fallbackId;
      }
    }

    return null;
  }

  /**
   * Execute operation with orchestration
   */
  async execute(
    operation: any,
    options?: MCPOrchestrationOptions
  ): Promise<MCPCoordinationResult> {
    const startTime = Date.now();
    const result: MCPCoordinationResult = {
      totalTokensUsed: 0,
      totalExecutionTime: 0,
    };

    try {
      // Check cache first
      if (options?.enableCaching) {
        const cacheKey = this.getCacheKey(operation);
        const cached = this.getFromCache(cacheKey);
        if (cached) {
          console.log(`[MCP Orchestrator] Cache hit for ${cacheKey}`);
          return cached;
        }
      }

      // Select primary server
      const context = this.buildActivationContext(operation);
      const primaryServer = options?.primaryServer || this.selectServer(context);
      
      if (!primaryServer) {
        throw new Error('No suitable MCP server found for operation');
      }

      console.log(`[MCP Orchestrator] Selected primary server: ${primaryServer}`);

      // Execute on primary server
      const primaryResponse = await this.executeOnServer(primaryServer, operation);
      result.primaryResult = primaryResponse;
      result.totalTokensUsed = (result.totalTokensUsed || 0) + (primaryResponse.tokensUsed || 0);

      // Execute on secondary servers if needed
      if (options?.parallelExecution) {
        const secondaryServers = this.selectSecondaryServers(primaryServer, context);
        const secondaryPromises = secondaryServers.map(id => 
          this.executeOnServer(id, operation)
        );
        
        const secondaryResults = await Promise.allSettled(secondaryPromises);
        result.secondaryResults = secondaryResults
          .filter(r => r.status === 'fulfilled')
          .map(r => (r as any).value);
        
        result.totalTokensUsed = (result.totalTokensUsed || 0) + result.secondaryResults.reduce(
          (sum, r) => sum + (r.tokensUsed || 0), 0
        );
      }

      // Synthesize results
      if (result.secondaryResults && result.secondaryResults.length > 0) {
        result.synthesis = this.synthesizeResults(result.primaryResult!, result.secondaryResults);
      }

      result.totalExecutionTime = Date.now() - startTime;

      // Cache successful result
      if (options?.enableCaching && result.primaryResult?.success) {
        const cacheKey = this.getCacheKey(operation);
        this.addToCache(cacheKey, result);
      }

      // Update server health
      this.updateServerHealth(primaryServer, true, result.totalExecutionTime);

      return result;
    } catch (error) {
      console.error('[MCP Orchestrator] Execution error:', error);
      
      // Try fallback servers
      if (options?.fallbackServers && options.fallbackServers.length > 0) {
        for (const fallbackId of options.fallbackServers) {
          try {
            const fallbackResponse = await this.executeOnServer(fallbackId, operation);
            result.primaryResult = fallbackResponse;
            result.totalTokensUsed = fallbackResponse.tokensUsed || 0;
            result.totalExecutionTime = Date.now() - startTime;
            return result;
          } catch (fallbackError) {
            console.error(`[MCP Orchestrator] Fallback ${fallbackId} failed:`, fallbackError);
          }
        }
      }

      throw error;
    }
  }

  /**
   * Execute operation on specific server
   */
  private async executeOnServer(
    serverId: MCPServerId,
    operation: any
  ): Promise<MCPServerResponse> {
    // Route to appropriate server implementation
    switch (serverId) {
      case 'serena':
        return await serenaMCP.execute(operation);
      
      case 'morphllm':
        return await morphllmMCP.execute(operation);
      
      case 'context7':
      case 'sequential':
      case 'magic':
      case 'playwright':
        // These would have their own implementations
        // For now, return mock response
        return {
          serverId,
          success: true,
          data: { message: `${serverId} execution simulated` },
          tokensUsed: 100,
        };
      
      default:
        throw new Error(`Unknown server: ${serverId}`);
    }
  }

  /**
   * Build activation context from operation
   */
  private buildActivationContext(operation: any): MCPActivationContext {
    return {
      taskType: operation.type || 'unknown',
      complexity: operation.complexity || 5,
      fileCount: operation.fileCount,
      operationType: operation.operationType,
      keywords: operation.keywords || [],
      requiresSymbolOperations: operation.type === 'symbol',
      requiresBulkEdits: operation.type === 'bulk-edit',
      requiresDocumentation: operation.type === 'documentation',
      requiresUIGeneration: operation.type === 'ui-component',
      requiresTesting: operation.type === 'test',
      requiresAnalysis: operation.type === 'analyze',
    };
  }

  /**
   * Select secondary servers for parallel execution
   */
  private selectSecondaryServers(
    primaryServer: MCPServerId,
    _context: MCPActivationContext
  ): MCPServerId[] {
    const config = this.servers.get(primaryServer);
    if (!config) return [];

    return config.worksbestWith.filter(id => {
      const health = this.serverHealth.get(id);
      return health && health.status === 'healthy';
    });
  }

  /**
   * Synthesize results from multiple servers
   */
  private synthesizeResults(
    primary: MCPServerResponse,
    secondary: MCPServerResponse[]
  ): any {
    return {
      primary: primary.data,
      secondary: secondary.map(s => s.data),
      consensus: secondary.every(s => s.success),
      totalServers: 1 + secondary.length,
    };
  }

  /**
   * Update server health metrics
   */
  private updateServerHealth(
    serverId: MCPServerId,
    success: boolean,
    responseTime: number
  ) {
    const health = this.serverHealth.get(serverId);
    if (!health) return;

    health.lastCheck = new Date();
    health.responseTime = responseTime;
    
    if (success) {
      health.successRate = Math.min(100, (health.successRate || 100) + 1);
      health.errorRate = Math.max(0, (health.errorRate || 0) - 1);
    } else {
      health.successRate = Math.max(0, (health.successRate || 100) - 5);
      health.errorRate = Math.min(100, (health.errorRate || 0) + 5);
    }

    // Update status based on metrics
    if (health.errorRate > 50) {
      health.status = 'unavailable';
    } else if (health.errorRate > 20) {
      health.status = 'degraded';
    } else {
      health.status = 'healthy';
    }
  }

  /**
   * Get cache key for operation
   */
  private getCacheKey(operation: any): string {
    return `${operation.type}::${JSON.stringify(operation.data || operation)}`;
  }

  /**
   * Get from cache
   */
  private getFromCache(key: string): MCPCoordinationResult | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    // Check TTL
    const age = Date.now() - entry.timestamp.getTime();
    if (age > entry.ttl * 1000) {
      this.cache.delete(key);
      return null;
    }

    entry.hits++;
    return entry.data;
  }

  /**
   * Add to cache
   */
  private addToCache(key: string, data: MCPCoordinationResult) {
    const entry: MCPCacheEntry = {
      serverId: data.primaryResult?.serverId || 'unknown' as MCPServerId,
      key,
      data,
      timestamp: new Date(),
      ttl: 300, // 5 minutes default
      hits: 0,
    };
    
    this.cache.set(key, entry);
    
    // Limit cache size
    if (this.cache.size > 100) {
      const firstKey = this.cache.keys().next().value;
      if (firstKey) this.cache.delete(firstKey);
    }
  }

  /**
   * Get all available servers
   */
  getAvailableServers(): MCPServerId[] {
    return Array.from(this.servers.keys()).filter(id => {
      const health = this.serverHealth.get(id);
      return health && health.status !== 'unavailable';
    });
  }

  /**
   * Get server health status
   */
  getServerHealth(): Map<MCPServerId, MCPServerHealth> {
    return this.serverHealth;
  }

  /**
   * Clear cache
   */
  clearCache() {
    this.cache.clear();
  }
}

// Export singleton instance
export const mcpOrchestrator = new MCPOrchestrator();
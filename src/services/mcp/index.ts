/**
 * MCP Module Index
 * Export all MCP servers and orchestration
 */

export * from './types';
export { serenaMCP } from './serena';
export { morphllmMCP } from './morphllm';
export { mcpOrchestrator } from './orchestrator';

// Re-export commonly used types
export type {
  MCPServerId,
  MCPServerConfig,
  MCPServerResponse,
  MCPActivationContext,
  MCPOrchestrationOptions,
  MCPCoordinationResult,
  SerenaSymbolOperation,
  SerenaProjectMemory,
  MorphllmEditPattern,
  MorphllmBulkEdit,
  MorphllmResult,
} from './types';
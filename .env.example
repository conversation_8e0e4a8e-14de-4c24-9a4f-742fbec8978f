# Claudia Environment Configuration

# SuperClaude Framework Configuration
SUPERCLAUDE_ENABLED=true
SUPERCLAUDE_VERSION=4.0.8
SUPERCLAUDE_DEFAULT_MODE=normal
SUPERCLAUDE_TOKEN_EFFICIENCY=false
SUPERCLAUDE_AUTO_PERSONA_DETECTION=true
SUPERCLAUDE_WAVE_ENABLED=true
SUPERCLAUDE_BUSINESS_PANEL_ENABLED=true

# MCP Server Configuration (for SuperClaude)
SUPERCLAUDE_MCP_CONTEXT7_ENABLED=true
SUPERCLAUDE_MCP_SEQUENTIAL_ENABLED=true
SUPERCLAUDE_MCP_MAGIC_ENABLED=true
SUPERCLAUDE_MCP_PLAYWRIGHT_ENABLED=true

# SuperClaude Resource Paths
SUPERCLAUDE_RESOURCES_PATH=./src-tauri/resources/superclaude
SUPERCLAUDE_FRAMEWORK_PATH=./SuperClaude_Framework-master

# Development Settings
SUPERCLAUDE_DEBUG=false
SUPERCLAUDE_LOG_LEVEL=info
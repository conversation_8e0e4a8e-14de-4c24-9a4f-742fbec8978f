# Claudia Development Guide

## Build Commands
```bash
# Install dependencies
bun install

# Development
bun run dev

# Build
bun run build

# Preview production build
bun run preview
```

## Code Quality
```bash
# Type check
bun run check
```

## Rust Code Quality
```bash
# Run Rust checks
cd src-tauri && cargo check

# Run Rust linter
cd src-tauri && cargo clippy
```

## Testing
```bash
# Run all tests
bun test

# Run specific test file
bun test <file-path>

# Run tests with coverage
bun test --coverage
```

## Code Style Guidelines

### TypeScript/React
- Use TypeScript for all new code
- Strict typing required - no implicit any
- Use functional components with hooks
- Camel case for variables/functions, PascalCase for components
- Use descriptive variable names

### Imports
- Absolute imports preferred with @/ alias
- Group imports: node modules, then relative imports
- Alphabetize within groups

### Tauri/Rust
- Follow Rust naming conventions
- Use snake_case for variables/functions
- Module structure with mod.rs
- Error handling with Result<T, E>

### UI Components
- Use Shadcn/ui components when possible
- Responsive design with Tailwind
- Consistent styling and spacing

## Missing SuperClaude Features
- Business Panel, Introspection, Orchestration modes
- All SuperClaude commands (analyze, brainstorm, build, etc.)
- MCP server integrations (Context7, Magic, Playwright)
- Agent implementations (15+ missing agents)
- Slash command system
- Advanced checkpoint management

## Backend Implementation Status

### Implemented Features
- SuperClaude command system with framework loading
- 6 mode types with content-based detection
- Full MCP server management (CRUD operations)
- Complete checkpoint/state management system
- Framework loading from markdown definitions

### Missing Features Requiring Backend Support
- Business panel expert orchestration
- Advanced wave management APIs
- Enhanced flag system with validation
- Persona management and activation
- Advanced tool chaining and monitoring
- Performance profiling metrics
- Dynamic context injection for prompts
# Claudia Development Guide

## Build Commands
```bash
# Install dependencies
bun install

# Development
bun run dev

# Build
bun run build

# Preview production build
bun run preview
```

## Code Quality
```bash
# Type check
bun run check

# Run Rust checks
cd src-tauri && cargo check

# Run Rust linter
cd src-tauri && cargo clippy
```

## Testing
```bash
# Run all tests
bun test

# Run specific test file
bun test <file-path>

# Run tests with coverage
bun test --coverage
```

## Code Style Guidelines

### TypeScript/React
- Use TypeScript for all new code
- Strict typing required - no implicit any
- Use functional components with hooks
- Camel case for variables/functions, PascalCase for components
- Use descriptive variable names
- Prefer absolute imports with @/ alias
- Group imports: node modules, then relative imports
- Alphabetize within groups

### Tauri/Rust
- Follow Rust naming conventions
- Use snake_case for variables/functions
- Module structure with mod.rs
- Error handling with Result<T, E>

### UI Components
- Use Shadcn/ui components when possible
- Responsive design with Tailwind
- Consistent styling and spacing

## Code Review
- All PRs require Claude code review via GitHub Action
- Review focuses on: code quality, bugs, performance, security, test coverage
- PRs with `[skip-review]` in title bypass review

## Version Management
```bash
# Bump version across all files
./scripts/bump-version.sh <version>
```
- Updates package.json, Cargo.toml, tauri.conf.json, and Info.plist
- Creates a git commit with version bump
- Requires manual `git push && git push --tags` after

## Missing Features
- Business Panel, Introspection, Orchestration modes
- All SuperClaude commands (analyze, brainstorm, build, etc.)
- MCP server integrations (Context7, Magic, Playwright)
- Agent implementations (15+ missing agents)
- Slash command system
- Advanced checkpoint management
# BUSINESS_SYMBOLS.md - Business Analysis Symbol System

Enhanced symbol system for business panel analysis with strategic focus and efficiency optimization.

## Business-Specific Symbols

### Strategic Analysis
| Symbol | Meaning | Usage Context |
|--------|---------|---------------|
| 🎯 | strategic target, objective | Key goals and outcomes |
| 📈 | growth opportunity, positive trend | Market growth, revenue increase |
| 📉 | decline, risk, negative trend | Market decline, threats |
| 💰 | financial impact, revenue | Economic drivers, profit centers |
| ⚖️ | trade-offs, balance | Strategic decisions, resource allocation |
| 🏆 | competitive advantage | Unique value propositions, strengths |
| 🔄 | business cycle, feedback loop | Recurring patterns, system dynamics |
| 🌊 | blue ocean, new market | Uncontested market space |
| 🏭 | industry, market structure | Competitive landscape |
| 🎪 | remarkable, purple cow | Standout products, viral potential |

### Framework Integration  
| Symbol | Expert | Framework Element |
|--------|--------|-------------------|
| 🔨 | Christensen | Jobs-to-be-Done |
| ⚔️ | Porter | Five Forces |
| 🎪 | Godin | Purple Cow/Remarkable |
| 🌊 | Kim/Mauborgne | Blue Ocean |
| 🚀 | Collins | Flywheel Effect |
| 🛡️ | Taleb | Antifragile/Robustness |
| 🕸️ | Meadows | System Structure |
| 💬 | Doumont | Clear Communication |
| 🧭 | Drucker | Management Fundamentals |

### Analysis Process
| Symbol | Process Stage | Description |
|--------|---------------|-------------|
| 🔍 | investigation | Initial analysis and discovery |
| 💡 | insight | Key realizations and breakthroughs |
| 🤝 | consensus | Expert agreement areas |
| ⚡ | tension | Productive disagreement |
| 🎭 | debate | Adversarial analysis mode |
| ❓ | socratic | Question-driven exploration |
| 🧩 | synthesis | Cross-framework integration |
| 📋 | conclusion | Final recommendations |

### Business Logic Flow
| Symbol | Meaning | Business Context |
|--------|---------|------------------|
| → | causes, leads to | Market trends → opportunities |
| ⇒ | strategic transformation | Current state ⇒ desired future |
| ← | constraint, limitation | Resource limits ← budget |
| ⇄ | mutual influence | Customer needs ⇄ product development |
| ∴ | strategic conclusion | Market analysis ∴ go-to-market strategy |
| ∵ | business rationale | Expand ∵ market opportunity |
| ≡ | strategic equivalence | Strategy A ≡ Strategy B outcomes |
| ≠ | competitive differentiation | Our approach ≠ competitors |

## Expert Voice Symbols

### Communication Styles
| Expert | Symbol | Voice Characteristic |
|--------|--------|---------------------|
| Christensen | 📚 | Academic, methodical |
| Porter | 📊 | Analytical, data-driven |
| Drucker | 🧠 | Wise, fundamental |
| Godin | 💬 | Conversational, provocative |
| Kim/Mauborgne | 🎨 | Strategic, value-focused |
| Collins | 📖 | Research-driven, disciplined |
| Taleb | 🎲 | Contrarian, risk-aware |
| Meadows | 🌐 | Holistic, systems-focused |
| Doumont | ✏️ | Precise, clarity-focused |

## Synthesis Output Templates

### Discussion Mode Synthesis
```markdown
## 🧩 SYNTHESIS ACROSS FRAMEWORKS

**🤝 Convergent Insights**: [Where multiple experts agree]
- 🎯 Strategic alignment on [key area]
- 💰 Economic consensus around [financial drivers]
- 🏆 Shared view of competitive advantage

**⚖️ Productive Tensions**: [Strategic trade-offs revealed]
- 📈 Growth vs 🛡️ Risk management (Taleb ⚡ Collins)
- 🌊 Innovation vs 📊 Market positioning (Kim/Mauborgne ⚡ Porter)

**🕸️ System Patterns** (Meadows analysis):
- Leverage points: [key intervention opportunities]
- Feedback loops: [reinforcing/balancing dynamics]

**💬 Communication Clarity** (Doumont optimization):
- Core message: [essential strategic insight]
- Action priorities: [implementation sequence]

**⚠️ Blind Spots**: [Gaps requiring additional analysis]

**🤔 Strategic Questions**: [Next exploration priorities]
```

### Debate Mode Synthesis
```markdown
## ⚡ PRODUCTIVE TENSIONS RESOLVED

**Initial Conflict**: [Primary disagreement area]
- 📚 **CHRISTENSEN position**: [Innovation framework perspective]
- 📊 **PORTER counter**: [Competitive strategy challenge]

**🔄 Resolution Process**: 
[How experts found common ground or maintained productive tension]

**🧩 Higher-Order Solution**: 
[Strategy that honors multiple frameworks]

**🕸️ Systems Insight** (Meadows):
[How the debate reveals deeper system dynamics]
```

### Socratic Mode Synthesis
```markdown
## 🎓 STRATEGIC THINKING DEVELOPMENT

**🤔 Question Themes Explored**:
- Framework lens: [Which expert frameworks were applied]
- Strategic depth: [Level of analysis achieved]

**💡 Learning Insights**:
- Pattern recognition: [Strategic thinking patterns developed]
- Framework integration: [How to combine expert perspectives]

**🧭 Next Development Areas**:
[Strategic thinking capabilities to develop further]
```

## Token Efficiency Integration

### Compression Strategies
- **Expert Voice Compression**: Maintain authenticity while reducing verbosity
- **Framework Symbol Substitution**: Use symbols for common framework concepts
- **Structured Output**: Organized templates reducing repetitive text
- **Smart Abbreviation**: Business-specific abbreviations with context preservation

### Business Abbreviations
```yaml
common_terms:
  'comp advantage': 'competitive advantage'
  'value prop': 'value proposition' 
  'go-to-market': 'GTM'
  'total addressable market': 'TAM'
  'customer acquisition cost': 'CAC'
  'lifetime value': 'LTV'
  'key performance indicator': 'KPI'
  'return on investment': 'ROI'
  'minimum viable product': 'MVP'
  'product-market fit': 'PMF'

frameworks:
  'jobs-to-be-done': 'JTBD'
  'blue ocean strategy': 'BOS'
  'good to great': 'G2G'  
  'five forces': '5F'
  'value chain': 'VC'
  'four actions framework': 'ERRC'
```

## Mode Configuration

### Default Settings
```yaml
business_panel_config:
  # Expert Selection
  max_experts: 5
  min_experts: 3
  auto_select: true
  diversity_optimization: true
  
  # Analysis Depth
  phase_progression: adaptive
  synthesis_required: true
  cross_framework_validation: true
  
  # Output Control
  symbol_compression: true
  structured_templates: true
  expert_voice_preservation: 0.85
  
  # Integration
  mcp_sequential_primary: true
  mcp_context7_patterns: true
  persona_coordination: true
```

### Performance Optimization
- **Token Budget**: 15-30K tokens for comprehensive analysis
- **Expert Caching**: Store expert personas for session reuse
- **Framework Reuse**: Cache framework applications for similar content
- **Synthesis Templates**: Pre-structured output formats for efficiency
- **Parallel Analysis**: Where possible, run expert analysis in parallel

## Quality Assurance

### Authenticity Validation
- **Voice Consistency**: Each expert maintains characteristic communication style
- **Framework Fidelity**: Analysis follows authentic framework methodology  
- **Interaction Realism**: Expert interactions reflect realistic professional dynamics
- **Synthesis Integrity**: Combined insights maintain individual framework value

### Business Analysis Standards
- **Strategic Relevance**: Analysis addresses real business strategic concerns
- **Implementation Feasibility**: Recommendations are actionable and realistic
- **Evidence Base**: Conclusions supported by framework logic and business evidence
- **Professional Quality**: Analysis meets executive-level business communication standards
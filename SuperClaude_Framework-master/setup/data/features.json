{"components": {"core": {"name": "core", "version": "4.0.8", "description": "SuperClaude framework documentation and core files", "category": "core", "dependencies": [], "enabled": true, "required_tools": []}, "commands": {"name": "commands", "version": "4.0.8", "description": "SuperClaude slash command definitions", "category": "commands", "dependencies": ["core"], "enabled": true, "required_tools": []}, "mcp": {"name": "mcp", "version": "4.0.8", "description": "MCP server configuration management via .claude.json", "category": "integration", "dependencies": ["core"], "enabled": true, "required_tools": []}, "modes": {"name": "modes", "version": "4.0.8", "description": "SuperClaude behavioral modes (Brainstorming, Introspection, Task Management, Token Efficiency)", "category": "modes", "dependencies": ["core"], "enabled": true, "required_tools": []}, "mcp_docs": {"name": "mcp_docs", "version": "4.0.8", "description": "MCP server documentation and usage guides", "category": "documentation", "dependencies": ["core"], "enabled": true, "required_tools": []}, "agents": {"name": "agents", "version": "4.0.8", "description": "14 specialized AI agents with domain expertise and intelligent routing", "category": "agents", "dependencies": ["core"], "enabled": true, "required_tools": []}}}
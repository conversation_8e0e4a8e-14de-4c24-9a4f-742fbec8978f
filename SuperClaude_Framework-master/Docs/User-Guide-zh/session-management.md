# 会话管理指南

SuperClaude 通过 Serena MCP 服务器提供持久会话管理，实现在 Claude Code 对话中真正的上下文保存和长期项目连续性。

## 具有持久内存的核心会话命令

### `/sc:load` - 具有持久内存的上下文加载
**目的**：使用项目上下文和以前会话的持久内存初始化会话
**MCP 集成**：触发 Serena MCP 读取存储的项目内存
**语法**：`/sc:load [project_path]`

**发生什么**：
- Serena MCP 从以前的会话中读取持久内存文件
- 从存储的内存中恢复项目上下文
- 加载以前的决策、模式和进度
- 使用历史上下文初始化会话状态

**使用案例**：
```bash
# 从持久内存加载现有项目上下文
/sc:load src/

# 恢复特定项目工作及其完整历史
/sc:load "authentication system"

# 使用代码库分析和先前见解初始化
/sc:load . --analyze
```

### `/sc:save` - 会话持久化到内存
**目的**：将当前会话状态和决策保存到持久内存
**MCP 集成**：触发 Serena MCP 写入内存文件
**语法**：`/sc:save "会话描述"`

**发生什么**：
- 当前上下文和决策被写入 Serena 内存
- 项目状态和进度在对话中持久保存
- 关键见解和模式被存储以供未来会话使用
- 创建带有时间戳的会话摘要以便检索

**使用案例**：
```bash
# 保存已完成的特性工作以供未来参考
/sc:save "user authentication implemented with JWT"

# 复杂工作期间的检查点
/sc:save "API design phase complete, ready for implementation"

# 永久存储架构决策
/sc:save "microservices architecture decided, service boundaries defined"
```

### `/sc:reflect` - 带有内存上下文的进度评估
**目的**：根据存储的内存分析当前进度并验证会话完整性
**MCP 集成**：使用 Serena MCP 将当前状态与存储的内存进行比较
**语法**：`/sc:reflect [--scope project|session]`

**发生什么**：
- Serena MCP 读取以前的内存和当前上下文
- 根据存储的目标和里程碑评估进度
- 使用历史上下文识别差距和下一步
- 根据项目内存验证会话完整性

**使用案例**：
```bash
# 根据存储的里程碑评估项目进度
/sc:reflect --scope project

# 验证当前会话完整性
/sc:reflect

# 根据内存检查是否准备进入下一阶段
/sc:reflect --scope session
```

## 持久内存架构

### Serena MCP 如何实现真正的持久性

**内存存储**：
- 会话上下文作为结构化内存文件存储
- 项目决策和架构模式永久保存
- 代码分析结果和见解在对话中保持
- 进度跟踪和里程碑数据长期维护

**跨会话连续性**：
- 新对话中自动加载以前的会话上下文
- 决策和理由在对话中保存和可访问
- 从过去的模式和解决方案中学习并维护
- 一致的项目理解无限期维护

**内存类型**：
- **项目内存**：长期项目上下文和架构
- **会话内存**：特定对话结果和决策
- **模式内存**：可重用的解决方案和架构模式
- **进度内存**：里程碑跟踪和完成状态

## 具有持久性的会话生命周期模式

### 新项目初始化
```bash
# 1. 开始全新项目
/sc:brainstorm "e-commerce platform requirements"

# 2. 将初始决策保存到持久内存
/sc:save "project scope and requirements defined"

# 3. 开始实现规划
/sc:workflow "user authentication system"

# 4. 永久保存架构决策
/sc:save "authentication architecture: JWT + refresh tokens + rate limiting"
```

### 恢复现有工作（跨对话）
```bash
# 1. 从持久内存加载以前的上下文
/sc:load "e-commerce project"

# 2. 根据存储的进度评估当前状态
/sc:reflect --scope project

# 3. 使用存储的上下文继续下一阶段
/sc:implement "payment processing integration"

# 4. 将进度检查点保存到内存
/sc:save "payment system integrated with Stripe API"
```

### 长期项目管理
```bash
# 具有持久性的周检查点模式
/sc:load project-name
/sc:reflect --scope project
# ... 处理特性 ...
/sc:save "week N progress: features X, Y, Z completed"

# 具有内存的阶段完成模式
/sc:reflect --scope project
/sc:save "phase 1 complete: core authentication and user management"
/sc:workflow "phase 2: payment and order processing"
```

## 跨对话连续性

### 使用持久性开始新对话

当开始新的 Claude Code 对话时，持久内存系统允许：

1. **自动上下文恢复**
   ```bash
   /sc:load project-name
   # 自动恢复所有以前的上下文、决策和进度
   ```

2. **进度继续**
   - 以前会话的决策立即可用
   - 架构模式和代码见解被保存
   - 项目历史和理由被维护

3. **智能上下文构建**
   - Serena MCP 根据当前工作提供相关内存
   - 过去的解决方案和模式指导新的实现
   - 项目演变被跟踪和理解

### 内存优化

**有效的内存使用**：
- 使用描述性、可搜索的内存名称
- 包含项目阶段和时间戳上下文
- 引用特定功能或架构决策
- 让未来的检索变得直观

**内存内容策略**：
- 存储决策和理由，而不仅仅是结果
- 包含考虑的替代方案
- 记录集成模式和依赖关系
- 为未来参考保留学习和洞察

**内存生命周期管理**：
- 定期清理过时的内存
- 整合相关的会话内存
- 归档已完成的项目阶段
- 修剪过时的架构决策

## 持久会话的最佳实践

### 会话开始协议
1. 对于现有项目始终以 `/sc:load` 开始
2. 使用 `/sc:reflect` 从内存中了解当前状态
3. 根据持久上下文和存储的模式规划工作
4. 基于以前的决策和架构选择构建

### 会话结束协议
1. 使用 `/sc:reflect` 根据存储的目标评估完整性
2. 使用 `/sc:save` 保存关键决策以供未来会话
3. 在内存中记录下一步和未解决的问题
4. 为无缝的未来继续保存上下文

### 内存质量维护
- 使用清晰、描述性的内存名称以便于检索
- 包含关于决策和替代方法的上下文
- 引用特定的代码位置和模式
- 在跨会话中保持内存结构的一致性

## 与其他 SuperClaude 功能的集成

### MCP 服务器协调
- **Serena MCP**：提供持久内存基础设施
- **Sequential MCP**：使用存储的内存进行增强的复杂分析
- **Context7 MCP**：引用存储的模式和文档方法
- **Morphllm MCP**：一致地应用存储的重构模式

### 智能体与内存的协作
- 智能体访问持久内存以增强上下文
- 先前的专家决策被保留和引用
- 通过共享内存进行跨会话智能体协调
- 基于项目历史的一致专家建议

### 命令与持久化的集成
- 所有 `/sc:` 命令都可以引用和构建持久上下文
- 先前的命令输出和决策在跨会话中可用
- 工作流程模式被存储和重用
- 实现历史指导未来的命令决策

## 持久会话故障排除

### 常见问题

**内存未加载**：
- 验证 Serena MCP 配置正确并正常运行
- 检查内存文件权限和可访问性
- 确保一致的项目命名约定
- 验证内存文件完整性和格式

**会话间上下文丢失**：
- 在结束会话前始终使用 `/sc:save`
- 使用描述性内存名称以便于检索
- 定期使用 `/sc:reflect` 验证内存完整性
- 定期备份重要的内存文件

**内存冲突**：
- 使用带时间戳的内存名称进行版本控制
- 定期清理过时的内存
- 清楚分离项目和会话内存
- 在跨会话中保持一致的内存命名约定

### 快速修复

**重置会话状态**：
```bash
/sc:load --fresh  # 不带先前上下文开始
/sc:reflect       # 评估当前状态
```

**内存清理**：
```bash
/sc:reflect --cleanup  # 删除过时的内存
/sc:save --consolidate # 合并相关内存
```

**上下文恢复**：
```bash
/sc:load --recent     # 加载最近的内存
/sc:reflect --repair  # 识别并修复上下文空白
```

## 高级持久会话模式

### 多阶段项目
- 使用阶段特定的内存命名进行组织
- 跨阶段维护架构决策连续性
- 通过持久内存进行跨阶段依赖跟踪
- 具有历史上下文的渐进式复杂性管理

### 团队协作
- 共享的内存约定和命名标准
- 为团队上下文保留决策理由
- 所有团队成员都可访问的集成模式文档
- 通过内存实现一致的代码风格和架构执行

### 长期维护
- 已完成项目的内存归档策略
- 通过累积内存开发模式库
- 随时间建立的可重用解决方案文档
- 通过持久内存积累建立知识库

## 持久会话管理的关键优势

### 项目连续性
- 在多次对话中无缝的工作延续
- Claude Code 会话之间无上下文丢失
- 保留的架构决策和技术理由
- 长期项目演进跟踪

### 提升生产力
- 减少重新解释项目上下文的需要
- 继续工作的更快启动时间
- 基于先前的洞察和模式进行构建
- 累积的项目知识增长

### 质量一致性
- 跨会话的一致架构模式
- 保留的代码质量决策和标准
- 可重用的解决方案和最佳实践
- 维持的技术债务意识

---

**关键要点**：通过 Serena MCP 的会话管理将 SuperClaude 从单次对话帮助转变为持久项目伙伴关系，在所有开发阶段和 Claude Code 对话中维护上下文、决策和学习。
// Learn more about <PERSON><PERSON> commands at https://tauri.app/develop/calling-rust/

// Declare modules
pub mod checkpoint;
pub mod claude_binary;
pub mod commands;
pub mod process;
pub mod framework_loader;

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .invoke_handler(tauri::generate_handler![
            // Agent commands
            commands::agents::list_agents,
            commands::agents::create_agent,
            commands::agents::update_agent,
            commands::agents::delete_agent,
            commands::agents::get_agent,
            commands::agents::list_agent_runs,
            commands::agents::get_agent_run,
            commands::agents::get_agent_run_with_real_time_metrics,
            commands::agents::list_agent_runs_with_metrics,
            commands::agents::execute_agent,
            commands::agents::list_running_sessions,
            commands::agents::kill_agent_session,
            commands::agents::get_session_status,
            commands::agents::cleanup_finished_processes,
            commands::agents::get_live_session_output,
            commands::agents::get_session_output,
            commands::agents::stream_session_output,
            commands::agents::export_agent,
            commands::agents::export_agent_to_file,
            commands::agents::get_claude_binary_path,
            commands::agents::set_claude_binary_path,
            commands::agents::list_claude_installations,
            commands::agents::import_agent,
            commands::agents::import_agent_from_file,
            
            // Business Panel commands
            commands::business_panel::list_business_experts,
            commands::business_panel::create_business_panel_session,
            commands::business_panel::execute_business_analysis,
            commands::business_panel::get_business_panel_results,
            
            // Claude commands
            commands::claude::execute_claude_code,
            commands::claude::cancel_claude_execution,
            commands::claude::get_claude_session_output,
            
            // MCP commands
            commands::mcp::mcp_add,
            commands::mcp::mcp_list,
            commands::mcp::mcp_get,
            commands::mcp::mcp_remove,
            commands::mcp::mcp_add_json,
            commands::mcp::mcp_add_from_claude_desktop,
            commands::mcp::mcp_serve,
            commands::mcp::mcp_test_connection,
            commands::mcp::mcp_reset_project_choices,
            commands::mcp::mcp_get_server_status,
            commands::mcp::mcp_read_project_config,
            commands::mcp::mcp_save_project_config,
            
            // Usage commands
            commands::usage::get_usage_stats,
            
            // Storage commands
            commands::storage::storage_list_tables,
            commands::storage::storage_read_table,
            commands::storage::storage_update_row,
            commands::storage::storage_delete_row,
            commands::storage::storage_insert_row,
            commands::storage::storage_execute_sql,
            commands::storage::storage_reset_database,
            
            // Slash Commands
            commands::slash_commands::slash_commands_list,
            
            // Proxy commands
            commands::proxy::save_proxy_settings,
            commands::proxy::get_proxy_settings,
            commands::proxy::test_proxy_connection,
            
            // SuperClaude commands
            commands::superclaude::execute_superclaude_command,
            commands::superclaude::process_superclaude_prompt,
            commands::superclaude::get_superclaude_config,
            commands::superclaude::update_superclaude_state,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

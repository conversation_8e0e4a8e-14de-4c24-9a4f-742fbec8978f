use anyhow::{Context, Result};
use log::{debug, info, warn};
use regex::Regex;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use std::path::PathBuf;

/// SuperClaude Framework components loaded from markdown files
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SuperClaudeFramework {
    pub commands: HashMap<String, CommandDefinition>,
    pub modes: HashMap<String, ModeDefinition>,
    pub personas: HashMap<String, PersonaDefinition>,
    pub flags: HashMap<String, FlagDefinition>,
    pub mcp_servers: HashMap<String, MCPServerDefinition>,
    pub rules: Vec<Rule>,
    pub principles: Vec<Principle>,
    pub version: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CommandDefinition {
    pub name: String,
    pub category: String,
    pub purpose: String,
    pub wave_enabled: bool,
    pub performance_profile: String,
    pub auto_personas: Vec<String>,
    pub mcp_integration: Vec<String>,
    pub tool_orchestration: Vec<String>,
    pub arguments: Option<String>,
    pub flags: Vec<String>,
    pub description: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModeDefinition {
    pub name: String,
    pub purpose: String,
    pub activation_triggers: Vec<String>,
    pub behavioral_changes: Vec<String>,
    pub outcomes: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PersonaDefinition {
    pub name: String,
    pub identity: String,
    pub priority_hierarchy: Vec<String>,
    pub core_principles: Vec<String>,
    pub mcp_preferences: MCPPreferences,
    pub auto_activation_triggers: Vec<String>,
    pub quality_standards: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MCPPreferences {
    pub primary: Vec<String>,
    pub secondary: Vec<String>,
    pub avoided: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FlagDefinition {
    pub name: String,
    pub trigger: String,
    pub behavior: String,
    pub category: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MCPServerDefinition {
    pub name: String,
    pub purpose: String,
    pub activation_patterns: Vec<String>,
    pub workflow_process: Vec<String>,
    pub error_recovery: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Rule {
    pub priority: String,
    pub triggers: Vec<String>,
    pub content: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Principle {
    pub category: String,
    pub content: String,
}

/// Framework loader that reads and parses SuperClaude markdown files
pub struct FrameworkLoader {
    framework_path: PathBuf,
}

impl FrameworkLoader {
    pub fn new(framework_path: PathBuf) -> Self {
        Self { framework_path }
    }

    /// Load the complete SuperClaude framework from markdown files
    pub fn load_framework(&self) -> Result<SuperClaudeFramework> {
        info!("Loading SuperClaude framework from: {:?}", self.framework_path);

        let mut framework = SuperClaudeFramework {
            commands: HashMap::new(),
            modes: HashMap::new(),
            personas: HashMap::new(),
            flags: HashMap::new(),
            mcp_servers: HashMap::new(),
            rules: Vec::new(),
            principles: Vec::new(),
            version: "4.0.8".to_string(),
        };

        // Load each component
        self.load_commands(&mut framework)?;
        self.load_modes(&mut framework)?;
        self.load_personas(&mut framework)?;
        self.load_flags(&mut framework)?;
        self.load_mcp_servers(&mut framework)?;
        self.load_rules(&mut framework)?;
        self.load_principles(&mut framework)?;

        info!("Successfully loaded SuperClaude framework with {} commands, {} modes, {} personas",
              framework.commands.len(), framework.modes.len(), framework.personas.len());

        Ok(framework)
    }

    /// Load command definitions from COMMANDS.md
    fn load_commands(&self, framework: &mut SuperClaudeFramework) -> Result<()> {
        let commands_path = self.framework_path.join("COMMANDS.md");
        if !commands_path.exists() {
            warn!("COMMANDS.md not found at {:?}", commands_path);
            return Ok(());
        }

        let content = fs::read_to_string(&commands_path)
            .context("Failed to read COMMANDS.md")?;

        // Parse command definitions using regex
        let command_regex = Regex::new(r"(?m)^\*\*`/(\w+)[^`]*`\*\*\n```yaml\n([\s\S]*?)```")?;
        
        for cap in command_regex.captures_iter(&content) {
            let name = cap[1].to_string();
            let yaml_content = &cap[2];
            
            // Parse YAML-like content
            let mut command = CommandDefinition {
                name: name.clone(),
                category: self.extract_yaml_field(yaml_content, "category").unwrap_or_default(),
                purpose: self.extract_yaml_field(yaml_content, "purpose").unwrap_or_default(),
                wave_enabled: self.extract_yaml_field(yaml_content, "wave-enabled")
                    .unwrap_or_default() == "true",
                performance_profile: self.extract_yaml_field(yaml_content, "performance-profile")
                    .unwrap_or_else(|| "standard".to_string()),
                auto_personas: Vec::new(),
                mcp_integration: Vec::new(),
                tool_orchestration: Vec::new(),
                arguments: self.extract_yaml_field(yaml_content, "arguments"),
                flags: Vec::new(),
                description: self.extract_yaml_field(yaml_content, "purpose").unwrap_or_default(),
            };

            // Extract additional details from markdown
            if let Some(details) = self.extract_command_details(&content, &name) {
                command.auto_personas = details.0;
                command.mcp_integration = details.1;
                command.tool_orchestration = details.2;
            }

            framework.commands.insert(name, command);
        }

        // Add additional commands from the detailed command list
        self.add_detailed_commands(framework, &content);

        debug!("Loaded {} commands", framework.commands.len());
        Ok(())
    }

    /// Extract command details from markdown content
    fn extract_command_details(&self, content: &str, command_name: &str) -> Option<(Vec<String>, Vec<String>, Vec<String>)> {
        // Look for command details in the format:
        // - **Auto-Persona**: Frontend, Backend, Architect
        // - **MCP Integration**: Magic, Context7, Sequential
        // - **Tool Orchestration**: [Read, Write, Edit, MultiEdit]
        
        let command_section_regex = Regex::new(&format!(
            r"(?m)^\*\*`/{}`.*?\n([\s\S]*?)(?:^\*\*`/|\z)",
            regex::escape(command_name)
        )).ok()?;
        
        let cap = command_section_regex.captures(content)?;
        let section = &cap[1];
        
        let mut personas = Vec::new();
        let mut mcp_servers = Vec::new();
        let mut tools = Vec::new();
        
        // Extract Auto-Persona
        if let Ok(persona_regex) = Regex::new(r"(?m)^\s*-\s*\*\*Auto-Persona\*\*:\s*(.+)$") {
            if let Some(cap) = persona_regex.captures(section) {
                personas = cap[1].split(',')
                    .map(|s| s.trim().to_lowercase())
                    .collect();
            }
        }
        
        // Extract MCP Integration
        if let Ok(mcp_regex) = Regex::new(r"(?m)^\s*-\s*\*\*MCP Integration\*\*:\s*(.+)$") {
            if let Some(cap) = mcp_regex.captures(section) {
                mcp_servers = cap[1].split(',')
                    .map(|s| s.trim().to_lowercase())
                    .collect();
            }
        }
        
        // Extract Tool Orchestration
        if let Ok(tool_regex) = Regex::new(r"(?m)^\s*-\s*\*\*Tool Orchestration\*\*:\s*\[([^\]]+)\]") {
            if let Some(cap) = tool_regex.captures(section) {
                tools = cap[1].split(',')
                    .map(|s| s.trim().to_string())
                    .collect();
            }
        }
        
        Some((personas, mcp_servers, tools))
    }

    /// Add detailed commands from the command list
    fn add_detailed_commands(&self, framework: &mut SuperClaudeFramework, _content: &str) {
        // Parse the detailed command list section
        let commands_list = vec![
            ("analyze", "analysis", "Multi-dimensional code and system analysis", true, vec!["analyzer", "architect", "security"]),
            ("build", "development", "Project builder with framework detection", true, vec!["frontend", "backend", "architect", "scribe"]),
            ("implement", "development", "Feature and code implementation", true, vec!["frontend", "backend", "architect", "security"]),
            ("improve", "quality", "Evidence-based code enhancement", true, vec!["refactorer", "performance", "architect", "qa"]),
            ("troubleshoot", "analysis", "Problem investigation", false, vec!["analyzer", "qa"]),
            ("explain", "analysis", "Educational explanations", false, vec!["mentor", "scribe"]),
            ("cleanup", "quality", "Project cleanup and technical debt reduction", false, vec!["refactorer"]),
            ("document", "documentation", "Documentation generation", false, vec!["scribe", "mentor"]),
            ("estimate", "planning", "Evidence-based estimation", false, vec!["analyzer", "architect"]),
            ("task", "planning", "Long-term project management", true, vec!["architect", "analyzer"]),
            ("test", "testing", "Testing workflows", false, vec!["qa"]),
            ("git", "development", "Git workflow assistant", false, vec!["devops", "scribe", "qa"]),
            ("design", "planning", "Design orchestration", true, vec!["architect", "frontend"]),
            ("workflow", "planning", "Workflow design and optimization", true, vec!["architect", "devops"]),
            ("refactor", "quality", "Code restructuring", false, vec!["refactorer", "architect"]),
            ("optimize", "quality", "Performance optimization", false, vec!["performance", "backend"]),
            ("debug", "analysis", "Step-by-step debugging", false, vec!["analyzer", "qa"]),
            ("review", "quality", "Code review", false, vec!["qa", "security", "architect"]),
            ("index", "meta", "Command catalog browsing", false, vec!["mentor", "analyzer"]),
            ("load", "meta", "Project context loading", false, vec!["analyzer", "architect", "scribe"]),
            ("spawn", "meta", "Task orchestration", false, vec!["analyzer", "architect", "devops"]),
            ("business-panel", "analysis", "Business expert panel analysis", false, vec!["architect", "mentor"]),
        ];

        for (name, category, description, wave_enabled, personas) in commands_list {
            framework.commands.entry(name.to_string()).or_insert(CommandDefinition {
                name: name.to_string(),
                category: category.to_string(),
                purpose: description.to_string(),
                wave_enabled,
                performance_profile: if wave_enabled { "complex".to_string() } else { "standard".to_string() },
                auto_personas: personas.iter().map(|s| s.to_string()).collect(),
                mcp_integration: vec![],
                tool_orchestration: vec![],
                arguments: Some("[arguments]".to_string()),
                flags: vec![],
                description: description.to_string(),
            });
        }
    }

    /// Load mode definitions from MODE_*.md files
    fn load_modes(&self, framework: &mut SuperClaudeFramework) -> Result<()> {
        let mode_files = vec![
            ("MODE_Brainstorming.md", "brainstorming"),
            ("MODE_Business_Panel.md", "business-panel"),
            ("MODE_Introspection.md", "introspection"),
            ("MODE_Orchestration.md", "orchestration"),
            ("MODE_Task_Management.md", "task-management"),
            ("MODE_Token_Efficiency.md", "token-efficiency"),
        ];

        for (filename, mode_name) in mode_files {
            let mode_path = self.framework_path.join(filename);
            if !mode_path.exists() {
                debug!("{} not found, skipping", filename);
                continue;
            }

            let content = fs::read_to_string(&mode_path)
                .context(format!("Failed to read {}", filename))?;

            let mode = self.parse_mode_file(&content, mode_name);
            framework.modes.insert(mode_name.to_string(), mode);
        }

        // Add normal mode
        framework.modes.insert("normal".to_string(), ModeDefinition {
            name: "normal".to_string(),
            purpose: "Standard Claude behavior".to_string(),
            activation_triggers: vec!["default".to_string()],
            behavioral_changes: vec!["Standard responses".to_string()],
            outcomes: vec!["Direct answers".to_string()],
        });

        debug!("Loaded {} modes", framework.modes.len());
        Ok(())
    }

    /// Parse a mode file
    fn parse_mode_file(&self, content: &str, mode_name: &str) -> ModeDefinition {
        let mut mode = ModeDefinition {
            name: mode_name.to_string(),
            purpose: String::new(),
            activation_triggers: Vec::new(),
            behavioral_changes: Vec::new(),
            outcomes: Vec::new(),
        };

        // Extract purpose
        if let Ok(purpose_regex) = Regex::new(r"(?m)^\*\*Purpose\*\*:\s*(.+)$") {
            if let Some(cap) = purpose_regex.captures(content) {
                mode.purpose = cap[1].to_string();
            }
        }

        // Extract activation triggers
        if let Some(triggers_section) = self.extract_section(content, "Activation Triggers") {
            mode.activation_triggers = triggers_section.lines()
                .filter(|line| line.starts_with("- "))
                .map(|line| line[2..].trim().to_string())
                .collect();
        }

        // Extract behavioral changes
        if let Some(changes_section) = self.extract_section(content, "Behavioral Changes") {
            mode.behavioral_changes = changes_section.lines()
                .filter(|line| line.starts_with("- "))
                .map(|line| line[2..].trim().to_string())
                .collect();
        }

        // Extract outcomes
        if let Some(outcomes_section) = self.extract_section(content, "Outcomes") {
            mode.outcomes = outcomes_section.lines()
                .filter(|line| line.starts_with("- "))
                .map(|line| line[2..].trim().to_string())
                .collect();
        }

        mode
    }

    /// Load persona definitions from PERSONAS.md
    fn load_personas(&self, framework: &mut SuperClaudeFramework) -> Result<()> {
        let personas_path = self.framework_path.join("PERSONAS.md");
        if !personas_path.exists() {
            warn!("PERSONAS.md not found");
            return Ok(());
        }

        let content = fs::read_to_string(&personas_path)
            .context("Failed to read PERSONAS.md")?;

        // Parse each persona section
        let persona_names = vec![
            "architect", "frontend", "backend", "security", "performance",
            "analyzer", "qa", "refactorer", "devops", "mentor", "scribe"
        ];

        for name in persona_names {
            if let Some(persona) = self.parse_persona(&content, name) {
                framework.personas.insert(name.to_string(), persona);
            }
        }

        debug!("Loaded {} personas", framework.personas.len());
        Ok(())
    }

    /// Parse a persona from the content
    fn parse_persona(&self, content: &str, persona_name: &str) -> Option<PersonaDefinition> {
        let section_regex = Regex::new(&format!(
            r"(?m)^##\s+`--persona-{}`[\s\S]*?(?=^##\s+`--persona-|\z)",
            regex::escape(persona_name)
        )).ok()?;

        let cap = section_regex.captures(content)?;
        let section = &cap[0];

        let mut persona = PersonaDefinition {
            name: persona_name.to_string(),
            identity: String::new(),
            priority_hierarchy: Vec::new(),
            core_principles: Vec::new(),
            mcp_preferences: MCPPreferences {
                primary: Vec::new(),
                secondary: Vec::new(),
                avoided: Vec::new(),
            },
            auto_activation_triggers: Vec::new(),
            quality_standards: Vec::new(),
        };

        // Extract identity
        if let Ok(identity_regex) = Regex::new(r"(?m)^\*\*Identity\*\*:\s*(.+)$") {
            if let Some(cap) = identity_regex.captures(section) {
                persona.identity = cap[1].to_string();
            }
        }

        // Extract priority hierarchy
        if let Ok(priority_regex) = Regex::new(r"(?m)^\*\*Priority Hierarchy\*\*:\s*(.+)$") {
            if let Some(cap) = priority_regex.captures(section) {
                persona.priority_hierarchy = cap[1].split('>')
                    .map(|s| s.trim().to_string())
                    .collect();
            }
        }

        // Extract MCP preferences
        if let Ok(mcp_regex) = Regex::new(r"(?m)^\s*-\s*\*\*(Primary|Secondary|Avoided)\*\*:\s*(.+)$") {
            for cap in mcp_regex.captures_iter(section) {
                let servers: Vec<String> = cap[2].split('-')
                    .map(|s| s.trim().to_lowercase())
                    .filter(|s| !s.is_empty() && !s.contains("for"))
                    .collect();
                
                match &cap[1] {
                    "Primary" => persona.mcp_preferences.primary = servers,
                    "Secondary" => persona.mcp_preferences.secondary = servers,
                    "Avoided" => persona.mcp_preferences.avoided = servers,
                    _ => {}
                }
            }
        }

        Some(persona)
    }

    /// Load flag definitions from FLAGS.md
    fn load_flags(&self, framework: &mut SuperClaudeFramework) -> Result<()> {
        let flags_path = self.framework_path.join("FLAGS.md");
        if !flags_path.exists() {
            warn!("FLAGS.md not found");
            return Ok(());
        }

        let content = fs::read_to_string(&flags_path)
            .context("Failed to read FLAGS.md")?;

        // Parse flag definitions
        let flag_regex = Regex::new(r"(?m)^\*\*--([^*]+)\*\*\n-\s*Trigger:\s*(.+)\n-\s*Behavior:\s*(.+)")?;
        
        for cap in flag_regex.captures_iter(&content) {
            let name = cap[1].trim().to_string();
            let flag = FlagDefinition {
                name: name.clone(),
                trigger: cap[2].trim().to_string(),
                behavior: cap[3].trim().to_string(),
                category: self.categorize_flag(&name),
            };
            
            framework.flags.insert(name, flag);
        }

        debug!("Loaded {} flags", framework.flags.len());
        Ok(())
    }

    /// Load MCP server definitions from MCP.md
    fn load_mcp_servers(&self, framework: &mut SuperClaudeFramework) -> Result<()> {
        let mcp_path = self.framework_path.join("MCP.md");
        if !mcp_path.exists() {
            warn!("MCP.md not found");
            return Ok(());
        }

        let content = fs::read_to_string(&mcp_path)
            .context("Failed to read MCP.md")?;

        // Parse MCP server definitions from markdown
        let server_regex = Regex::new(r"(?m)^##\s+`([^`]+)`\s*\n\*\*Purpose\*\*:\s*(.+?)\s*\n([\s\S]*?)(?:^##|\z)")?;
        
        for cap in server_regex.captures_iter(&content) {
            let name = cap[1].trim().to_lowercase();
            let purpose = cap[2].trim().to_string();
            
            // Extract activation patterns and workflow process
            let mut activation_patterns = Vec::new();
            let mut workflow_process = Vec::new();
            
            // Look for activation patterns section
            if let Ok(pattern_regex) = Regex::new(r"(?m)^\*\*Activation Patterns\*\*:\s*([^\n]+)") {
                if let Some(pattern_cap) = pattern_regex.captures(&cap[3]) {
                    let patterns_str = &pattern_cap[1];
                    activation_patterns = patterns_str.split(',')
                        .map(|s| s.trim().to_string())
                        .filter(|s| !s.is_empty())
                        .collect();
                }
            }
            
            // Look for workflow process section
            if let Ok(workflow_regex) = Regex::new(r"(?m)^\*\*Workflow Process\*\*:\s*([^\n]+)") {
                if let Some(workflow_cap) = workflow_regex.captures(&cap[3]) {
                    let workflow_str = &workflow_cap[1];
                    workflow_process = workflow_str.split(',')
                        .map(|s| s.trim().to_string())
                        .filter(|s| !s.is_empty())
                        .collect();
                }
            }
            
            let server = MCPServerDefinition {
                name: name.clone(),
                purpose,
                activation_patterns,
                workflow_process,
                error_recovery: "Fallback to native tools".to_string(),
            };
            
            framework.mcp_servers.insert(name, server);
        }

        // Define default MCP servers if none were parsed
        if framework.mcp_servers.is_empty() {
            let servers = vec![
                ("context7", "Documentation & Research"),
                ("sequential", "Complex Analysis & Thinking"),
                ("magic", "UI Components & Design"),
                ("playwright", "Browser Automation & Testing"),
                ("morphllm", "Bulk Code Transformations"),
                ("serena", "Symbol Operations & Memory"),
            ];

            for (name, purpose) in servers {
                let server = MCPServerDefinition {
                    name: name.to_string(),
                    purpose: purpose.to_string(),
                    activation_patterns: Vec::new(),
                    workflow_process: Vec::new(),
                    error_recovery: "Fallback to native tools".to_string(),
                };
                
                framework.mcp_servers.insert(name.to_string(), server);
            }
        }

        debug!("Loaded {} MCP servers", framework.mcp_servers.len());
        Ok(())
    }

    /// Load rules from RULES.md
    fn load_rules(&self, framework: &mut SuperClaudeFramework) -> Result<()> {
        let rules_path = self.framework_path.join("RULES.md");
        if !rules_path.exists() {
            warn!("RULES.md not found");
            return Ok(());
        }

        let content = fs::read_to_string(&rules_path)
            .context("Failed to read RULES.md")?;

        // Parse rules by priority
        let priority_regex = Regex::new(r"(?m)^\*\*Priority\*\*:\s*(🔴|🟡|🟢)")?;
        let rule_regex = Regex::new(r"(?m)^-\s*\*\*([^*]+)\*\*:\s*(.+)$")?;
        
        let mut current_priority = "NORMAL";
        
        for line in content.lines() {
            if let Some(cap) = priority_regex.captures(line) {
                current_priority = match &cap[1] {
                    "🔴" => "CRITICAL",
                    "🟡" => "IMPORTANT",
                    "🟢" => "RECOMMENDED",
                    _ => "NORMAL",
                };
            } else if let Some(cap) = rule_regex.captures(line) {
                let rule = Rule {
                    priority: current_priority.to_string(),
                    triggers: vec![cap[1].trim().to_string()],
                    content: cap[2].trim().to_string(),
                };
                framework.rules.push(rule);
            }
        }

        debug!("Loaded {} rules", framework.rules.len());
        Ok(())
    }

    /// Load principles from PRINCIPLES.md
    fn load_principles(&self, framework: &mut SuperClaudeFramework) -> Result<()> {
        let principles_path = self.framework_path.join("PRINCIPLES.md");
        if !principles_path.exists() {
            warn!("PRINCIPLES.md not found");
            return Ok(());
        }

        let content = fs::read_to_string(&principles_path)
            .context("Failed to read PRINCIPLES.md")?;

        // Parse principles by category
        let category_regex = Regex::new(r"(?m)^##\s+(.+)$")?;
        let principle_regex = Regex::new(r"(?m)^-\s*\*\*([^*]+)\*\*:\s*(.+)$")?;
        
        let mut current_category = "General".to_string();
        
        for line in content.lines() {
            if let Some(cap) = category_regex.captures(line) {
                current_category = cap[1].to_string();
            } else if let Some(cap) = principle_regex.captures(line) {
                let principle = Principle {
                    category: current_category.clone(),
                    content: format!("{}: {}", cap[1].trim(), cap[2].trim()),
                };
                framework.principles.push(principle);
            }
        }

        debug!("Loaded {} principles", framework.principles.len());
        Ok(())
    }

    // Helper methods

    fn extract_yaml_field(&self, yaml: &str, field: &str) -> Option<String> {
        let regex = Regex::new(&format!(r#"(?m)^{}: "?([^"\n]+)"?"#, regex::escape(field))).ok()?;
        regex.captures(yaml).map(|cap| cap[1].to_string())
    }

    fn extract_section(&self, content: &str, heading: &str) -> Option<String> {
        let regex = Regex::new(&format!(
            r"(?m)^##\s+{}[\s\S]*?\n([\s\S]*?)(?:^##|\z)",
            regex::escape(heading)
        )).ok()?;
        
        regex.captures(content).map(|cap| cap[1].to_string())
    }

    fn categorize_flag(&self, flag_name: &str) -> String {
        if flag_name.contains("think") || flag_name.contains("analysis") {
            "analysis".to_string()
        } else if flag_name.contains("mcp") || flag_name.starts_with("c7") || flag_name.starts_with("seq") {
            "mcp".to_string()
        } else if flag_name.contains("delegate") || flag_name.contains("concurrency") {
            "execution".to_string()
        } else if flag_name.contains("uc") || flag_name.contains("scope") {
            "output".to_string()
        } else {
            "mode".to_string()
        }
    }
}
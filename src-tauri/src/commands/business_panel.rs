use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager};
use crate::process::ProcessRegistryState;
use crate::commands::agents::AgentDb;

/// Business panel expert definition
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct BusinessExpert {
    pub id: String,
    pub name: String,
    pub framework: String,
    pub focus_areas: Vec<String>,
    pub communication_style: String,
    pub symbol: String,
}

/// Business analysis result
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct BusinessAnalysis {
    pub expert_id: String,
    pub key_insights: Vec<String>,
    pub recommendations: Vec<String>,
    pub questions: Option<Vec<String>>,
    pub symbol: String,
}

/// Business panel session
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct BusinessPanelSession {
    pub id: String,
    pub experts: Vec<String>,
    pub content: String,
    pub mode: String,
    pub analyses: Vec<BusinessAnalysis>,
}

// In-memory storage for business panel sessions
lazy_static::lazy_static! {
    static ref BUSINESS_PANEL_SESSIONS: Arc<Mutex<HashMap<String, BusinessPanelSession>>> = 
        Arc::new(Mutex::new(HashMap::new()));
}

/// List all available business panel experts
#[tauri::command]
pub async fn list_business_experts() -> Result<Vec<BusinessExpert>, String> {
    let experts = vec![
        BusinessExpert {
            id: "christensen".to_string(),
            name: "Clayton Christensen".to_string(),
            framework: "Disruption Theory".to_string(),
            focus_areas: vec!["innovation".to_string(), "jobs-to-be-done".to_string()],
            communication_style: "academic, methodical".to_string(),
            symbol: "🔨".to_string(),
        },
        BusinessExpert {
            id: "porter".to_string(),
            name: "Michael Porter".to_string(),
            framework: "Five Forces".to_string(),
            focus_areas: vec!["strategy".to_string(), "competition".to_string()],
            communication_style: "analytical, data-driven".to_string(),
            symbol: "⚔️".to_string(),
        },
        BusinessExpert {
            id: "drucker".to_string(),
            name: "Peter Drucker".to_string(),
            framework: "Management by Objectives".to_string(),
            focus_areas: vec!["management".to_string(), "effectiveness".to_string()],
            communication_style: "wise, fundamental".to_string(),
            symbol: "🧭".to_string(),
        },
    ];
    
    Ok(experts)
}

/// Create a business panel analysis session
#[tauri::command]
pub async fn create_business_panel_session(
    app: AppHandle,
    db: tauri::State<'_, AgentDb>,
    content: String,
    mode: Option<String>,
    selected_experts: Option<Vec<String>>,
) -> Result<BusinessPanelSession, String> {
    let experts = selected_experts.unwrap_or(vec!["christensen".to_string(), "porter".to_string()]);
    let mode = mode.unwrap_or("discussion".to_string());
    
    let session_id = format!("session-{}", std::time::SystemTime::now()
        .duration_since(std::time::UNIX_EPOCH)
        .map_err(|_| "System time error")?
        .as_millis());
    
    let session = BusinessPanelSession {
        id: session_id.clone(),
        experts: experts.clone(),
        content: content.clone(),
        mode: mode.clone(),
        analyses: vec![],
    };

    // Store session in memory
    {
        let mut sessions = BUSINESS_PANEL_SESSIONS.lock().map_err(|_| "Failed to lock sessions")?;
        sessions.insert(session_id.clone(), session.clone());
    }

    // Save to database
    {
        let conn = db.0.lock().map_err(|e| e.to_string())?;
        crate::commands::agents::create_business_panel_session_db(&conn, &session_id, &experts, &content, &mode)
            .map_err(|e| format!("Failed to save business panel session to database: {}", e))?;
    }

    // Register with process registry
    let registry = app.state::<ProcessRegistryState>().0.clone();
    let _run_id = registry.register_business_panel_session(
        session_id.clone(),
        experts,
        "business-panel".to_string(), // project_path
        "Business Panel Analysis".to_string(), // task
        "sonnet".to_string(), // model
    ).map_err(|e| format!("Failed to register business panel session: {}", e))?;

    Ok(session)
}

/// Execute business panel analysis
#[tauri::command]
pub async fn execute_business_analysis(
    app: AppHandle,
    db: tauri::State<'_, AgentDb>,
    session_id: String,
) -> Result<Vec<BusinessAnalysis>, String> {
    // Get session
    let session = {
        let sessions = BUSINESS_PANEL_SESSIONS.lock().map_err(|_| "Failed to lock sessions")?;
        sessions.get(&session_id).cloned().ok_or_else(|| "Session not found".to_string())?
    };

    // Generate analysis results
    let analyses: Vec<BusinessAnalysis> = session.experts.iter().map(|expert_id| {
        let (symbol, name) = match expert_id.as_str() {
            "christensen" => ("🔨", "Clayton Christensen"),
            "porter" => ("⚔️", "Michael Porter"),
            "drucker" => ("🧭", "Peter Drucker"),
            _ => ("📊", "Generic Expert"),
        };

        BusinessAnalysis {
            expert_id: expert_id.clone(),
            key_insights: vec![
                format!("{} {} identified key patterns", symbol, name),
                format!("{} {} found opportunities for improvement", symbol, name),
            ],
            recommendations: vec![
                format!("Apply {} framework principles", match expert_id.as_str() {
                    "christensen" => "Disruption Theory",
                    "porter" => "Five Forces",
                    "drucker" => "Management by Objectives",
                    _ => "relevant"
                }).to_string(),
                "Consider strategic implications".to_string(),
            ],
            questions: Some(vec![
                format!("What are the {} implications?", match expert_id.as_str() {
                    "christensen" => "disruption",
                    "porter" => "competitive",
                    "drucker" => "management",
                    _ => "strategic"
                }).to_string(),
                "How can we improve outcomes?".to_string(),
            ]),
            symbol: symbol.to_string(),
        }
    }).collect();

    // Update session with results
    {
        let mut sessions = BUSINESS_PANEL_SESSIONS.lock().map_err(|_| "Failed to lock sessions")?;
        if let Some(session) = sessions.get_mut(&session_id) {
            session.analyses = analyses.clone();
        }
    }

    // Save results to database
    {
        let conn = db.0.lock().map_err(|e| e.to_string())?;
        for analysis in &analyses {
            crate::commands::agents::save_business_analysis_db(&conn, &session_id, analysis)
                .map_err(|e| format!("Failed to save business analysis to database: {}", e))?;
        }
        crate::commands::agents::complete_business_panel_session_db(&conn, &session_id)
            .map_err(|e| format!("Failed to complete business panel session in database: {}", e))?;
    }

    // Mark session as completed in process registry
    let registry = app.state::<ProcessRegistryState>().0.clone();
    if let Ok(Some(process_info)) = registry.get_business_panel_session_by_id(&session_id) {
        let _ = registry.complete_business_panel_session(process_info.run_id);
    }

    Ok(analyses)
}

/// Get latest business panel analysis results
#[tauri::command]
pub async fn get_business_panel_results(
    session_id: String,
) -> Result<Vec<BusinessAnalysis>, String> {
    let sessions = BUSINESS_PANEL_SESSIONS.lock().map_err(|_| "Failed to lock sessions")?;
    let session = sessions.get(&session_id).ok_or_else(|| "Session not found".to_string())?;
    Ok(session.analyses.clone())
}
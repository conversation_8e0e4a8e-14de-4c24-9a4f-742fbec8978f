use anyhow::Result;
use log::{debug, error, info, warn};
use serde::{Deserialize, Serialize};
use serde_json::json;
use std::sync::{Arc, Mutex};
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager};
use crate::framework_loader::{FrameworkLoader, SuperClaudeFramework};
use std::path::PathBuf;
use lazy_static::lazy_static;

// Global framework instance
lazy_static! {
    static ref FRAMEWORK: Arc<Mutex<Option<SuperClaudeFramework>>> = Arc::new(Mutex::new(None));
}

/// SuperClaude command configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SuperClaudeCommand {
    pub name: String,
    pub description: String,
    pub category: String,
    pub wave_enabled: bool,
    pub performance_profile: String,
    pub auto_personas: Vec<String>,
    pub mcp_servers: Vec<String>,
    pub arguments: Option<String>,
    pub flags: Option<Vec<String>>,
    pub examples: Option<Vec<String>>,
    pub routing_info: CommandRoutingInfo,
}

/// Command routing information for dynamic execution
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CommandRoutingInfo {
    pub execution_type: String, // "direct", "wave", "agent", "mcp"
    pub target_component: String, // Component name to route to
    pub required_capabilities: Vec<String>, // Required MCP servers or capabilities
    pub timeout_ms: u64, // Execution timeout in milliseconds
}

/// SuperClaude session state
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SuperClaudeSessionState {
    pub active_mode: String,
    pub active_personas: Vec<String>,
    pub active_wave_strategy: Option<String>,
    pub current_wave: Option<u32>,
    pub token_efficiency_enabled: bool,
    pub business_panel_active: bool,
    pub active_experts: Option<Vec<String>>,
    pub mcp_servers_active: Vec<String>,
}

/// SuperClaude configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SuperClaudeConfig {
    pub commands: Vec<SuperClaudeCommand>,
    pub modes: Vec<String>,
    pub personas: Vec<String>,
    pub business_panel_experts: Vec<String>,
    pub mcp_servers: Vec<String>,
    pub routing_table: std::collections::HashMap<String, CommandRoutingInfo>,
}

/// Execute a SuperClaude command
#[tauri::command]
pub async fn execute_superclaude_command(
    app: AppHandle,
    project_path: String,
    command: String,
    arguments: String,
    flags: Vec<String>,
    model: String,
) -> Result<(), String> {
    info!(
        "Executing SuperClaude command: {} with args: {} and flags: {:?}",
        command, arguments, flags
    );

    // Get routing information for this command
    let routing_info = {
        let framework_guard = (*FRAMEWORK).lock().map_err(|e| {
            error!("Failed to lock framework: {}", e);
            "Failed to access framework".to_string()
        })?;
        
        if let Some(framework) = framework_guard.as_ref() {
            if let Some(cmd) = framework.commands.get(&command) {
                // Create routing info based on command properties
                let execution_type = if cmd.wave_enabled {
                    "wave".to_string()
                } else if !cmd.mcp_integration.is_empty() {
                    "mcp".to_string()
                } else {
                    "direct".to_string()
                };
                
                let target_component = if cmd.wave_enabled {
                    "wave_orchestrator".to_string()
                } else if !cmd.mcp_integration.is_empty() {
                    cmd.mcp_integration.first().cloned().unwrap_or("claude".to_string())
                } else {
                    "claude".to_string()
                };
                
                Some(CommandRoutingInfo {
                    execution_type,
                    target_component,
                    required_capabilities: cmd.mcp_integration.clone(),
                    timeout_ms: 30000,
                })
            } else {
                None
            }
        } else {
            None
        }
    };

    // Build the enhanced prompt with SuperClaude context
    let mut enhanced_prompt = format!("/sc:{}", command);
    
    if !arguments.is_empty() {
        enhanced_prompt.push_str(&format!(" {}", arguments));
    }
    
    for flag in &flags {
        enhanced_prompt.push_str(&format!(" --{}", flag));
    }

    // Add SuperClaude metadata to the prompt
    let metadata = json!({
        "superclaude": true,
        "command": command,
        "arguments": arguments,
        "flags": flags,
        "framework_version": "4.0.8",
        "routing_info": routing_info
    });

    debug!("Enhanced prompt: {}", enhanced_prompt);
    debug!("SuperClaude metadata: {:?}", metadata);

    // Use the existing Claude execution infrastructure
    // This will route through the existing execute_claude_code command
    // Note: MCP servers will be passed from frontend based on SuperClaude command analysis
    crate::commands::claude::execute_claude_code(
        app,
        project_path,
        enhanced_prompt,
        model,
        None, // MCP servers are handled by the frontend based on command parsing
    )
    .await
}

/// Process a prompt with SuperClaude enhancements
#[tauri::command]
pub async fn process_superclaude_prompt(
    _app: AppHandle,
    prompt: String,
    _project_path: String,
) -> Result<String, String> {
    info!("Processing prompt with SuperClaude enhancements");

    // Use framework-aware enhancement
    let enhanced = enhance_prompt_with_framework(&prompt);
    
    if enhanced != prompt {
        debug!("Enhanced prompt: {}", enhanced);
        Ok(enhanced)
    } else {
        // Fallback to simple mode detection if framework not loaded
        let mode = detect_mode(&prompt);
        if mode != "normal" {
            Ok(format!("[Mode: {}] {}", mode, prompt))
        } else {
            Ok(prompt)
        }
    }
}

/// Detect mode from prompt content
fn detect_mode(prompt: &str) -> String {
    let lower = prompt.to_lowercase();
    
    if lower.contains("brainstorm") || lower.contains("explore") || lower.contains("not sure") {
        "brainstorming".to_string()
    } else if lower.contains("analyze") && lower.contains("reasoning") {
        "introspection".to_string()
    } else if lower.contains("task") || lower.contains("todo") || lower.contains("plan") {
        "task-management".to_string()
    } else if lower.contains("optimize") || lower.contains("parallel") {
        "orchestration".to_string()
    } else if lower.contains("compress") || lower.contains("--uc") {
        "token-efficiency".to_string()
    } else if lower.contains("business") || lower.contains("strategic") {
        "business-panel".to_string()
    } else {
        "normal".to_string()
    }
}

/// Get SuperClaude configuration
#[tauri::command]
pub async fn get_superclaude_config(_app: AppHandle) -> Result<SuperClaudeConfig, String> {
    info!("Getting SuperClaude configuration from framework");

    // Get framework instance
    let framework_guard = (*FRAMEWORK).lock().map_err(|e| {
        error!("Failed to lock framework: {}", e);
        "Failed to access framework".to_string()
    })?;

    if let Some(framework) = framework_guard.as_ref() {
        // Convert framework to config
        let commands: Vec<SuperClaudeCommand> = framework.commands.values()
            .map(|cmd| {
                // Create routing info based on command properties
                let execution_type = if cmd.wave_enabled {
                    "wave".to_string()
                } else if !cmd.mcp_integration.is_empty() {
                    "mcp".to_string()
                } else {
                    "direct".to_string()
                };
                
                let target_component = if cmd.wave_enabled {
                    "wave_orchestrator".to_string()
                } else if !cmd.mcp_integration.is_empty() {
                    cmd.mcp_integration.first().cloned().unwrap_or("claude".to_string())
                } else {
                    "claude".to_string()
                };
                
                SuperClaudeCommand {
                    name: cmd.name.clone(),
                    description: cmd.description.clone(),
                    category: cmd.category.clone(),
                    wave_enabled: cmd.wave_enabled,
                    performance_profile: cmd.performance_profile.clone(),
                    auto_personas: cmd.auto_personas.clone(),
                    mcp_servers: cmd.mcp_integration.clone(),
                    arguments: cmd.arguments.clone(),
                    flags: Some(cmd.flags.clone()),
                    examples: None,
                    routing_info: CommandRoutingInfo {
                        execution_type,
                        target_component,
                        required_capabilities: cmd.mcp_integration.clone(),
                        timeout_ms: 30000, // Default 30 second timeout
                    },
                }
            })
            .collect();

        // Build routing table
        let mut routing_table = std::collections::HashMap::new();
        for cmd in &commands {
            routing_table.insert(cmd.name.clone(), cmd.routing_info.clone());
        }

        let config = SuperClaudeConfig {
            commands,
            modes: framework.modes.keys().cloned().collect(),
            personas: framework.personas.keys().cloned().collect(),
            business_panel_experts: vec![
                "christensen".to_string(),
                "porter".to_string(),
                "drucker".to_string(),
                "godin".to_string(),
                "kim_mauborgne".to_string(),
                "collins".to_string(),
                "taleb".to_string(),
                "meadows".to_string(),
                "doumont".to_string(),
            ],
            mcp_servers: framework.mcp_servers.keys().cloned().collect(),
            routing_table,
        };

        Ok(config)
    } else {
        // Return default if framework not loaded
        warn!("Framework not loaded, returning default configuration");
        Ok(get_default_config())
    }
}

/// Get default configuration
fn get_default_config() -> SuperClaudeConfig {
    SuperClaudeConfig {
        commands: vec![],
        modes: vec!["normal".to_string()],
        personas: vec![],
        business_panel_experts: vec![],
        mcp_servers: vec![],
        routing_table: std::collections::HashMap::new(),
    }
}

/// Update SuperClaude session state
#[tauri::command]
pub async fn update_superclaude_state(
    _app: AppHandle,
    session_id: String,
    state: SuperClaudeSessionState,
) -> Result<bool, String> {
    info!("Updating SuperClaude state for session: {}", session_id);
    debug!("New state: {:?}", state);

    // In a real implementation, this would persist the state
    // For now, we just log it and return success
    
    Ok(true)
}

/// Initialize SuperClaude framework
pub fn init_superclaude(app: &AppHandle) -> Result<()> {
    info!("Initializing SuperClaude framework");

    // Check if SuperClaude framework files exist
    let app_data_dir = app.path().app_data_dir().map_err(|e| {
        error!("Failed to get app data directory: {}", e);
        anyhow::anyhow!("Failed to get app data directory: {}", e)
    })?;

    let superclaude_dir = app_data_dir.join("superclaude");
    
    // Create SuperClaude directory if it doesn't exist
    if !superclaude_dir.exists() {
        std::fs::create_dir_all(&superclaude_dir).map_err(|e| {
            error!("Failed to create SuperClaude directory: {}", e);
            anyhow::anyhow!("Failed to create SuperClaude directory: {}", e)
        })?;
        
        info!("Created SuperClaude directory at: {:?}", superclaude_dir);
    }

    // Initialize configuration files if needed
    let config_file = superclaude_dir.join("config.json");
    if !config_file.exists() {
        let default_config = json!({
            "version": "4.0.8",
            "enabled": true,
            "default_mode": "normal",
            "token_efficiency": false,
            "auto_persona_detection": true,
        });

        std::fs::write(&config_file, serde_json::to_string_pretty(&default_config)?)?;
        info!("Created default SuperClaude configuration");
    }

    // Load the framework from markdown files
    let framework_path = PathBuf::from("SuperClaude_Framework-master");
    if framework_path.exists() {
        info!("Loading SuperClaude framework from {:?}", framework_path);
        let loader = FrameworkLoader::new(framework_path);
        
        match loader.load_framework() {
            Ok(framework) => {
                info!("Successfully loaded SuperClaude framework with {} commands, {} modes, {} personas",
                      framework.commands.len(), framework.modes.len(), framework.personas.len());
                
                // Store framework in global state
                let mut framework_guard = (*FRAMEWORK).lock().map_err(|e| {
                    error!("Failed to lock framework: {}", e);
                    anyhow::anyhow!("Failed to lock framework: {}", e)
                })?;
                *framework_guard = Some(framework);
            }
            Err(e) => {
                error!("Failed to load SuperClaude framework: {}", e);
                warn!("Continuing with default configuration");
            }
        }
    } else {
        warn!("SuperClaude framework directory not found at {:?}", framework_path);
    }

    Ok(())
}

/// Get loaded framework
pub fn get_framework() -> Option<SuperClaudeFramework> {
    (*FRAMEWORK).lock().ok()?.clone()
}

/// Process prompt with enhanced SuperClaude context
pub fn enhance_prompt_with_framework(prompt: &str) -> String {
    let framework_guard = (*FRAMEWORK).lock().ok();
    if let Some(Some(framework)) = framework_guard.as_deref() {
        // Check if this is a SuperClaude command
        if prompt.starts_with("/sc:") {
            let command_name = prompt[4..].split_whitespace().next().unwrap_or("");
            
            if let Some(command) = framework.commands.get(command_name) {
                let mut enhanced = format!("[SuperClaude Command: {}]", command_name);
                
                // Add personas
                if !command.auto_personas.is_empty() {
                    enhanced.push_str(&format!(" [Personas: {}]", command.auto_personas.join(", ")));
                }
                
                // Add MCP servers
                if !command.mcp_integration.is_empty() {
                    enhanced.push_str(&format!(" [MCP: {}]", command.mcp_integration.join(", ")));
                }
                
                // Add wave status
                if command.wave_enabled {
                    enhanced.push_str(" [Wave-Enabled]");
                }
                
                enhanced.push_str(&format!(" {}", prompt));
                return enhanced;
            }
        }
        
        // Detect mode from content
        for (mode_name, mode) in &framework.modes {
            for trigger in &mode.activation_triggers {
                if prompt.to_lowercase().contains(&trigger.to_lowercase()) {
                    return format!("[Mode: {}] {}", mode_name, prompt);
                }
            }
        }
    }
    
    prompt.to_string()
}